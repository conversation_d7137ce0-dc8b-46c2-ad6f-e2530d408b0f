{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from termcolor import colored\n", "\n", "from base.datasets.tenants import DOGFOOD_SHARD\n", "from base.prompt_format_retrieve.prompt_formatter import ChatRetrieverPromptInput\n", "from base.prompt_format_chat.prompt_formatter import ChatPromptInput\n", "\n", "from experimental.michiel.research.agentqa.retrieval import BlobGetter, create_retriever\n", "from experimental.michiel.research.agentqa.agent import (\n", "    <PERSON><PERSON><PERSON>,\n", "    ModelCall,\n", "    ToolCallEntry,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["request_id = \"4382ba44-1484-4936-a839-f74fbc7e2fe9\"\n", "\n", "blob_getter = BlobGetter(DOGFOOD_SHARD)\n", "documents = blob_getter.get_blobs_from_request_id(request_id)\n", "doc_ids = {doc.id for doc in documents}"]}, {"cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# load retriever\n", "retriever_config = {\n", "    \"scorer\": {\n", "        \"name\": \"dense_scorer_v2_ffwd\",\n", "        \"checkpoint_path\": \"/mnt/efs/augment/user/igor/checkpoints/chatanol/chatanol1-18.hybrid/neox/global_step1468\",\n", "        # \"cache_dir\": \"/mnt/efs/augment/user/michiel/misc/cache/chatagent_cache\"\n", "    },\n", "    \"chunker\": {\n", "        \"name\": \"smart_line_level\",\n", "        \"max_chunk_chars\": 768,\n", "        \"max_headers\": 3,\n", "    },\n", "    \"query_formatter\": {\n", "        \"name\": \"base:chatanol6-singleturnisspecial\",\n", "        \"tokenizer_name\": \"rogue\",\n", "        \"max_tokens\": 1024,\n", "    },\n", "    \"document_formatter\": {\n", "        \"name\": \"base:ethanol6-embedding-with-path-key\",\n", "        \"tokenizer_name\": \"rogue\",\n", "    },\n", "}\n", "retriever = create_retriever(retriever_config)\n", "retriever.load()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["retriever.add_docs(documents)\n", "# retriever.add_docs(documents[:5])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def update_docs_new_request(\n", "    request_id: str,\n", "):\n", "    new_documents = blob_getter.get_blobs_from_request_id(request_id)\n", "    new_doc_ids = {doc.id for doc in new_documents}\n", "    new_docs = [doc for doc in new_documents if doc.id not in doc_ids]\n", "    old_docs_ids = [doc.id for doc in documents if doc.id not in new_doc_ids]\n", "    print(f\"Removing {len(old_docs_ids)} old docs.\")\n", "    retriever.remove_docs(old_docs_ids)\n", "    print(f\"Adding {len(new_docs)} new docs.\")\n", "    retriever.add_docs(new_docs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from experimental.michiel.research.agentqa.agent import (\n", "    TopLevelQuestionAnsweringAgent,\n", "    ProduceAnswerAgent,\n", "    JudgeAnswerAgent,\n", "    InformationAgent,\n", ")\n", "from research.llm_apis.llm_client import AnthropicDirectClient\n", "from experimental.michiel.research.agentqa.tools import (\n", "    CodebaseRetrievalTool,\n", "    DirectorySubTreeTool,\n", "    JudgeAnswerTool,\n", "    ToolSelectionTool,\n", ")\n", "\n", "\n", "def answer_expensive_agent(chat_input: ChatPromptInput, request_docs: list):\n", "    model_name = \"claude-3-5-sonnet-20241022\"\n", "    # model_name = \"claude-3-5-sonnet@20240620\"\n", "\n", "    client = AnthropicDirectClient(model_name=model_name)\n", "\n", "    produce_answer_agent = ProduceAnswerAgent(model_client=client)\n", "    judge_answer_tool = JudgeAnswerTool()\n", "    judge_answer_agent = JudgeAnswerAgent(\n", "        model_client=client, judge_answer_tool=judge_answer_tool\n", "    )\n", "    codebase_retrieval_tool = CodebaseRetrievalTool(\n", "        retriever=retriever, max_tool_chars=20000\n", "    )\n", "    directory_subtree_tool = DirectorySubTreeTool(\n", "        request_docs, max_directory_chars=10000, max_file_chars=10000\n", "    )\n", "    tool_selection_tool = ToolSelectionTool()\n", "    information_agent = InformationAgent(\n", "        model_client=client,\n", "        codebase_retrieval_tool=codebase_retrieval_tool,\n", "        directory_subtree_tool=directory_subtree_tool,\n", "        tool_selection_tool=tool_selection_tool,\n", "    )\n", "\n", "    agent = TopLevelQuestionAnsweringAgent(\n", "        max_turns=10,\n", "        produce_answer_agent=produce_answer_agent,\n", "        judge_answer_agent=judge_answer_agent,\n", "        information_agent=information_agent,\n", "    )\n", "\n", "    null_agent_log = AgentLog()\n", "    return agent(null_agent_log, chat_input), null_agent_log\n", "\n", "\n", "def answer_baseline_agent(chat_input: ChatPromptInput, request_docs: list):\n", "    model_name = \"claude-3-5-sonnet-20241022\"\n", "    # model_name = \"claude-3-5-sonnet@20240620\"\n", "\n", "    client = AnthropicDirectClient(model_name=model_name)\n", "\n", "    produce_answer_agent = ProduceAnswerAgent(model_client=client)\n", "    judge_answer_tool = JudgeAnswerTool()\n", "    judge_answer_agent = JudgeAnswerAgent(\n", "        model_client=client, judge_answer_tool=judge_answer_tool\n", "    )\n", "    codebase_retrieval_tool = CodebaseRetrievalTool(\n", "        retriever=retriever, max_tool_chars=20000\n", "    )\n", "    directory_subtree_tool = DirectorySubTreeTool(\n", "        request_docs, max_directory_chars=10000, max_file_chars=10000\n", "    )\n", "    tool_selection_tool = ToolSelectionTool()\n", "    information_agent = InformationAgent(\n", "        model_client=client,\n", "        codebase_retrieval_tool=codebase_retrieval_tool,\n", "        directory_subtree_tool=directory_subtree_tool,\n", "        tool_selection_tool=tool_selection_tool,\n", "    )\n", "\n", "    agent = TopLevelQuestionAnsweringAgent(\n", "        max_turns=1,\n", "        produce_answer_agent=produce_answer_agent,\n", "        judge_answer_agent=judge_answer_agent,\n", "        information_agent=information_agent,\n", "    )\n", "\n", "    null_agent_log = AgentLog()\n", "    return agent(null_agent_log, chat_input), null_agent_log"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["##### Succeeds where base chat fails (or is meaningfully worse)\n", "\n", "# question = \"where does binks prompt formatter merge overlapping chunks?\"\n", "# question = \"list all registered model names in `research/models/fastforward_models.py`\"\n", "# question = \"which methods in utils_for_str.py are not covered by unit tests\"\n", "# question = \"How can I set multiple evals for the fastbackward training with custom names?\"\n", "\n", "# General reasoning is correct, but model confuses empty completion with empty list of completions\n", "# question = \"Do quality filtered completions show up in the history panel?\"\n", "\n", "# question = \"I want to modify a chunker to skip documents from the docset service. How do I do that?\"\n", "\n", "\n", "##### Fails where base chat succeeds (or is meaningfully better)\n", "\n", "# Failure - model doesn't discover flags.yaml\n", "# question = \"How do I change the default completion model in production?\"\n", "\n", "##### Both fail\n", "\n", "# Failure: ignores 'base' part\n", "# question = \"where is `suggested_prefix_char_count` in base\"\n", "\n", "# Failure: looks through part of code, confidently suggests it's nowhere. Cha<PERSON>l doesn't get it even if asked literally and restricted to base\n", "# question = \"where is `suggested_prefix_char_count` in base directory\"\n", "\n", "# Failure: gets confused by research code\n", "# question = \"How is the next edit output length propagated from the deploy config to the edit server\"\n", "\n", "# Failure: doesn't find eldenv3, gets confused with research, still answers confidently\n", "# question = \"Where is the base prompt formatter for eldenv3?\"\n", "\n", "# Failure: finds fastbackward instead\n", "# question = \"Does fastforward model in research use all available GPUs? Show exact lines.\"\n", "\n", "# Failure: doesn't call tool to look through entire directory. Agent does better than chat though.\n", "# question = \"which deployment configs are using neural speculative model?\"\n", "\n", "\n", "##### Both succeed\n", "\n", "# question = \"where is tutorial for str_diff\"\n", "# question = \"How do I serialize a python dataclass\"\n", "# question = \"Where is the default shortcut for accepting a code instruction set?\"\n", "# question = \"Where is eldenv3 configured?\"\n", "# question = \"Where is the prompt formatter for the eldenv5 model?\"\n", "# question = \"Do quality filtered completions show up in request insight?\"\n", "# question = \"How do I change the cluster when running evaluation jobs?\"\n", "\n", "# Agent has a more complete answer\n", "# question = \"How does the find-missing api decide which blobs are missing or unindexed?\"\n"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["# load docset\n", "import json\n", "\n", "docset_path = (\n", "    \"/home/<USER>/augment/services/integrations/docset/server/docsets_dump.json\"\n", ")\n", "with open(docset_path) as f:\n", "    docset = json.load(f)\n", "\n", "formatted_docset_list = \"\"\n", "for entry in docset:\n", "    name = entry[\"docset_id\"].split(\"://\")[1]\n", "    formatted_docset_list += f\"{name}\\n\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(formatted_docset_list)"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["# Experiment with different clients\n", "\n", "from pathlib import Path\n", "from research.llm_apis.llm_client import (\n", "    QwenFastForwardClient,\n", ")\n", "from research.models.all_models import get_model\n", "from research.models import fastforward_llama_models\n", "\n", "sonnet_name = \"claude-3-5-sonnet-20241022\"\n", "haiku_name = \"claude-3-5-haiku-20241022\"\n", "sonnet_client = AnthropicDirectClient(model_name=sonnet_name)\n", "haiku_client = AnthropicDirectClient(model_name=haiku_name)\n", "\n", "# model = get_model(\n", "#     \"fastforward_qwen25coder_14b\",\n", "#     checkpoint_path=Path(\"/mnt/efs/augment/checkpoints/qwen25-coder/14b-instruct-ffw\"),\n", "#     checkpoint_sha256=\"4c30e35231d7a1c0db0a21e37c5503d35ea5694fc325b0074195c5761f7fc010\",\n", "# )\n", "\n", "model = get_model(\n", "    \"fastforward_qwen25coder_32b\",\n", "    checkpoint_path=Path(\"/mnt/efs/augment/checkpoints/qwen25-coder/32b-instruct-ffw\"),\n", "    checkpoint_sha256=\"4c30e35231d7a1c0db0a21e37c5503d35ea5694fc325b0074195c5761f7fc010\",\n", ")\n", "model.load()\n", "\n", "qwen_client = QwenFastForwardClient(\n", "    model=model,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Experiment with router tool\n", "from typing import cast\n", "from experimental.michiel.research.agentqa.tools import Tool\n", "from experimental.michiel.research.agentqa.agent import (\n", "    AgentFunction,\n", "    format_chat_input,\n", "    ChatPromptInput,\n", "    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "    TextPrompt,\n", ")\n", "from research.llm_apis.llm_client import (\n", "    LLMClient,\n", "    <PERSON><PERSON><PERSON><PERSON>,\n", ")\n", "from textwrap import dedent\n", "\n", "\n", "class General<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(Tool):\n", "    name = \"general_query_router\"\n", "\n", "    tool_description = dedent(\n", "        \"\"\"\\\n", "        Call this tool to classify a user query along a number of dimensions.\n", "        \"\"\"\n", "    )\n", "\n", "    is_general_question_description = dedent(\n", "        \"\"\"\\\n", "        Whether the question is a general programming question, as opposed to a question that relates to a specific codebase.\n", "        Be conservative and assume the question is specific to a codebase unless you are certain it is not.\n", "        \"\"\"\n", "    )\n", "\n", "    about_specific_file_description = dedent(\n", "        \"\"\"\\\n", "        If the query primarily requires information in a specific, single file, please specify the path to that file. Otherwise, just write none.\n", "        Some examples where the file should be specified:\n", "        - The user asks for the contents of a file\n", "        - The user asks to write tests for a class in a specific file\n", "        - The user asks to summarize a file\n", "\n", "        Some examples where the file should not be specified:\n", "        - The user asks for the contents of a directory\n", "        - The user doesn't mention any files\n", "        - The user mentions a file, but it's clear the question is about the codebase as a whole.\n", "        - The user has the file open, but the question is not related to that file.\n", "        \"\"\"\n", "    )\n", "\n", "    external_package_description = dedent(\n", "        f\"\"\"\\\n", "        If the query is about an external package (e.g. Python, JS, numpy), please specify the name of the package from the list below. Otherwise, just write none.\n", "        The list of packages is: \\n{formatted_docset_list}\n", "        \"\"\"\n", "    )\n", "\n", "    has_sufficient_context_description = dedent(\n", "        \"\"\"\\\n", "        Whether the question can be answered with the context provided in the input, chat history and retrieved code.\n", "\n", "        Set to true only if you are CONFIDENT that the question can be answered with the context provided.\n", "        If you are not sure, set this to false.\n", "        \"\"\"\n", "    )\n", "\n", "    def get_input_schema(self):\n", "        return {\n", "            \"type\": \"object\",\n", "            \"properties\": {\n", "                \"is_general_question\": {\n", "                    \"type\": \"boolean\",\n", "                    \"description\": self.is_general_question_description,\n", "                },\n", "                \"about_specific_file\": {\n", "                    \"type\": \"string\",\n", "                    \"description\": self.about_specific_file_description,\n", "                },\n", "                \"external_package\": {\n", "                    \"type\": \"string\",\n", "                    \"description\": self.external_package_description,\n", "                },\n", "                \"has_sufficient_context\": {\n", "                    \"type\": \"boolean\",\n", "                    \"description\": self.has_sufficient_context_description,\n", "                },\n", "            },\n", "            \"required\": [\n", "                \"is_general_question\",\n", "                \"about_specific_file\",\n", "                \"external_package\",\n", "                \"has_sufficient_context\",\n", "            ],\n", "        }\n", "\n", "\n", "class RouterAgent(AgentFunction):\n", "    def __init__(\n", "        self,\n", "        router_tool: Tool,\n", "        router_client: LLMClient,\n", "        retriever,\n", "        chat_history_len: int = 2000,\n", "        max_retrieval_chars: int = 10000,\n", "    ):\n", "        self.router_tool = router_tool\n", "        self.router_client = router_client\n", "        self.retriever = retriever\n", "        self.chat_history_len = chat_history_len\n", "        self.max_retrieval_chars = max_retrieval_chars\n", "\n", "    def __call__(self, agent_log: AgentLog, chat_input: ChatPromptInput) -> bool:\n", "        current_agent_log = AgentLog(name=\"RouterAgent\")\n", "        agent_log.register_child(current_agent_log)\n", "        current_agent_log.relevant_inputs = {\n", "            \"chat_input\": chat_input,\n", "        }\n", "\n", "        messages = self.format_router_prompt(chat_input)\n", "\n", "        call_result, call_metadata = self.router_client.generate(\n", "            messages=messages,\n", "            max_tokens=2048,\n", "            tools=[self.router_tool.get_tool_param()],\n", "            tool_choice={\"type\": \"tool\", \"name\": self.router_tool.name},\n", "        )\n", "        model_call = ModelCall(\n", "            model_input=messages,\n", "            model_response=call_result,\n", "        )\n", "        current_agent_log.register_model_call(model_call)\n", "        assert len(call_result) == 1, f\"Expected one message but got {len(call_result)}\"\n", "        assert str(type(call_result[0])) == str(\n", "            ToolCall\n", "        ), f\"Expected tool call but got: {type(call_result[0])}\"\n", "        tool_call = cast(<PERSON><PERSON><PERSON><PERSON>, call_result[0])\n", "        return tool_call.tool_input\n", "\n", "    def format_router_prompt(\n", "        self, chat_input: ChatPromptInput\n", "    ) -> list[list[General<PERSON><PERSON><PERSON><PERSON><PERSON>]]:\n", "        \"\"\"Format the prompt for the router agent function.\"\"\"\n", "        formatted_chat_input = format_chat_input(\n", "            chat_input,\n", "            chat_history_len=self.chat_history_len,\n", "        )\n", "\n", "        router_prompt = dedent(\n", "            \"\"\"\\\n", "            You are a tool-calling agent that classifies the nature of a query.\n", "            \"\"\"\n", "        )\n", "\n", "        user_message_string = \"\"\n", "        user_message_string += router_prompt\n", "        user_message_string += f\"The user query is:\\n{formatted_chat_input}\"\n", "        if chat_input.path and not chat_input.selected_code:\n", "            user_message_string += f\"Current file is: {chat_input.path}\\n\"\n", "        if self.max_retrieval_chars > 0:\n", "            chunks, scores = self.retrieve(chat_input)\n", "            retrieval_chars = 0\n", "            user_message_string += \"Here are some code snippets retrieved in response to the query that may be relevant to classifying the query:\\n\"\n", "            for chunk in chunks:\n", "                if retrieval_chars + len(chunk.text) > self.max_retrieval_chars:\n", "                    break\n", "                retrieval_chars += len(chunk.text) + 1\n", "                user_message_string += f\"{chunk.text}\\n\"\n", "            user_message_string += (\n", "                f\"As a reminder, the user query is:\\n{formatted_chat_input}\"\n", "            )\n", "\n", "        messages = []\n", "        messages.append([TextPrompt(text=user_message_string)])\n", "        return messages\n", "\n", "    def retrieve(self, chat_input: ChatPromptInput):\n", "        query = ChatRetrieverPromptInput(\n", "            prefix=\"\",\n", "            suffix=\"\",\n", "            path=\"\",\n", "            message=chat_input.message,\n", "            selected_code=\"\",\n", "            chat_history=[],\n", "        )\n", "        chunks, scores = self.retriever.query(query, top_k=None)\n", "        return chunks, scores\n", "\n", "\n", "request_id = \"b14b0e44-bd60-4b19-956f-ecb576c4822c\"\n", "# chat_input = blob_getter.get_chat_request_without_retrievals(request_id)\n", "# print(\"question\", chat_input.message)\n", "\n", "# question = \"What were all the US presidents and when were they elected?\"\n", "# question = \"What is the difference between functions and classes in Python?\"\n", "\n", "\n", "# question = \"What determines the name of a kubernetes pod?\"\n", "\n", "\n", "# question = \"where do we do matrix multiplication in the production retrieval service.\"\n", "# question = \"How do I change the default completion model in production?\"\n", "# question = \"where is `suggested_prefix_char_count` in base\"\n", "# question = \"where does binks prompt formatter merge overlapping chunks?\"\n", "# question = \"How can I set multiple evals for the fastbackward training with custom names?\"\n", "# question = \"list all registered model names in `research/models/fastforward_models.py\"\n", "# question = \"list all registered model names in fastforward_models.py\"\n", "# question = \"Do quality filtered completions show up in the history panel?\"\n", "# question = \"I want to modify a chunker to skip documents from the docset service. How do I do that?\"\n", "# question = \"How can I set multiple evals for the fastbackward training with custom names?\"\n", "# question = \"Where is the base prompt formatter for eldenv3?\"\n", "# question = \"When using pythons .get() method on dictionaries, when does it return the default value?.\"\n", "# question = \"In grafana, why does it complain about too much data and refueses to show graphs?\"\n", "# question = \"Where is the load balancing strategy for our embedding indexers defined?\"\n", "# question = \"I got this error: n must be at least one\"\n", "# question = \"API Token not found\"\n", "# question = \"I got error: 'API Token not found'\"\n", "\n", "# question = \"which chat retrievers do we have deployed according to the metadata jsonnet file?\"\n", "# question = \"What does vscode client do with out of date files for a completion request?\"\n", "\n", "# question = \"What is a crate in rust\"\n", "\n", "\n", "# question = \"suggested_prefix_char_count in vscode client\"\n", "# question = \"Does fastforward model in research use all available GPUs? Show exact lines\"\n", "# question = \"How do I change the default completion model in production?\"\n", "# question = \"which deployment configs are using neural speculative model?\"\n", "# question = \"find all deployment configs using neural speculative model\"\n", "# question = \"where is tutorial for str_diff\"\n", "# question = \"is before_lrange or after_lrange or before_crange or after_crange of DiffHunk used anywhere currently?\"\n", "# question = \"Does adding directory to .augmentignore mean that we no longer retrieve from already uploaded files?\"\n", "# question = \"How can I add a toggle with label text 'workspace rules' to the bottom left corner of the chat input box in vscode?\"\n", "# question = \"Which tool could I use to generate a comparison report for AugmentQA?\"\n", "\n", "question = \"Please explain how to create a new directory with a minimal rust example, running with bazel.\"\n", "# question = \"Add tests\"\n", "\n", "chat_input = ChatPromptInput(\n", "    path=\"research/eval/harness/systems/basic_RAG_system.py\",\n", "    prefix=\"\",\n", "    selected_code=\"\",\n", "    suffix=\"\",\n", "    message=question,\n", "    chat_history=[],\n", "    prefix_begin=0,\n", "    suffix_end=0,\n", "    retrieved_chunks=[],\n", ")\n", "\n", "\n", "router_agent = RouterAgent(\n", "    router_tool=GeneralQueryRouter(),\n", "    router_client=sonnet_client,\n", "    # router_client=haiku_client,\n", "    retriever=retriever,\n", "    chat_history_len=2000,\n", "    max_retrieval_chars=10000,\n", ")\n", "null_agent_log = AgentLog()\n", "router_result = router_agent(null_agent_log, chat_input)\n", "print(\"router_result\", router_result)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test router\n", "\n", "from experimental.michiel.research.agentqa.tools import Tool\n", "\n", "\n", "class General<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(Tool):\n", "    name = \"general_query_router\"\n", "\n", "    tool_description = dedent(\n", "        \"\"\"\\\n", "        Call this tool to specify whether the question is a general programming question that can be answered without looking at the codebase, or a question specific to a particular codebase.\n", "        \"\"\"\n", "    )\n", "\n", "    is_general_question_description = dedent(\n", "        \"\"\"\\\n", "        Whether the question is a general programming question, as opposed to a question that relates to a specific codebase.\n", "        Be conservative and assume the question is specific to a codebase unless you are certain it is not.\n", "        \"\"\"\n", "    )\n", "\n", "    def get_input_schema(self):\n", "        return {\n", "            \"type\": \"object\",\n", "            \"properties\": {\n", "                \"is_general_question\": {\n", "                    \"type\": \"boolean\",\n", "                    \"description\": self.is_general_question_description,\n", "                },\n", "            },\n", "            \"required\": [\"is_general_question\"],\n", "        }"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from experimental.michiel.research.agentqa.fast_agent import (\n", "    FastQuestionAnsweringAgent,\n", "    FastInformationAgent,\n", "    InitialInformationAgent,\n", ")\n", "from experimental.michiel.research.agentqa.tools import (\n", "    QueryRouterTool,\n", ")\n", "\n", "# client = sonnet_client\n", "# client = haiku_client\n", "# client = qwen_client\n", "\n", "\n", "def answer_fast_agent(chat_input: ChatPromptInput, request_docs: list):\n", "    produce_answer_agent = ProduceAnswerAgent(model_client=sonnet_client)\n", "\n", "    codebase_retrieval_tool = CodebaseRetrievalTool(\n", "        retriever=retriever, max_tool_chars=10000\n", "    )\n", "    directory_subtree_tool = DirectorySubTreeTool(\n", "        request_docs, max_directory_chars=5000, max_file_chars=5000\n", "    )\n", "\n", "    initial_information_agent = InitialInformationAgent(\n", "        codebase_retrieval_tool=codebase_retrieval_tool,\n", "        directory_subtree_tool=directory_subtree_tool,\n", "    )\n", "\n", "    information_agent = FastInformationAgent(\n", "        codebase_retrieval_tool=codebase_retrieval_tool,\n", "        codebase_retrieval_client=qwen_client,\n", "        directory_subtree_tool=directory_subtree_tool,\n", "        directory_subtree_client=qwen_client,\n", "    )\n", "    router_tool = QueryRouterTool()\n", "\n", "    agent = FastQuestionAnsweringAgent(\n", "        max_turns=5,\n", "        router_tool=router_tool,\n", "        router_client=qwen_client,\n", "        produce_answer_agent=produce_answer_agent,\n", "        information_agent=information_agent,\n", "        initial_information_agent=initial_information_agent,\n", "    )\n", "\n", "    null_agent_log = AgentLog()\n", "    return agent(null_agent_log, chat_input), null_agent_log\n", "\n", "\n", "# question = \"What is the difference between functions and classes in Python?\"\n", "\n", "\n", "# question = \"What determines the name of a kubernetes pod?\"\n", "\n", "\n", "# question = \"where do we do matrix multiplication in the production retrieval service.\"\n", "# question = \"How do I change the default completion model in production?\"\n", "# question = \"where is `suggested_prefix_char_count` in base\"\n", "# question = \"where does binks prompt formatter merge overlapping chunks?\"\n", "# question = \"How can I set multiple evals for the fastbackward training with custom names?\"\n", "# question = \"list all registered model names in `research/models/fastforward_models.py\"\n", "# question = \"list all registered model names in fastforward_models.py\"\n", "# question = \"Do quality filtered completions show up in the history panel?\"\n", "# question = \"I want to modify a chunker to skip documents from the docset service. How do I do that?\"\n", "# question = \"How can I set multiple evals for the fastbackward training with custom names?\"\n", "# question = \"Where is the base prompt formatter for eldenv3?\"\n", "# question = \"When using pythons .get() method on dictionaries, when does it return the default value?.\"\n", "# question = \"Where is the load balancing strategy for our embedding indexers defined?\"\n", "# question = \"I got this error: n must be at least one\"\n", "question = \"API Token not found\"\n", "question = \"I got error: '<PERSON> Token not found'\"\n", "\n", "# question = \"which chat retrievers do we have deployed according to the metadata jsonnet file?\"\n", "# question = \"What does vscode client do with out of date files for a completion request?\"\n", "\n", "\n", "# question = \"suggested_prefix_char_count in vscode client\"\n", "# question = \"Does fastforward model in research use all available GPUs? Show exact lines\"\n", "# question = \"How do I change the default completion model in production?\"\n", "# question = \"which deployment configs are using neural speculative model?\"\n", "# question = \"find all deployment configs using neural speculative model\"\n", "# question = \"where is tutorial for str_diff\"\n", "# question = \"is before_lrange or after_lrange or before_crange or after_crange of DiffHunk used anywhere currently?\"\n", "# question = \"Does adding directory to .augmentignore mean that we no longer retrieve from already uploaded files?\"\n", "# question = \"How can I add a toggle with label text 'workspace rules' to the bottom left corner of the chat input box in vscode?\"\n", "# question = \"Which tool could I use to generate a comparison report for AugmentQA?\"\n", "\n", "# question = \"Please explain how to create a new directory with a minimal rust example, running with bazel.\"\n", "\n", "\n", "null_agent_log = AgentLog()\n", "chat_input = ChatPromptInput(\n", "    path=\"\",\n", "    prefix=\"\",\n", "    selected_code=\"\",\n", "    suffix=\"\",\n", "    message=question,\n", "    chat_history=[],\n", "    prefix_begin=0,\n", "    suffix_end=0,\n", "    retrieved_chunks=[],\n", ")\n", "\n", "current_request_id = \"4382ba44-1484-4936-a839-f74fbc7e2fe9\"\n", "update_docs_new_request(current_request_id)\n", "\n", "\n", "fast_answer, fast_null_log = answer_fast_agent(chat_input, documents)\n", "logs = fast_null_log.child_logs[0]\n", "\n", "# baseline_answer, baseline_null_log = answer_baseline_agent(chat_input, documents)\n", "# logs = baseline_null_log.child_logs[0]\n", "\n", "# expensive_answer, expensive_null_log = answer_expensive_agent(chat_input, documents)\n", "# logs = expensive_null_log.child_logs[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(logs.error_state[\"traceback\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def create_printp(prefix=\"\"):\n", "    def printp(*args, color=None):\n", "        text = \" \".join(map(str, args))\n", "        lines = text.splitlines()\n", "        for line in lines:\n", "            colored_line = colored(line, color) if color else line\n", "            print(prefix + colored_line)\n", "\n", "    return printp\n", "\n", "\n", "# Get full overview\n", "stack = [(logs, 0)]\n", "while stack:\n", "    current_event, depth = stack.pop()\n", "    prefix = \"   \" * depth\n", "    printp = create_printp(prefix)\n", "    type_str = str(type(current_event))\n", "    if type_str == str(AgentLog):\n", "        printp(f\"Agent call: {current_event.name}\", color=\"blue\")\n", "        for event in reversed(current_event.combined_history):\n", "            stack.append((event, depth + 1))\n", "    elif type_str == str(ModelCall):\n", "        printp(\"Model call\", color=\"yellow\")\n", "    elif type_str == str(ToolCallEntry):\n", "        tool_name = current_event.tool_call.tool_name\n", "        printp(f\"Tool call: {tool_name}\", color=\"green\")\n", "        if tool_name != \"judge_answer\":\n", "            printp(f\"Tool input: {current_event.tool_call.tool_input}\")\n", "    else:\n", "        raise ValueError(f\"Unknown type: {type(current_event)}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def create_printp(prefix=\"\"):\n", "    def printp(*args, color=None):\n", "        text = \" \".join(map(str, args))\n", "        lines = text.splitlines()\n", "        for line in lines:\n", "            colored_line = colored(line, color) if color else line\n", "            print(prefix + colored_line)\n", "\n", "    return printp\n", "\n", "\n", "# Get full overview\n", "stack = [(logs, 0)]\n", "while stack:\n", "    current_event, depth = stack.pop()\n", "    prefix = \"   \" * depth\n", "    printp = create_printp(prefix)\n", "    type_str = str(type(current_event))\n", "    if type_str == str(AgentLog):\n", "        printp(f\"Agent call: {current_event.name}\", color=\"blue\")\n", "        for event in reversed(current_event.combined_history):\n", "            stack.append((event, depth + 1))\n", "    elif type_str == str(ModelCall):\n", "        printp(\"Model call\", color=\"yellow\")\n", "    elif type_str == str(ToolCallEntry):\n", "        tool_name = current_event.tool_call.tool_name\n", "        printp(f\"Tool call: {tool_name}\", color=\"green\")\n", "        if tool_name != \"judge_answer\":\n", "            printp(f\"Tool input: {current_event.tool_call.tool_input}\")\n", "        printp(\n", "            f\"Formatted tool output: {current_event.formatted_tool_output.tool_output if current_event.formatted_tool_output else None}\"\n", "        )\n", "    else:\n", "        raise ValueError(f\"Unknown type: {type(current_event)}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from experimental.michiel.research.agentqa.question_agent_system import (\n", "    FastQuestionAgentSystem,\n", ")\n", "\n", "retriever_config = {\n", "    \"scorer\": {\n", "        \"name\": \"dense_scorer_v2_ffwd\",\n", "        \"checkpoint_path\": \"/mnt/efs/augment/user/igor/checkpoints/chatanol/chatanol1-18.hybrid/neox/global_step1468\",\n", "    },\n", "    \"chunker\": {\"name\": \"line_level\", \"max_lines_per_chunk\": 30},\n", "    \"query_formatter\": {\n", "        \"name\": \"base:chatanol6-singleturnisspecial\",\n", "        \"tokenizer_name\": \"rogue\",\n", "        \"max_tokens\": 1024,\n", "    },\n", "    \"document_formatter\": {\n", "        \"name\": \"base:chatanol6-embedding\",\n", "        \"tokenizer_name\": \"rogue\",\n", "    },\n", "}\n", "\n", "\n", "system_config = {\n", "    \"model_config\": {\n", "        \"fast\": {\n", "            \"checkpoint_path\": \"/mnt/efs/augment/checkpoints/qwen25-coder/32b-instruct-fb-mp8/\",\n", "            \"model_parallel_size\": 8,\n", "        },\n", "        \"slow\": {\n", "            \"model_name\": \"claude-3-5-sonnet-20241022\",\n", "        },\n", "    },\n", "    \"agent_config\": {\n", "        \"max_turns\": 1,\n", "    },\n", "    \"verbose\": True,\n", "    \"retriever_config\": retriever_config,\n", "}\n", "\n", "\n", "system = FastQuestionAgentSystem.from_yaml_config(system_config)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.prompt_format_chat.prompt_formatter import ChatPromptInput\n", "\n", "question = \"How does the find-missing api decide which blobs are missing or unindexed?\"\n", "\n", "chat_input = ChatPromptInput(\n", "    path=\"\",\n", "    prefix=\"\",\n", "    selected_code=\"\",\n", "    suffix=\"\",\n", "    message=question,\n", "    chat_history=[],\n", "    prefix_begin=0,\n", "    suffix_end=0,\n", "    retrieved_chunks=[],\n", ")\n", "\n", "result = system.generate(chat_input)\n", "\n", "print(result.generated_text)\n", "print(result.extra_output.additional_info[\"output_state\"][\"error_state\"][\"traceback\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from transformers import AutoTokenizer\n", "\n", "tokenizer = AutoTokenizer.from_pretrained(\n", "    pretrained_model_name_or_path=\"Qwen/Qwen2.5-Coder-32B-Instruct\"\n", ")\n", "messages = [\n", "    [\n", "        {\"role\": \"system\", \"content\": \"test system.\"},\n", "        {\"role\": \"user\", \"content\": \"test user.\"},\n", "        {\"role\": \"assistant\", \"content\": \"test assistant.\"},\n", "    ],\n", "]\n", "formatted_messages = tokenizer.apply_chat_template(\n", "    messages,\n", "    tokenize=False,\n", "    # add_generation_prompt=True,\n", "    # system_prompt=system_prompt or \"You are a helpful assistant.\",\n", ")\n", "\n", "print(formatted_messages)"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.llm_apis.llm_client import GeminiVertexClient\n", "\n", "\n", "gemini_client = GeminiVertexClient(model_name=\"gemini-2.0-flash-exp\")\n", "message = TextPrompt(text=\"What were all the US presidents and when were they elected?\")\n", "gemini_client.generate(\n", "    messages=[[message]],\n", "    max_tokens=4096,\n", "    system_prompt=\"You are a helpful assistant.\",\n", ")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
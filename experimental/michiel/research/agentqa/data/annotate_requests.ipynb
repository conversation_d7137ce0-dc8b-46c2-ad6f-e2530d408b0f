{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "from experimental.michiel.research.agentqa.tools import <PERSON><PERSON>, GeneralQueryRouter\n", "\n", "PATH = \"/home/<USER>/augment/services/integrations/docset/server/docsets_dump.json\"\n", "\n", "with open(PATH, \"r\") as f:\n", "    docsets_dict = json.load(f)\n", "\n", "\n", "DOCSETS = set()\n", "for docset in docsets_dict:\n", "    docset_id = docset[\"docset_id\"].split(\"://\")[1]\n", "    DOCSETS.add(docset_id)\n", "\n", "print(f\"Found {len(DOCSETS)} docsets: {list(DOCSETS)[:5]}, ...\")\n", "\n", "\n", "query_router_tool: Tool = GeneralQueryRouter(docsets=list(DOCSETS))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.prompt_format_chat.lib.string_formatter import StringFormatter\n", "from base.prompt_format_chat.lib.token_counter import TokenCounter\n", "\n", "\n", "QUERY_ROUTER_SYSTEM_PROMPT = f\"\"\"\\\n", "Your task is to analyze the last message in the conversation and classify it for routing purposes. Focus only on the last user message while using the full conversation history for context.\n", "\n", "Analyze the following aspects:\n", "1. Query Type: Classify the query into one of these categories:\n", "   - \"general\": General programming questions independent of any codebase\n", "   - \"current_file\": Questions about understanding the current file or selected code\n", "   - \"edit\": Requests to modify the current file or selected code\n", "   - \"overview\": Requests for architectural/high-level understanding of the project\n", "   - \"codebase\": Questions specific to the codebase (default if uncertain)\n", "2. Required Files: Does the query require information from specific files?\n", "3. External Packages: Is the query about an external package/library?\n", "\n", "Use the general_query_router tool to provide your classification. Before using the tool, explain your reasoning briefly, then make your classification.\n", "\n", "Remember:\n", "- Consider the full conversation context when interpreting the last message\n", "- Only specify files if the query primarily requires information from those specific files\n", "- For external packages, only specify if the query is directly about using that package\n", "- The external package MUST BE from the list of available packages: {list(DOCSETS)}. Otherwise, specify \"none\".\n", "\"\"\"\n", "\n", "\n", "def get_query_router_system_prompt(token_counter: TokenCounter) -> StringFormatter:\n", "    return StringFormatter(\n", "        QUERY_ROUTER_SYSTEM_PROMPT,\n", "        token_counter=token_counter,\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.prompt_format_chat.prompt_formatter import (\n", "    ChatTokenApportionment,\n", ")\n", "from base.prompt_format.common import Exchange, PromptChunk\n", "from base.prompt_format_chat.lib.token_counter_claude import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\n", "from base.prompt_format_chat.structured_binks_prompt_formatter import (\n", "    StructuredBinksPromptFormatter,\n", ")\n", "from research.llm_apis.llm_client import get_client, LLMClient\n", "\n", "TOKEN_APPORTIONMENT = ChatTokenApportionment(\n", "    prefix_len=1024 * 2,\n", "    suffix_len=1024 * 2,\n", "    path_len=256,\n", "    message_len=-1,  # Deprecated field\n", "    selected_code_len=-1,  # Deprecated field\n", "    chat_history_len=1024 * 4,\n", "    retrieval_len_per_each_user_guided_file=3000,\n", "    retrieval_len_for_user_guided=8000,\n", "    retrieval_len=-1,  # Fill the rest of the input prompt with retrievals\n", "    max_prompt_len=1024 * 12,  # 12k for prompt\n", "    inject_current_file_into_retrievals=True,\n", "    tool_results_len=1024 * 4,  # 4k for tool results\n", ")\n", "\n", "TOKEN_COUNTER = ClaudeTokenCounter()\n", "\n", "\n", "PROMPT_FORMATTER = StructuredBinksPromptFormatter.create(\n", "    TOKEN_COUNTER,\n", "    TOKEN_APPORTIONMENT,\n", "    system_prompt_factory=get_query_router_system_prompt,\n", ")\n", "\n", "ANTHROPIC_CLIENT: LLMClient = get_client(\n", "    \"anthropic-direct\",\n", "    model_name=\"claude-3-5-sonnet-20241022\",\n", "    max_retries=4,\n", "    use_caching=False,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import dataclasses\n", "from base.prompt_format_chat.prompt_formatter import (\n", "    StructuredChatPromptOutput,\n", ")\n", "from base.prompt_format_chat.structured_binks_prompt_formatter import (\n", "    StructuredBinksPromptFormatter,\n", ")\n", "from research.llm_apis.llm_client import (\n", "    <PERSON><PERSON><PERSON><PERSON>,\n", ")\n", "from experimental.michiel.research.agentqa.tools import Tool\n", "from research.core.chat_prompt_input import ResearchChatPromptInput\n", "from base.third_party_clients.anthropic_direct_client import get_tool_definition\n", "from research.llm_apis.prod_adapter_utils import (\n", "    convert_chat_prompt_output_to_messages,\n", "    convert_tool_definitions_to_tool_params,\n", ")\n", "\n", "\n", "class FailedToFindExternalPackage(Exception):\n", "    def __init__(self, predicted_docset: str, matched_docsets: list[str]):\n", "        self.message = f\"Failed to find external package for {predicted_docset}. Matched docsets: {matched_docsets}\"\n", "        super().__init__(self.message)\n", "\n", "\n", "@dataclasses.dataclass\n", "class QueryRouterResult:\n", "    category: str\n", "    about_specific_file: list[str]\n", "    external_package: str\n", "\n", "    @staticmethod\n", "    def find_external_package(predicted_docset):\n", "        if predicted_docset == \"none\" or predicted_docset in DOCSETS:\n", "            return predicted_docset\n", "        matched_docsets = []\n", "        for docset in DOCSETS:\n", "            if docset.startswith(predicted_docset + \"~\"):\n", "                matched_docsets.append(docset)\n", "        if len(matched_docsets) == 1:\n", "            return matched_docsets[0]\n", "        raise FailedToFindExternalPackage(predicted_docset, matched_docsets)\n", "\n", "    @classmethod\n", "    def from_tool_call(cls, tool_call: ToolCall):\n", "        category = tool_call.tool_input[\"category\"]\n", "        about_specific_file = tool_call.tool_input[\"about_specific_file\"]\n", "        external_package = tool_call.tool_input[\"external_package\"]\n", "        external_package = QueryRouterResult.find_external_package(external_package)\n", "        return cls(category, about_specific_file, external_package)\n", "\n", "\n", "def run_query_router_claude(\n", "    chat_prompt_input: ResearchChatPromptInput,\n", "    prompt_formatter: StructuredBinksPromptFormatter,\n", "    query_router_tool: Tool,\n", "    force_tool_call: bool = True,\n", "):\n", "    prompt_output: StructuredChatPromptOutput = prompt_formatter.format_prompt(\n", "        chat_prompt_input\n", "    )\n", "    messages, tool_params_from_history = convert_chat_prompt_output_to_messages(\n", "        prompt_output,\n", "        strict=False,  # Allow tools without definitions\n", "    )\n", "\n", "    tool_params = convert_tool_definitions_to_tool_params(\n", "        chat_prompt_input.tool_definitions,\n", "    )\n", "    tool_names = {tool_param.name for tool_param in tool_params}\n", "    for tool_param in tool_params_from_history:\n", "        if tool_param.name not in tool_names:\n", "            tool_params.append(tool_param)\n", "\n", "    kwargs = dict(\n", "        messages=messages,\n", "        max_tokens=2048,  # we don't expect long outputs for the query router\n", "        system_prompt=prompt_output.system_prompt,\n", "        temperature=0.0,\n", "        tools=(tool_params + [query_router_tool.get_tool_param()]),\n", "    )\n", "    if force_tool_call:\n", "        kwargs[\"tool_choice\"] = {\"type\": \"tool\", \"name\": query_router_tool.name}\n", "\n", "    response, response_metadata = ANTHROPIC_CLIENT.generate(**kwargs)\n", "    response = response[-1]\n", "    if not isinstance(response, ToolCall):\n", "        raise ValueError(\n", "            f\"Expected <PERSON><PERSON><PERSON><PERSON> response, got {type(response)}: {response}\"\n", "        )\n", "    response = QueryRouterResult.from_tool_call(response)\n", "    assert response.category in [\n", "        \"general\",\n", "        \"current_file\",\n", "        \"edit\",\n", "        \"overview\",\n", "        \"codebase\",\n", "    ]\n", "    return response"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.datasets.tenants import DOGFOOD_SHARD, VANGUARD\n", "from research.tools.chat_replay.replay_utils import BlobGetter\n", "from experimental.michiel.research.agentqa.tools import <PERSON><PERSON>, GeneralQueryRouter\n", "\n", "\n", "tenant = VANGUARD\n", "blob_getter = BlobGetter(tenant)\n", "query_router_tool = GeneralQueryRouter(docsets=list(DOCSETS))\n", "\n", "request_id = \"1273c233-c365-498e-a32f-166da3f44b23\"\n", "# request_id = \"1273c233-c365-498e-a32f-166da3f44b23\" # something went wrong???\n", "# request_id = \"06de17e0-206a-48b7-8f45-c3be985ad4ae\"\n", "# request_id = \"242c3eb0-b8a1-4513-ba13-0189cb96c37e\"  # Exceed context length\n", "# request_id = \"f6383ac3-f414-4ff0-9331-d3ac02a02e54\"  # model tends to predict \"openai\" without version\n", "# request_id = \"7e3208e5-1c29-48ab-ac63-4e00f3ee6fe9\"  # model predicts empty docset instead of \"none\"\n", "# request_id = \"eb638a4e-ae31-46fc-b72e-60c4c2bd96d8\"\n", "# request_id = \"496868c1-5cbe-4648-8371-aaa0f5a9c4a7\"  # has tool without definition\n", "# request_id = \"9c9a272e-02fd-4d94-88d5-feb5fbc6f8ec\"  # model typically predicts \"jest\" docset instead of \"jest~24.1.1\"\n", "chat_input = blob_getter.get_chat_request(request_id)\n", "prompt_output = PROMPT_FORMATTER.format_prompt(chat_input)\n", "# prompt_output.chat_history\n", "# convert_chat_prompt_input_to_messages(prompt_output)\n", "response = run_query_router_claude(chat_input, PROMPT_FORMATTER, query_router_tool)\n", "response\n", "# # prompt_output"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import contextlib\n", "import io\n", "import sys\n", "from IPython.utils.capture import capture_output\n", "\n", "\n", "@contextlib.contextmanager\n", "def suppress_output():\n", "    \"\"\"Suppress stdout and stderr within a context.\n", "\n", "    Usage:\n", "        with suppress_output():\n", "            print(\"This will not be visible\")\n", "    \"\"\"\n", "    with capture_output() as captured:\n", "        yield captured\n", "\n", "\n", "# Test it\n", "print(\"This should be visible\")\n", "with suppress_output():\n", "    print(\"This will not be visible\")\n", "print(\"This should be visible too\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from anthropic import BadRequestError\n", "from collections import defaultdict\n", "import tqdm\n", "import json\n", "import pyarrow\n", "import pyarrow.parquet\n", "from pathlib import Path\n", "from typing import Iterator, Iterable, Any\n", "\n", "from base.datasets.tenants import VANGUARD, VANGUARD_I1\n", "from research.tools.chat_replay.replay_utils import BlobGetter\n", "from experimental.michiel.research.agentqa.data.utils import (\n", "    maybe_run_in_multiple_threads,\n", ")\n", "from research.data.rag.retrieval_utils import deserialize_retrieved_chunks\n", "from base.prompt_format_chat.prompt_formatter import ExceedContextLength\n", "\n", "\n", "def iter_parquet_files(\n", "    paths: Iterable[Path], processed_request_ids: set[str]\n", ") -> Iterator[dict[str, Any]]:\n", "    for path in paths:\n", "        data = pyarrow.parquet.read_table(path)\n", "        df = data.to_pandas()\n", "        for _, row in df.iterrows():\n", "            row_dict = row.to_dict()\n", "            request_id = row_dict[\"request_id\"]\n", "            if request_id in processed_request_ids:\n", "                continue\n", "            yield row.to_dict()\n", "\n", "\n", "def read_processed_data(output_path: str) -> tuple[set[str], dict[str, int]]:\n", "    processed_request_ids = set()\n", "    stats = defaultdict(int)\n", "\n", "    try:\n", "        with open(output_path, \"r\") as f:\n", "            for line in f:\n", "                try:\n", "                    datum = json.loads(line)\n", "                    request_id = datum[\"request_id\"]\n", "                    processed_request_ids.add(request_id)\n", "\n", "                    # Update stats\n", "                    stats[\"success\"] += 1\n", "                    query_router_response = datum[\"query_router_response\"]\n", "                    stats[\"has_external_package\"] += int(\n", "                        query_router_response[\"external_package\"] != \"none\"\n", "                    )\n", "                    stats[\"has_specific_file\"] += int(\n", "                        len(query_router_response[\"about_specific_file\"]) > 0\n", "                    )\n", "                except json.JSONDecodeError:\n", "                    continue\n", "    except FileNotFoundError:\n", "        # File doesn't exist yet, return empty results\n", "        pass\n", "\n", "    return processed_request_ids, stats\n", "\n", "\n", "def convert_chunk_to_prompt_chunk(chunk):\n", "    return PromptChunk(\n", "        text=chunk.text,\n", "        path=chunk.parent_doc.path,\n", "        char_start=chunk.char_offset,\n", "        char_end=chunk.char_offset + chunk.length,\n", "        unique_id=chunk.id,\n", "        origin=\"dense_retriever\",\n", "    )\n", "\n", "\n", "blob_getter = BlobGetter(VANGUARD)\n", "query_router_tool = GeneralQueryRouter(docsets=list(DOCSETS))\n", "\n", "\n", "def process(sample):\n", "    request_id = sample[\"request_id\"]\n", "    with suppress_output():\n", "        chat_input = blob_getter.get_chat_request(\n", "            request_id,\n", "            raise_on_multiple=False,\n", "        )\n", "    assert chat_input is not None\n", "\n", "    chunks = deserialize_retrieved_chunks(sample[\"serialized_chunks\"])\n", "    chunks = [convert_chunk_to_prompt_chunk(chunk) for chunk in chunks]\n", "    chat_input_with_retrievals = dataclasses.replace(\n", "        chat_input, retrieved_chunks=chunks\n", "    )\n", "    try:\n", "        response_with_retrievals = run_query_router_claude(\n", "            chat_input_with_retrievals, PROMPT_FORMATTER, query_router_tool\n", "        )\n", "    except ExceedContextLength as e:\n", "        print(f\"Failed to run query router for {request_id}: {e}\")\n", "        return None, \"exceed_context_length\"\n", "    except FailedToFindExternalPackage as e:\n", "        print(f\"Failed to find external package for {request_id}: {e}\")\n", "        return None, \"failed_to_find_external_package\"\n", "    except BadRequestError as e:\n", "        print(f\"Failed to run query router for {request_id}: {e}\")\n", "        return None, \"bad_request\"\n", "    except:\n", "        assert False, request_id\n", "        raise\n", "\n", "    return (request_id, chat_input_with_retrievals, response_with_retrievals), \"success\"\n", "\n", "\n", "DATA_PATHS = Path(\n", "    \"/mnt/efs/spark-data/shared/agentqa/v0/i0-vanguard0_since_20240101_to_20250303/samples_with_retrieval/\"\n", ").glob(\"*.zstd.parquet\")\n", "OUTPUT_PATH = \"/mnt/efs/spark-data/shared/agentqa/v0/i0-vanguard0_since_20240101_to_20250303/queryrouterlabels_v4.jsonl\"\n", "SUPPORT_URL = (\n", "    \"https://support.i0.t.us-central1.prod.augmentcode.com/t/i0-vanguard0/request/\"\n", ")\n", "# DATA_PATHS = Path(\n", "#     \"/mnt/efs/spark-data/shared/agentqa/v0/i1-vanguard0_since_20240101_to_20250303/samples_with_retrieval/\"\n", "# ).glob(\"*.zstd.parquet\")\n", "# OUTPUT_PATH = \"/mnt/efs/spark-data/shared/agentqa/v0/i1-vanguard0_since_20240101_to_20250303/queryrouterlabels_v4.jsonl\"\n", "# SUPPORT_URL = (\n", "#     \"https://support.i0.t.us-central1.prod.augmentcode.com/t/i0-vanguard0/request/\"\n", "# )\n", "\n", "NUM_THREADS = 30\n", "\n", "processed_request_ids, stats = read_processed_data(OUTPUT_PATH)\n", "print(f\"Already processed {len(processed_request_ids)} requests\")\n", "print(f\"Stats: {dict(stats)}\")\n", "\n", "\n", "data_iterator = tqdm.tqdm(\n", "    maybe_run_in_multiple_threads(\n", "        process,\n", "        iter_parquet_files(DATA_PATHS, processed_request_ids),\n", "        num_threads=NUM_THREADS,\n", "    ),\n", ")\n", "\n", "with open(OUTPUT_PATH, \"a\") as f:\n", "    for response, status in data_iterator:\n", "        stats[status] += 1\n", "        if status != \"success\":\n", "            continue\n", "\n", "        request_id, chat_input, query_router_response = response\n", "\n", "        datum = {\n", "            \"request_id\": request_id,\n", "            \"chat_request\": dataclasses.asdict(chat_input),\n", "            \"query_router_response\": dataclasses.asdict(query_router_response),\n", "        }\n", "        json.dump(datum, f)\n", "\n", "        stats[\"has_external_package\"] += int(\n", "            query_router_response.external_package != \"none\"\n", "        )\n", "        stats[\"has_specific_file\"] += int(\n", "            len(query_router_response.about_specific_file) > 0\n", "        )\n", "        data_iterator.set_postfix(stats)\n", "        f.write(\"\\n\")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
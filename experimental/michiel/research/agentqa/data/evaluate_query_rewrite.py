import argparse
import json
import os
import threading
from collections import defaultdict
from dataclasses import asdict, dataclass
from datetime import datetime
from typing import Any, Optional, cast

import numpy as np
import tqdm

from experimental.michiel.research.agentqa.agent import (
    AgentLog,
)
from experimental.michiel.research.agentqa.data.utils import (
    extract_chunks,
    maybe_run_in_multiple_threads,
)
from experimental.michiel.research.agentqa.fast_agent import (
    InitialInformationAgent,
)
from experimental.michiel.research.agentqa.lib.query_rewriter import QueryRewriter
from experimental.michiel.research.agentqa.lib.retrieval_formatter import (
    RetrievalsFormatter,
)
from experimental.michiel.research.agentqa.lib.scoring import score_formatted_retrievals
from experimental.michiel.research.agentqa.lib.utils import interleave_sequences
from experimental.michiel.research.agentqa.tools import (
    CodebaseRetrievalTool,
    DirectorySubTreeTool,
)
from research.core.types import Document
from research.data.rag.retrieval_utils import serialize_retrieved_chunks
from research.eval.harness import utils
from research.eval.harness.factories import create_retriever
from research.eval.harness.tasks.augment_qa_task import AugmentQATask
from research.llm_apis.llm_client import (
    ToolCall,
    get_client,
)


@dataclass
class QueryRewriteConfig:
    max_initial_retrieval_chars: int
    max_followup_additional_retrieval_chars: int
    max_num_chunks: int
    max_directory_chars: int
    max_file_chars: int
    chat_history_len: int
    temperature: float
    n_attempts: int
    output_dir: str
    retriever_config: dict[str, Any]
    limit: Optional[int]
    store_retrievals: bool
    num_threads: int


def main(query_rewrite_config: QueryRewriteConfig):
    task = AugmentQATask.from_yaml_config(
        {
            "dataset_path": "/mnt/efs/augment/data/processed/augment_qa/v3",
            "html_report_output_dir": "/mnt/efs/augment/public_html/michiel/augmentqa_v3",
        }
    )

    print(f"There are {len(task)} examples in {task.name}")

    retriever = create_retriever(query_rewrite_config.retriever_config)
    retriever.load()
    retriever_lock = threading.Lock()

    sample = task.samples[0]
    repo_path = task.dataset_path / f"repos/{sample.repo}.jsonl.zst"
    docs = [Document(**d) for d in utils.read_jsonl_zst(repo_path)]

    print(f"Loaded {len(docs)} files for {sample.repo}")

    retriever.remove_all_docs()
    retriever.add_docs(docs)

    doc_ids = retriever.get_doc_ids()
    docs = retriever.get_docs(doc_ids)

    codebase_retrieval_tool = CodebaseRetrievalTool(
        retriever=retriever,
        max_tool_chars=0,  # This limit is unused as we use raw tool output, not formatted
        max_num_chunks=query_rewrite_config.max_num_chunks,
    )

    directory_subtree_tool = DirectorySubTreeTool(
        files=docs,
        max_directory_chars=query_rewrite_config.max_directory_chars,
        max_file_chars=query_rewrite_config.max_file_chars,
    )

    initial_information_agent = InitialInformationAgent(
        codebase_retrieval_tool=codebase_retrieval_tool,
        directory_subtree_tool=directory_subtree_tool,
    )

    slow_client = get_client(
        "anthropic-direct",
        model_name="claude-3-5-sonnet-20241022",
        max_retries=4,
    )

    query_rewriter = QueryRewriter(
        client=slow_client,
        codebase_retrieval_tool=codebase_retrieval_tool,
        chat_history_len=query_rewrite_config.chat_history_len,
        temperature=query_rewrite_config.temperature,
    )

    raw_metrics_dict = defaultdict(list)

    def process_sample(
        process_input,
    ):
        """Perform multiple retrieval attempts and score the results."""
        idx, sample = process_input
        sample_scores = {}
        retrieval_results = {}

        retrieval_formatter = RetrievalsFormatter()

        null_agent_log = AgentLog()
        # Initial directory search and codebase retrieval
        with retriever_lock:
            step0 = initial_information_agent(
                agent_log=null_agent_log,
                chat_input=sample,
            )

        assert len(step0.information_call_result) == 2

        initial_retrievals = extract_chunks(
            step0.information_call_result[1].tool_result
        )
        retrieval_formatter.add_chunks(
            initial_retrievals, query_rewrite_config.max_initial_retrieval_chars
        )
        initial_formatted_retrievals = retrieval_formatter.format()
        retrieval_results["initial"] = initial_formatted_retrievals
        retrieval_results["initial_serialized_chunks"] = serialize_retrieved_chunks(
            retrieval_formatter.chunks,
        )

        initial_score = score_formatted_retrievals(sample, initial_formatted_retrievals)

        for k, v in initial_score.items():
            sample_scores[f"initial_{k}"] = v

        # Compute 'baseline' with no rewrite and adding additional chunks
        followup_baseline_retrieval_formatter = RetrievalsFormatter(
            formatted_files={
                k: v for k, v in retrieval_formatter.formatted_files.items()
            },
        )
        followup_baseline_retrieval_formatter.add_chunks(
            initial_retrievals,
            query_rewrite_config.max_initial_retrieval_chars
            + query_rewrite_config.max_followup_additional_retrieval_chars,
        )
        formatted_combined_retrievals = followup_baseline_retrieval_formatter.format()
        retrieval_results["baseline"] = formatted_combined_retrievals
        retrieval_results["baseline_serialized_chunks"] = serialize_retrieved_chunks(
            followup_baseline_retrieval_formatter.chunks,
        )

        baseline_score = score_formatted_retrievals(
            sample, formatted_combined_retrievals
        )
        for k, v in baseline_score.items():
            sample_scores[f"baseline_{k}"] = v

        attempt_scores = []
        for attempt in range(query_rewrite_config.n_attempts):
            # copy retrieval_formatter
            followup_rewrite_retrieval_formatter = RetrievalsFormatter(
                formatted_files={
                    k: v for k, v in retrieval_formatter.formatted_files.items()
                },
            )

            retrieval_tool_call_results, retrieval_tool_call_metadata = (
                query_rewriter.rewrite_query(
                    chat_input=sample,
                    tool_history=step0.information_call_result,
                )
            )
            print("retrieval_tool_call_results")
            print(retrieval_tool_call_results)

            retrievals = []

            for retrieval_tool_call_result in retrieval_tool_call_results:
                queries = retrieval_tool_call_result.tool_input
                if "code_section_requests" in queries:
                    queries = queries["code_section_requests"]
                for query in queries:
                    print(query)

                assert str(type(retrieval_tool_call_result)) == str(ToolCall)
                retrieval_tool_call = cast(ToolCall, retrieval_tool_call_result)
                with retriever_lock:
                    retrieval_tool_output, _ = codebase_retrieval_tool.activate_tool(
                        retrieval_tool_call
                    )
                    retrievals.append(extract_chunks(retrieval_tool_output))

            retrievals = list(interleave_sequences(retrievals))
            # Currently new retrieval is added to initial retrieval
            # they are deduplicated in the formatter
            # In future, old and new retrieval may be combined in arbitrary ways
            followup_rewrite_retrieval_formatter.add_chunks(
                initial_retrievals, query_rewrite_config.max_initial_retrieval_chars
            )
            followup_rewrite_retrieval_formatter.add_chunks(
                retrievals,
                query_rewrite_config.max_followup_additional_retrieval_chars,
            )
            formatted_combined_retrievals = (
                followup_rewrite_retrieval_formatter.format()
            )
            retrieval_results[f"followup_{attempt}_tool_calls"] = [
                asdict(retrieval_tool_call_result)
                for retrieval_tool_call_result in retrieval_tool_call_results
            ]
            retrieval_results[f"followup_{attempt}_serialized_chunks"] = (
                serialize_retrieved_chunks(followup_rewrite_retrieval_formatter.chunks),
            )
            retrieval_results[f"followup_{attempt}"] = formatted_combined_retrievals

            attempt_score = score_formatted_retrievals(
                sample, formatted_combined_retrievals
            )
            attempt_scores.append(attempt_score)

        for k in initial_score.keys():
            current_key_scores = []
            for attempt_score in attempt_scores:
                current_key_scores.append(attempt_score[k])
            sample_scores[f"followup_{k}"] = current_key_scores

        return idx, sample, sample_scores, retrieval_results

    if query_rewrite_config.limit is not None:
        inputs = task.samples[: query_rewrite_config.limit]
    else:
        inputs = task.samples
    input_iterator = enumerate(inputs)
    data_iterator = tqdm.tqdm(
        maybe_run_in_multiple_threads(
            process_sample,
            input_iterator,
            num_threads=query_rewrite_config.num_threads,
        ),
    )

    result_dict = {}
    for idx, sample, sample_scores, retrieval_results in data_iterator:
        result_dict[idx] = {
            "sample": asdict(sample),
            "scores": sample_scores,
        }
        if query_rewrite_config.store_retrievals:
            result_dict[idx]["retrievals"] = retrieval_results
        for k, v in sample_scores.items():
            raw_metrics_dict[k].append(v)

    # Average score
    # Convert each top level key to an array
    metrics_array_dict = {
        key: np.array(value) for key, value in raw_metrics_dict.items()
    }

    print("average keyword recall in initial retrieval")
    avg_initial_retrievals_keyword_recall = np.mean(
        metrics_array_dict["initial_retrievals_keyword_recall"]
    )
    print(avg_initial_retrievals_keyword_recall)

    print("average keyword recall in baseline retrieval")
    avg_baseline_retrievals_keyword_recall = np.mean(
        metrics_array_dict["baseline_retrievals_keyword_recall"]
    )
    print(avg_baseline_retrievals_keyword_recall)

    print("average keyword recall in followup retrieval over attempts")
    avg_followup_retrievals_keyword_recall = np.mean(
        metrics_array_dict["followup_retrievals_keyword_recall"]
    )
    print(avg_followup_retrievals_keyword_recall)

    print("max keyword recall in followup retrieval over attempts")
    max_followup_retrievals_keyword_recall = np.mean(
        np.max(metrics_array_dict["followup_retrievals_keyword_recall"], axis=1)
    )
    print(max_followup_retrievals_keyword_recall)

    save_metrics = {
        "avg_initial_retrievals_keyword_recall": avg_initial_retrievals_keyword_recall,
        "avg_baseline_retrievals_keyword_recall": avg_baseline_retrievals_keyword_recall,
        "avg_followup_retrievals_keyword_recall": avg_followup_retrievals_keyword_recall,
        "max_followup_retrievals_keyword_recall": max_followup_retrievals_keyword_recall,
    }

    # Save results and config to json file
    # Use datetime as filename
    time_str = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
    output_path = os.path.join(query_rewrite_config.output_dir, f"{time_str}.json")
    output_dict = {
        "config": asdict(query_rewrite_config),
        "metrics": save_metrics,
        "processed_samples": result_dict,
    }
    with open(output_path, "w") as f:
        json.dump(
            output_dict,
            f,
            indent=2,
        )


# load retriever
retriever_config = {
    "scorer": {
        "name": "dense_scorer_v2_ffwd",
        "checkpoint_path": "/mnt/efs/augment/user/igor/checkpoints/chatanol/chatanol1-18.hybrid/neox/global_step1468",
        # "checkpoint_path": "/home/<USER>/data/checkpoints/chatanol/chatanol1-18.hybrid/neox/global_step1468",
        "cache_dir": "~/data/cache/queryrewrite_cache",
    },
    "chunker": {
        "name": "smart_line_level",
        "max_chunk_chars": 768,
        "max_headers": 3,
    },
    "query_formatter": {
        "name": "base:chatanol6-singleturnisspecial",
        "tokenizer_name": "rogue",
        "max_tokens": 1024,
    },
    "document_formatter": {
        "name": "base:ethanol6-embedding-with-path-key",
        "tokenizer_name": "rogue",
    },
}

query_rewrite_config = QueryRewriteConfig(
    max_initial_retrieval_chars=20_000,
    max_followup_additional_retrieval_chars=20_000,
    max_num_chunks=250,
    max_directory_chars=10_000,
    max_file_chars=10_000,
    chat_history_len=2_000,
    temperature=0.8,
    n_attempts=5,
    output_dir="/mnt/efs/spark-data/shared/agent/queryrewrite/results/",
    retriever_config=retriever_config,
    limit=None,
    store_retrievals=False,
    num_threads=1,
)

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--output_dir",
        "-o",
        type=str,
        help="The output path.",
    )
    parser.add_argument(
        "--limit",
        "-l",
        type=int,
        help="The limit for the number of samples.",
    )
    parser.add_argument(
        "--store_retrievals",
        "-s",
        action="store_true",
        help="Whether to store the retrievals.",
    )
    parser.add_argument(
        "--num_threads",
        "-n",
        type=int,
        help="The number of threads to use.",
    )
    parser.add_argument(
        "--num_attempts",
        "-a",
        type=int,
        help="The number of attempts to use.",
    )
    args = parser.parse_args()
    if args.output_dir is not None:
        query_rewrite_config.output_dir = args.output_dir

    if args.limit is not None:
        query_rewrite_config.limit = args.limit

    if args.store_retrievals:
        query_rewrite_config.store_retrievals = args.store_retrievals

    if args.num_threads is not None:
        query_rewrite_config.num_threads = args.num_threads

    if args.num_attempts is not None:
        query_rewrite_config.n_attempts = args.num_attempts

    main(query_rewrite_config)

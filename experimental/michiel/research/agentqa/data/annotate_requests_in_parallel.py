"""<PERSON><PERSON>t to generate answers given a repo and a set of questions."""

import concurrent.futures
import dataclasses
import json
import numpy as np
import openai
from pathlib import Path
from textwrap import dedent
import tqdm
from typing import Any, Callable, Iterator

from base.diff_utils.diff_utils import compute_file_diff, File
from research.data.synthetic_code_edit.api_lib import GPT_CHAT_PRICES


INPUT_COMMITS_DIR = Path("/mnt/efs/augment/user/yury/smart_paste/data/v3/commits")
OUTPUT_PATH = Path(
    # "/mnt/efs/augment/user/yury/smart_paste/data/v5/commits_with_instructions.jsonl"
    "/home/<USER>/data/commits_with_instructions.jsonl"
)


MAX_FILE_SIZE = 256000  # We are targetting 64K StarCoder2 tokens, which has around 3-4 characters per token.
N_SAMPLES = 500_000
GPT_MODEL_NAME = "gpt-4o-mini"
TEMPERATURE = 1.0
MAX_OUTPUT_TOKENS = 1024
NUM_PROCESSES = 35
PRINT_INTERVAL = 1000  # Print instruction every 1000 iterations


INPUT_COMMITS_FILES = sorted(list(INPUT_COMMITS_DIR.glob("*.jsonl")))
np.random.seed(31415)
np.random.shuffle(INPUT_COMMITS_FILES)


# Create a single OpenAI client instance
OPENAI_CLIENT = openai.OpenAI()


def get_id(datum: dict[str, Any]) -> int:
    return hash(
        (
            datum["repo_name"],
            datum["title"],
            datum["before_file"].path,
            datum["before_file"].contents,
        )
    )


def iterate_over_diffs(
    paths: list[Path],
    max_file_size: int,
    completed_sample_ids: set[int],
) -> Iterator[dict[str, Any]]:
    for path in paths:
        with open(path, "r") as f:
            for line in f:
                try:
                    datum: dict[str, Any] = json.loads(line)
                except json.JSONDecodeError:
                    continue

                if datum["before_file"]["path"] is None:
                    assert datum["before_file"]["contents"] is None
                    before_file = File(datum["after_file"]["path"], "")
                else:
                    before_file = File(
                        datum["before_file"]["path"], datum["before_file"]["contents"]
                    )
                if len(before_file.contents) > max_file_size:
                    continue

                after_file = File(
                    datum["after_file"]["path"], datum["after_file"]["contents"]
                )
                if len(after_file.contents) > max_file_size:
                    continue

                datum["before_file"] = before_file
                datum["after_file"] = after_file
                datum["id"] = get_id(datum)
                if datum["id"] in completed_sample_ids:
                    continue
                yield datum


def gpt_wrapper(
    messages: list[dict[str, str]],
    model: str,
    system_prompt: str | None = None,
    temperature: float | None = None,
    max_tokens: int | None = None,
    seed: int | None = None,
) -> tuple[str, float]:
    """Wrapper around the OpenAI API to make it easier to use."""

    if system_prompt:
        messages.insert(0, {"role": "system", "content": system_prompt})

    response = OPENAI_CLIENT.chat.completions.create(
        model=model,
        messages=messages,
        temperature=temperature,
        max_tokens=max_tokens,
        seed=seed,
    )

    assert response.usage is not None
    response_text = response.choices[0].message.content
    assert response_text is not None

    prompt_tokens = response.usage.prompt_tokens
    completion_tokens = response.usage.completion_tokens

    total_cost = (
        prompt_tokens * GPT_CHAT_PRICES[model]["prompt_tokens"]
        + completion_tokens * GPT_CHAT_PRICES[model]["completion_tokens"]
    )

    return response_text, total_cost


def format_prompt(sample: dict[str, Any]) -> str:
    before_file = sample["before_file"].contents
    diff = compute_file_diff(sample["before_file"], sample["after_file"], True)

    prompt = dedent(f"""\
I have a code file (denoted as FILE) as well as the changes made to this file in a commit. The title of this commit message is denoted as COMMIT_MESSAGE and the changes in a unidiff format are denoted as DIFF.

I'm working on an AI coding assistant, and I want it to re-do the commit from scratch given the original FILE and INSTRUCTION. Your task is to summarize the given COMMIT_MESSAGE and DIFF into an extremely concise INSTRUCTION, which will then be used by the AI assistant.

Start by identifying the key changes from the DIFF (using the COMMIT_MESSAGE for context). Think step-by-step. Finish your response with a line that starts with "**Instruction:**" followed by the INSTRUCTION itself on the same line, keeping it as short as possible.

COMMIT_MESSAGE

```
{sample["title"]}
```

FILE

```
{before_file}
```

DIFF

```
{diff}
```

""")
    return prompt


def extract_instruction(text: str) -> str | None:
    instruction_marker = "**Instruction:**"
    if instruction_marker not in text:
        return None
    instruction_marker_index = text.index(instruction_marker)
    text = text[instruction_marker_index + len(instruction_marker) :]
    text = text.strip()
    return text


def generate_instruction(diff: dict[str, Any]) -> dict[str, Any]:
    response = diff.copy()
    try:
        instruction_response, cost = gpt_wrapper(
            messages=[{"role": "user", "content": format_prompt(diff)}],
            model=GPT_MODEL_NAME,
            temperature=TEMPERATURE,
        )
        instruction = extract_instruction(instruction_response)
        response.update(
            {
                "gpt_response": instruction_response,
                "instruction": instruction,
                "status": "success" if instruction else "failed",
                "cost": cost,
            }
        )
    except openai.BadRequestError as e:
        if e.code == "context_length_exceeded":
            response.update({"status": "context_length_exceeded", "cost": 0.0})
        else:
            raise
    return response


def maybe_run_in_multiple_threads(
    processor_fn: Callable,
    inputs: Iterator[Any],
    num_threads: int,
) -> Iterator[Any]:
    """Run a processor in multiple threads if num_threads > 1."""
    assert num_threads > 0
    if num_threads == 1:
        yield from map(processor_fn, inputs)
    else:
        with concurrent.futures.ThreadPoolExecutor(max_workers=num_threads) as executor:
            futures = set()
            for item in inputs:
                if len(futures) >= num_threads:
                    done, futures = concurrent.futures.wait(
                        futures, return_when=concurrent.futures.FIRST_COMPLETED
                    )
                    for future in done:
                        yield future.result()
                futures.add(executor.submit(processor_fn, item))

            for future in concurrent.futures.as_completed(futures):
                yield future.result()


completed_sample_ids: set[int] = set()
if OUTPUT_PATH.exists():
    for line in OUTPUT_PATH.open("r"):
        response: dict[str, Any] = json.loads(line[:-1])
        if response["status"] != "success":
            continue
        completed_sample_ids.add(response["id"])
    print(f"Loaded {len(completed_sample_ids)} already completed diffs")

pbar = tqdm.tqdm(
    desc="Generate suggested edits",
    total=N_SAMPLES,
)
n_samples: int = len(completed_sample_ids)
pbar.update(n_samples)

n_failed: int = 0
cost: float = 0.0

with OUTPUT_PATH.open("a") as f:
    for index, response in enumerate(
        maybe_run_in_multiple_threads(
            generate_instruction,
            iterate_over_diffs(
                INPUT_COMMITS_FILES,
                MAX_FILE_SIZE,
                completed_sample_ids,
            ),
            num_threads=NUM_PROCESSES,
        ),
        start=1,
    ):
        response["before_file"] = dataclasses.asdict(response["before_file"])
        response["after_file"] = dataclasses.asdict(response["after_file"])
        f.write(json.dumps(response) + "\n")
        f.flush()
        n_samples += 1
        if response["status"] == "success":
            pbar.update()
            if index % PRINT_INTERVAL == 0:
                print(response["instruction"])
        else:
            n_failed += 1
        cost += response["cost"]
        pbar.set_postfix(
            {
                "n_failed": n_failed,
                "cost": cost,
            }
        )
        if n_samples >= N_SAMPLES:
            break
print("Done")

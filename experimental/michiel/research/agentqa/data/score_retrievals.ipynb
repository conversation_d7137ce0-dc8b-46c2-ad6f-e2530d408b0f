{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["There are 100 examples in AugmentQATask\n"]}], "source": ["from research.eval.harness.tasks.augment_qa_task import AugmentQATask\n", "\n", "task = AugmentQATask.from_yaml_config(\n", "    {\n", "        \"dataset_path\": \"/mnt/efs/augment/data/processed/augment_qa/v3\",\n", "        \"html_report_output_dir\": \"/mnt/efs/augment/public_html/michiel/augmentqa_v3\",\n", "    }\n", ")\n", "\n", "print(f\"There are {len(task)} examples in {task.name}\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["from typing import Any, Callable\n", "from research.eval.harness.tasks.augment_qa_task import count_keywords\n", "\n", "\n", "RetrievalScorer = Callable[[str, Any], float]\n", "\n", "\n", "def keywords_recall_scorer(retrieval_section: str, label: list[str]) -> float:\n", "    assert len(set(label)) == len(label)\n", "    assert len(label) > 0\n", "    n_keywords = len(label)\n", "    n_matched_keywords = count_keywords(retrieval_section, label)\n", "    return n_matched_keywords / n_keywords if n_keywords > 0 else 1.0\n", "\n", "\n", "def keywords_mrr_scorer(chunks: list[Any], label: list[str]) -> float:\n", "    keyword_reciprocal_ranks = []\n", "    for keyword in label:\n", "        keyword_rank = None\n", "        for index, chunk in enumerate(chunks):\n", "            if keyword in chunk.text or keyword == chunk.path:\n", "                keyword_rank = index\n", "                break\n", "        if keyword_rank is not None:\n", "            keyword_reciprocal_ranks.append(1.0 / (keyword_rank + 1))\n", "        else:\n", "            keyword_reciprocal_ranks.append(0.0)\n", "    return sum(keyword_reciprocal_ranks) / len(keyword_reciprocal_ranks)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 100 examples from c64k_v1 (10 with paths)\n", "Loaded 100 examples from c12k_v1 (53 with paths)\n", "Loaded 100 examples from c120k_v2 (53 with paths)\n", "Loaded 100 examples from c64k_v2 (60 with paths)\n", "Loaded 100 examples from c12k_v2 (70 with paths)\n"]}], "source": ["import enum\n", "import dataclasses\n", "import json\n", "import pandas as pd\n", "from pathlib import Path\n", "import re\n", "\n", "\n", "class ConfidenceLevel(enum.Enum):\n", "    BAD = \"bad\"\n", "    MEDIOCRE = \"mediocre\"\n", "    OK = \"ok\"\n", "    GOOD = \"good\"\n", "\n", "\n", "def extract_paths_from_snippets(text: str) -> list[str]:\n", "    \"\"\"Extract paths from augment code snippets in the text.\n", "\n", "    Args:\n", "        text: String containing potential augment code snippets\n", "\n", "    Returns:\n", "        List of paths found in path= attributes\n", "    \"\"\"\n", "    pattern = r'<augment_code_snippet\\s+path=\"([^\"]+)\"'\n", "    paths = re.findall(pattern, text)\n", "    return paths\n", "\n", "\n", "@dataclasses.dataclass(frozen=True)\n", "class Label:\n", "    answer: str\n", "    confidence: ConfidenceLevel\n", "    paths: list[str]\n", "    keywords: list[str]\n", "    answer_paths: list[str]\n", "    combined_paths: list[str]\n", "\n", "    @classmethod\n", "    def from_json(cls, data: dict) -> \"Label\":\n", "        answer_paths = extract_paths_from_snippets(data[\"answer\"])\n", "        combined_paths = list(set(data[\"paths\"] + answer_paths))\n", "        return cls(\n", "            answer=data[\"answer\"],\n", "            confidence=ConfidenceLevel(data[\"confidence\"]),\n", "            paths=data[\"paths\"],\n", "            keywords=data[\"keywords\"],\n", "            answer_paths=answer_paths,\n", "            combined_paths=combined_paths,\n", "        )\n", "\n", "\n", "def load_labels(path: Path) -> list[Label]:\n", "    with path.open(\"r\") as f:\n", "        return [Label.from_json(json.loads(line)) for line in f]\n", "\n", "\n", "BASE_DIR = Path(\"/mnt/efs/augment/user/yury/agentqa\")\n", "datasets = {\n", "    # v1 datasets\n", "    \"c64k_v1\": \"augmentqa_silver_c64k_v1.jsonl\",\n", "    \"c12k_v1\": \"augmentqa_silver_c12k_v1.jsonl\",\n", "    # v2 datasets\n", "    \"c120k_v2\": \"augmentqa_silver_c120k_v2.jsonl\",\n", "    \"c64k_v2\": \"augmentqa_silver_c64k_v2.jsonl\",\n", "    \"c12k_v2\": \"augmentqa_silver_c12k_v2.jsonl\",\n", "}\n", "\n", "silver_labels = {}\n", "for name, filename in datasets.items():\n", "    path = BASE_DIR / filename\n", "    silver_labels[name] = load_labels(path)\n", "    n_answers_with_paths = sum(\n", "        1 for label in silver_labels[name] if len(label.answer_paths) > 0\n", "    )\n", "    print(\n", "        f\"Loaded {len(silver_labels[name])} examples from {name} ({n_answers_with_paths} with paths)\"\n", "    )"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["100\n", "dict_keys(['initial', 'initial_serialized_chunks', 'baseline', 'baseline_serialized_chunks', 'followup_0_tool_calls', 'followup_0_serialized_chunks', 'followup_0', 'followup_1_tool_calls', 'followup_1_serialized_chunks', 'followup_1', 'followup_2_tool_calls', 'followup_2_serialized_chunks', 'followup_2', 'followup_3_tool_calls', 'followup_3_serialized_chunks', 'followup_3', 'followup_4_tool_calls', 'followup_4_serialized_chunks', 'followup_4'])\n"]}], "source": ["queryrewrites = json.load(\n", "    open(\n", "        \"/mnt/efs/spark-data/shared/agent/queryrewrite/results/2025-01-13_00-03-13.json\"\n", "    )\n", ")\n", "print(len(queryrewrites[\"processed_samples\"]))\n", "print(queryrewrites[\"processed_samples\"][\"0\"][\"retrievals\"].keys())"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["RETRIEVAL_SETS = [\n", "    # 'baseline',\n", "    \"followup_0\",\n", "    \"followup_1\",\n", "    \"followup_2\",\n", "    \"followup_3\",\n", "    \"followup_4\",\n", "]\n", "\n", "for sample_idx in range(100):\n", "    for retrieval_name in RETRIEVAL_SETS:\n", "        retrieval_set = queryrewrites[\"processed_samples\"][str(sample_idx)][\n", "            \"retrievals\"\n", "        ][retrieval_name]\n", "        baseline_retrieval_set = queryrewrites[\"processed_samples\"][str(sample_idx)][\n", "            \"retrievals\"\n", "        ][\"baseline\"]\n", "\n", "        gold_score = keywords_recall_scorer(\n", "            retrieval_set,\n", "            task.samples[sample_idx].keywords,\n", "        )\n", "        gold_baseline_score = keywords_recall_scorer(\n", "            baseline_retrieval_set,\n", "            task.samples[sample_idx].keywords,\n", "        )\n", "\n", "        if gold_score > gold_baseline_score:\n", "            # print(task.samples[sample_idx].message)\n", "\n", "            tool_calls = queryrewrites[\"processed_samples\"][str(sample_idx)][\n", "                \"retrievals\"\n", "            ][retrieval_name + \"_tool_calls\"]\n", "            # for tool_call in tool_calls:\n", "            #     for code_section_request in tool_call[\"tool_input\"][\"code_section_requests\"]:\n", "            #         print(\"\\t\", code_section_request)"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>gold_score</th>\n", "      <th>silver_c120k_v2_mrr_keywords</th>\n", "      <th>silver_c120k_v2_recall_keywords</th>\n", "      <th>silver_c120k_v2_mrr_paths</th>\n", "      <th>silver_c120k_v2_mrr_combined_paths</th>\n", "      <th>silver_c120k_v2_recall_paths</th>\n", "      <th>silver_c120k_v2_recall_avg</th>\n", "      <th>silver_c120k_v2_mrr_avg</th>\n", "      <th>silver_c120k_v2_avg</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>1500.000000</td>\n", "      <td>1500.000000</td>\n", "      <td>1500.000000</td>\n", "      <td>1500.000000</td>\n", "      <td>1500.000000</td>\n", "      <td>1500.000000</td>\n", "      <td>1500.000000</td>\n", "      <td>1500.000000</td>\n", "      <td>1500.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>0.000504</td>\n", "      <td>0.000665</td>\n", "      <td>0.023952</td>\n", "      <td>0.000764</td>\n", "      <td>0.000718</td>\n", "      <td>0.021315</td>\n", "      <td>0.022633</td>\n", "      <td>0.000714</td>\n", "      <td>0.011674</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>0.005779</td>\n", "      <td>0.005261</td>\n", "      <td>0.175598</td>\n", "      <td>0.007029</td>\n", "      <td>0.006546</td>\n", "      <td>0.232346</td>\n", "      <td>0.185189</td>\n", "      <td>0.005483</td>\n", "      <td>0.095222</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>-0.032258</td>\n", "      <td>-0.024250</td>\n", "      <td>-1.000000</td>\n", "      <td>-0.038462</td>\n", "      <td>-0.035714</td>\n", "      <td>-1.000000</td>\n", "      <td>-1.000000</td>\n", "      <td>-0.025000</td>\n", "      <td>-0.511719</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>0.034483</td>\n", "      <td>0.035714</td>\n", "      <td>1.000000</td>\n", "      <td>0.038690</td>\n", "      <td>0.038690</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>0.034483</td>\n", "      <td>0.517241</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        gold_score  silver_c120k_v2_mrr_keywords  \\\n", "count  1500.000000                   1500.000000   \n", "mean      0.000504                      0.000665   \n", "std       0.005779                      0.005261   \n", "min      -0.032258                     -0.024250   \n", "25%       0.000000                      0.000000   \n", "50%       0.000000                      0.000000   \n", "75%       0.000000                      0.000000   \n", "max       0.034483                      0.035714   \n", "\n", "       silver_c120k_v2_recall_keywords  silver_c120k_v2_mrr_paths  \\\n", "count                      1500.000000                1500.000000   \n", "mean                          0.023952                   0.000764   \n", "std                           0.175598                   0.007029   \n", "min                          -1.000000                  -0.038462   \n", "25%                           0.000000                   0.000000   \n", "50%                           0.000000                   0.000000   \n", "75%                           0.000000                   0.000000   \n", "max                           1.000000                   0.038690   \n", "\n", "       silver_c120k_v2_mrr_combined_paths  silver_c120k_v2_recall_paths  \\\n", "count                         1500.000000                   1500.000000   \n", "mean                             0.000718                      0.021315   \n", "std                              0.006546                      0.232346   \n", "min                             -0.035714                     -1.000000   \n", "25%                              0.000000                      0.000000   \n", "50%                              0.000000                      0.000000   \n", "75%                              0.000000                      0.000000   \n", "max                              0.038690                      1.000000   \n", "\n", "       silver_c120k_v2_recall_avg  silver_c120k_v2_mrr_avg  \\\n", "count                 1500.000000              1500.000000   \n", "mean                     0.022633                 0.000714   \n", "std                      0.185189                 0.005483   \n", "min                     -1.000000                -0.025000   \n", "25%                      0.000000                 0.000000   \n", "50%                      0.000000                 0.000000   \n", "75%                      0.000000                 0.000000   \n", "max                      1.000000                 0.034483   \n", "\n", "       silver_c120k_v2_avg  \n", "count          1500.000000  \n", "mean              0.011674  \n", "std               0.095222  \n", "min              -0.511719  \n", "25%               0.000000  \n", "50%               0.000000  \n", "75%               0.000000  \n", "max               0.517241  "]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["import numpy as np\n", "from research.data.rag.retrieval_utils import deserialize_retrieved_chunks\n", "\n", "\n", "stats = []\n", "silver_label_name = \"c120k_v2\"\n", "RETRIEVAL_SETS = [\n", "    \"baseline\",\n", "    \"followup_0\",\n", "    \"followup_1\",\n", "    \"followup_2\",\n", "    \"followup_3\",\n", "    \"followup_4\",\n", "]\n", "\n", "\n", "for sample_idx in range(100):\n", "    for retrieval_name_a in RETRIEVAL_SETS:\n", "        retrieval_set_a = queryrewrites[\"processed_samples\"][str(sample_idx)][\n", "            \"retrievals\"\n", "        ][retrieval_name_a]\n", "        retrieval_chunks_a = queryrewrites[\"processed_samples\"][str(sample_idx)][\n", "            \"retrievals\"\n", "        ][retrieval_name_a + \"_serialized_chunks\"]\n", "        retrieval_chunks_a = deserialize_retrieved_chunks(retrieval_chunks_a)\n", "\n", "        for retrieval_name_b in RETRIEVAL_SETS:\n", "            if retrieval_name_a >= retrieval_name_b:\n", "                continue\n", "\n", "            retrieval_set_b = queryrewrites[\"processed_samples\"][str(sample_idx)][\n", "                \"retrievals\"\n", "            ][retrieval_name_b]\n", "            retrieval_chunks_b = queryrewrites[\"processed_samples\"][str(sample_idx)][\n", "                \"retrievals\"\n", "            ][retrieval_name_b + \"_serialized_chunks\"]\n", "            retrieval_chunks_b = deserialize_retrieved_chunks(retrieval_chunks_b)\n", "\n", "            gold_score_a = keywords_mrr_scorer(\n", "                retrieval_chunks_a,\n", "                task.samples[sample_idx].keywords,\n", "            )\n", "            gold_score_b = keywords_mrr_scorer(\n", "                retrieval_chunks_b,\n", "                task.samples[sample_idx].keywords,\n", "            )\n", "\n", "            silver_keywords = silver_labels[silver_label_name][sample_idx].keywords\n", "            silver_paths = silver_labels[silver_label_name][sample_idx].paths\n", "            silver_combined_paths = silver_labels[silver_label_name][\n", "                sample_idx\n", "            ].combined_paths\n", "\n", "            current_stats = {\n", "                \"silver_label_confidence\": silver_labels[silver_label_name][\n", "                    sample_idx\n", "                ].confidence.value,\n", "                \"retrieval_set_pair\": f\"{retrieval_name_a}-{retrieval_name_b}\",\n", "                \"gold_score\": (\n", "                    keywords_mrr_scorer(\n", "                        retrieval_chunks_b,\n", "                        task.samples[sample_idx].keywords,\n", "                    )\n", "                    - keywords_mrr_scorer(\n", "                        retrieval_chunks_a,\n", "                        task.samples[sample_idx].keywords,\n", "                    )\n", "                ),\n", "                f\"silver_{silver_label_name}_mrr_keywords\": (\n", "                    keywords_mrr_scorer(\n", "                        retrieval_chunks_b,\n", "                        silver_keywords,\n", "                    )\n", "                    - keywords_mrr_scorer(\n", "                        retrieval_chunks_a,\n", "                        silver_keywords,\n", "                    )\n", "                ),\n", "                f\"silver_{silver_label_name}_recall_keywords\": (\n", "                    keywords_recall_scorer(\n", "                        retrieval_set_b,\n", "                        silver_keywords,\n", "                    )\n", "                    - keywords_recall_scorer(\n", "                        retrieval_set_a,\n", "                        silver_keywords,\n", "                    )\n", "                ),\n", "                f\"silver_{silver_label_name}_mrr_paths\": (\n", "                    keywords_mrr_scorer(\n", "                        retrieval_chunks_b,\n", "                        silver_paths,\n", "                    )\n", "                    - keywords_mrr_scorer(\n", "                        retrieval_chunks_a,\n", "                        silver_paths,\n", "                    )\n", "                ),\n", "                f\"silver_{silver_label_name}_mrr_combined_paths\": (\n", "                    keywords_mrr_scorer(\n", "                        retrieval_chunks_b,\n", "                        silver_combined_paths,\n", "                    )\n", "                    - keywords_mrr_scorer(\n", "                        retrieval_chunks_a,\n", "                        silver_combined_paths,\n", "                    )\n", "                ),\n", "                f\"silver_{silver_label_name}_recall_paths\": (\n", "                    keywords_recall_scorer(\n", "                        retrieval_set_b,\n", "                        silver_paths,\n", "                    )\n", "                    - keywords_recall_scorer(\n", "                        retrieval_set_a,\n", "                        silver_paths,\n", "                    )\n", "                ),\n", "            }\n", "            current_stats[f\"silver_{silver_label_name}_recall_avg\"] = (\n", "                current_stats[f\"silver_{silver_label_name}_recall_keywords\"]\n", "                + current_stats[f\"silver_{silver_label_name}_recall_paths\"]\n", "            ) / 2\n", "            current_stats[f\"silver_{silver_label_name}_mrr_avg\"] = (\n", "                current_stats[f\"silver_{silver_label_name}_mrr_keywords\"]\n", "                + current_stats[f\"silver_{silver_label_name}_mrr_paths\"]\n", "            ) / 2\n", "            current_stats[f\"silver_{silver_label_name}_avg\"] = (\n", "                current_stats[f\"silver_{silver_label_name}_recall_avg\"]\n", "                + current_stats[f\"silver_{silver_label_name}_mrr_avg\"]\n", "            ) / 2\n", "            stats.append(current_stats)\n", "\n", "\n", "stats = pd.DataFrame(stats)\n", "stats.describe()"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Number of gold A < B samples: 284\n", "\n", "silver_c120k_v2_mrr_keywords classified 312 samples as A < B\n", "\t 15.7% of samples that is actually A > B\n", "\t 40.7% of samples that is actually A = B\n", "\t 43.6% of samples that is actually A < B\n", "\t 47.9% of gold A < B samples recovered\n", "\n", "silver_c120k_v2_mrr_paths classified 279 samples as A < B\n", "\t 5.4% of samples that is actually A > B\n", "\t 46.6% of samples that is actually A = B\n", "\t 48.0% of samples that is actually A < B\n", "\t 47.2% of gold A < B samples recovered\n", "\n", "silver_c120k_v2_mrr_combined_paths classified 279 samples as A < B\n", "\t 5.4% of samples that is actually A > B\n", "\t 46.6% of samples that is actually A = B\n", "\t 48.0% of samples that is actually A < B\n", "\t 47.2% of gold A < B samples recovered\n", "\n", "silver_c120k_v2_mrr_avg classified 453 samples as A < B\n", "\t 11.5% of samples that is actually A > B\n", "\t 48.1% of samples that is actually A = B\n", "\t 40.4% of samples that is actually A < B\n", "\t 64.4% of gold A < B samples recovered\n", "\n", "silver_c120k_v2_recall_keywords classified 174 samples as A < B\n", "\t 6.9% of samples that is actually A > B\n", "\t 46.6% of samples that is actually A = B\n", "\t 46.6% of samples that is actually A < B\n", "\t 28.5% of gold A < B samples recovered\n", "\n", "silver_c120k_v2_recall_paths classified 203 samples as A < B\n", "\t 3.4% of samples that is actually A > B\n", "\t 46.8% of samples that is actually A = B\n", "\t 49.8% of samples that is actually A < B\n", "\t 35.6% of gold A < B samples recovered\n", "\n", "silver_c120k_v2_recall_avg classified 283 samples as A < B\n", "\t 6.7% of samples that is actually A > B\n", "\t 52.7% of samples that is actually A = B\n", "\t 40.6% of samples that is actually A < B\n", "\t 40.5% of gold A < B samples recovered\n", "\n"]}], "source": ["# Compute simple statistics\n", "\n", "print(f\"Number of gold A < B samples: {(stats.gold_score != 0).sum()}\")\n", "print()\n", "\n", "for feature in [\n", "    \"silver_c120k_v2_mrr_keywords\",\n", "    \"silver_c120k_v2_mrr_paths\",\n", "    \"silver_c120k_v2_mrr_combined_paths\",\n", "    \"silver_c120k_v2_mrr_avg\",\n", "    \"silver_c120k_v2_recall_keywords\",\n", "    \"silver_c120k_v2_recall_paths\",\n", "    \"silver_c120k_v2_recall_avg\",\n", "]:\n", "    true_positives_hard = ((stats.gold_score > 0) & (stats[feature] > 0)) | (\n", "        (stats.gold_score < 0) & (stats[feature] < 0)\n", "    )\n", "    false_positives_hard = ((stats.gold_score == 0) & (stats[feature] > 0)) | (\n", "        (stats.gold_score == 0) & (stats[feature] < 0)\n", "    )\n", "    false_positives_easy = ((stats.gold_score < 0) & (stats[feature] > 0)) | (\n", "        (stats.gold_score > 0) & (stats[feature] < 0)\n", "    )\n", "\n", "    print(f\"{feature} classified {(stats[feature] != 0).sum()} samples as A < B\")\n", "    print(\n", "        f\"\\t {100.0 * false_positives_easy.sum() / (stats[feature] != 0).sum():.1f}% of samples that is actually A > B\"\n", "    )\n", "    print(\n", "        f\"\\t {100.0 * false_positives_hard.sum() / (stats[feature] != 0).sum():.1f}% of samples that is actually A = B\"\n", "    )\n", "    print(\n", "        f\"\\t {100.0 * true_positives_hard.sum() / (stats[feature] != 0).sum():.1f}% of samples that is actually A < B\"\n", "    )\n", "    print(\n", "        f\"\\t {100.0 * true_positives_hard.sum() / (stats.gold_score != 0).sum():.1f}% of gold A < B samples recovered\"\n", "    )\n", "    print()"]}, {"cell_type": "code", "execution_count": 116, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Silver label confidence: bad\n", "Number of gold A < B samples: 0\n", "\n", "silver_c120k_v2_mrr_paths classified 0 samples as A < B\n", "\t nan% of samples that is actually A > B\n", "\t nan% of samples that is actually A = B\n", "\t nan% of samples that is actually A < B\n", "\t nan% of gold A < B samples recovered\n", "\n", "Silver label confidence: mediocre\n", "Number of gold A < B samples: 5\n", "\n", "silver_c120k_v2_mrr_paths classified 9 samples as A < B\n", "\t 0.0% of samples that is actually A > B\n", "\t 100.0% of samples that is actually A = B\n", "\t 0.0% of samples that is actually A < B\n", "\t 0.0% of gold A < B samples recovered\n", "\n", "Silver label confidence: ok\n", "Number of gold A < B samples: 62\n", "\n", "silver_c120k_v2_mrr_paths classified 59 samples as A < B\n", "\t 1.7% of samples that is actually A > B\n", "\t 8.5% of samples that is actually A = B\n", "\t 89.8% of samples that is actually A < B\n", "\t 85.5% of gold A < B samples recovered\n", "\n", "Silver label confidence: good\n", "Number of gold A < B samples: 217\n", "\n", "silver_c120k_v2_mrr_paths classified 211 samples as A < B\n", "\t 6.6% of samples that is actually A > B\n", "\t 55.0% of samples that is actually A = B\n", "\t 38.4% of samples that is actually A < B\n", "\t 37.3% of gold A < B samples recovered\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1290148/1541320788.py:19: RuntimeWarning: invalid value encountered in scalar divide\n", "  print(f\"\\t {100.0 * false_positives_easy.sum() / (s[feature] != 0).sum():.1f}% of samples that is actually A > B\")\n", "/tmp/ipykernel_1290148/1541320788.py:20: RuntimeWarning: invalid value encountered in scalar divide\n", "  print(f\"\\t {100.0 * false_positives_hard.sum() / (s[feature] != 0).sum():.1f}% of samples that is actually A = B\")\n", "/tmp/ipykernel_1290148/1541320788.py:21: RuntimeWarning: invalid value encountered in scalar divide\n", "  print(f\"\\t {100.0 * true_positives_hard.sum() / (s[feature] != 0).sum():.1f}% of samples that is actually A < B\")\n", "/tmp/ipykernel_1290148/1541320788.py:22: RuntimeWarning: invalid value encountered in scalar divide\n", "  print(f\"\\t {100.0 * true_positives_hard.sum() / (s.gold_score != 0).sum():.1f}% of gold A < B samples recovered\")\n"]}], "source": ["for confidence_level in [level.value for level in ConfidenceLevel]:\n", "    print(f\"Silver label confidence: {confidence_level}\")\n", "    s = stats[stats.silver_label_confidence == confidence_level]\n", "\n", "    print(f\"Number of gold A < B samples: {(s.gold_score != 0).sum()}\")\n", "    print()\n", "\n", "    for feature in [\n", "        # \"silver_c120k_v2_mrr_keywords\",\n", "        \"silver_c120k_v2_mrr_paths\",\n", "        # \"silver_c120k_v2_mrr_avg\",\n", "    ]:\n", "        true_positives_hard = ((s.gold_score > 0) & (s[feature] > 0)) | (\n", "            (s.gold_score < 0) & (s[feature] < 0)\n", "        )\n", "        false_positives_hard = ((s.gold_score == 0) & (s[feature] > 0)) | (\n", "            (s.gold_score == 0) & (s[feature] < 0)\n", "        )\n", "        false_positives_easy = ((s.gold_score < 0) & (s[feature] > 0)) | (\n", "            (s.gold_score > 0) & (s[feature] < 0)\n", "        )\n", "\n", "        print(f\"{feature} classified {(s[feature] != 0).sum()} samples as A < B\")\n", "        print(\n", "            f\"\\t {100.0 * false_positives_easy.sum() / (s[feature] != 0).sum():.1f}% of samples that is actually A > B\"\n", "        )\n", "        print(\n", "            f\"\\t {100.0 * false_positives_hard.sum() / (s[feature] != 0).sum():.1f}% of samples that is actually A = B\"\n", "        )\n", "        print(\n", "            f\"\\t {100.0 * true_positives_hard.sum() / (s[feature] != 0).sum():.1f}% of samples that is actually A < B\"\n", "        )\n", "        print(\n", "            f\"\\t {100.0 * true_positives_hard.sum() / (s.gold_score != 0).sum():.1f}% of gold A < B samples recovered\"\n", "        )\n", "        print()"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Coefficients:\n", "  silver_c120k_v2_mrr_keywords: 0.327\n", "  silver_c120k_v2_mrr_paths: 0.348\n", "Intercept: 0.000\n", "R-squared score: 0.415\n"]}], "source": ["from sklearn.linear_model import LinearRegression\n", "\n", "# Prepare features (X) and label (y)\n", "X = stats[[\"silver_c120k_v2_mrr_keywords\", \"silver_c120k_v2_mrr_paths\"]]\n", "y = stats[\"gold_score\"]\n", "\n", "# Create and fit the model\n", "model = LinearRegression(fit_intercept=True)\n", "model.fit(X, y)\n", "\n", "# Print model coefficients and intercept\n", "print(\"Coefficients:\")\n", "for feature, coef in zip(X.columns, model.coef_):\n", "    print(f\"  {feature}: {coef:.3f}\")\n", "print(f\"Intercept: {model.intercept_:.3f}\")\n", "\n", "# Make predictions\n", "y_pred = model.predict(X)\n", "\n", "# Calculate R-squared score\n", "r2_score = model.score(X, y)\n", "print(f\"R-squared score: {r2_score:.3f}\")"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>gold_score</th>\n", "      <th>silver_c120k_v2_mrr_keywords</th>\n", "      <th>silver_c120k_v2_recall_keywords</th>\n", "      <th>silver_c120k_v2_mrr_paths</th>\n", "      <th>silver_c120k_v2_mrr_combined_paths</th>\n", "      <th>silver_c120k_v2_recall_paths</th>\n", "      <th>silver_c120k_v2_recall_avg</th>\n", "      <th>silver_c120k_v2_mrr_avg</th>\n", "      <th>silver_c120k_v2_avg</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>600.000000</td>\n", "      <td>600.000000</td>\n", "      <td>600.000000</td>\n", "      <td>600.000000</td>\n", "      <td>600.000000</td>\n", "      <td>600.000000</td>\n", "      <td>600.000000</td>\n", "      <td>600.000000</td>\n", "      <td>600.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>0.389420</td>\n", "      <td>0.379446</td>\n", "      <td>0.802091</td>\n", "      <td>0.382568</td>\n", "      <td>0.381806</td>\n", "      <td>0.779732</td>\n", "      <td>0.790912</td>\n", "      <td>0.381007</td>\n", "      <td>0.585959</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>0.368501</td>\n", "      <td>0.318467</td>\n", "      <td>0.273485</td>\n", "      <td>0.331997</td>\n", "      <td>0.330693</td>\n", "      <td>0.341492</td>\n", "      <td>0.268773</td>\n", "      <td>0.284732</td>\n", "      <td>0.243805</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>0.043685</td>\n", "      <td>0.076289</td>\n", "      <td>0.666667</td>\n", "      <td>0.062745</td>\n", "      <td>0.066667</td>\n", "      <td>0.500000</td>\n", "      <td>0.666667</td>\n", "      <td>0.099850</td>\n", "      <td>0.429198</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>0.260530</td>\n", "      <td>0.333333</td>\n", "      <td>1.000000</td>\n", "      <td>0.354167</td>\n", "      <td>0.340290</td>\n", "      <td>1.000000</td>\n", "      <td>0.875000</td>\n", "      <td>0.399206</td>\n", "      <td>0.601960</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>0.673380</td>\n", "      <td>0.616667</td>\n", "      <td>1.000000</td>\n", "      <td>0.583333</td>\n", "      <td>0.583333</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>0.583043</td>\n", "      <td>0.762379</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       gold_score  silver_c120k_v2_mrr_keywords  \\\n", "count  600.000000                    600.000000   \n", "mean     0.389420                      0.379446   \n", "std      0.368501                      0.318467   \n", "min      0.000000                      0.000000   \n", "25%      0.043685                      0.076289   \n", "50%      0.260530                      0.333333   \n", "75%      0.673380                      0.616667   \n", "max      1.000000                      1.000000   \n", "\n", "       silver_c120k_v2_recall_keywords  silver_c120k_v2_mrr_paths  \\\n", "count                       600.000000                 600.000000   \n", "mean                          0.802091                   0.382568   \n", "std                           0.273485                   0.331997   \n", "min                           0.000000                   0.000000   \n", "25%                           0.666667                   0.062745   \n", "50%                           1.000000                   0.354167   \n", "75%                           1.000000                   0.583333   \n", "max                           1.000000                   1.000000   \n", "\n", "       silver_c120k_v2_mrr_combined_paths  silver_c120k_v2_recall_paths  \\\n", "count                          600.000000                    600.000000   \n", "mean                             0.381806                      0.779732   \n", "std                              0.330693                      0.341492   \n", "min                              0.000000                      0.000000   \n", "25%                              0.066667                      0.500000   \n", "50%                              0.340290                      1.000000   \n", "75%                              0.583333                      1.000000   \n", "max                              1.000000                      1.000000   \n", "\n", "       silver_c120k_v2_recall_avg  silver_c120k_v2_mrr_avg  \\\n", "count                  600.000000               600.000000   \n", "mean                     0.790912                 0.381007   \n", "std                      0.268773                 0.284732   \n", "min                      0.000000                 0.000000   \n", "25%                      0.666667                 0.099850   \n", "50%                      0.875000                 0.399206   \n", "75%                      1.000000                 0.583043   \n", "max                      1.000000                 1.000000   \n", "\n", "       silver_c120k_v2_avg  \n", "count           600.000000  \n", "mean              0.585959  \n", "std               0.243805  \n", "min               0.000000  \n", "25%               0.429198  \n", "50%               0.601960  \n", "75%               0.762379  \n", "max               1.000000  "]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "from research.data.rag.retrieval_utils import deserialize_retrieved_chunks\n", "\n", "\n", "stats = []\n", "silver_label_name = \"c120k_v2\"\n", "RETRIEVAL_SETS = [\n", "    \"baseline\",\n", "    \"followup_0\",\n", "    \"followup_1\",\n", "    \"followup_2\",\n", "    \"followup_3\",\n", "    \"followup_4\",\n", "]\n", "\n", "\n", "for sample_idx in range(100):\n", "    for retrieval_name_a in RETRIEVAL_SETS:\n", "        retrieval_set_a = queryrewrites[\"processed_samples\"][str(sample_idx)][\n", "            \"retrievals\"\n", "        ][retrieval_name_a]\n", "        retrieval_chunks_a = queryrewrites[\"processed_samples\"][str(sample_idx)][\n", "            \"retrievals\"\n", "        ][retrieval_name_a + \"_serialized_chunks\"]\n", "        retrieval_chunks_a = deserialize_retrieved_chunks(retrieval_chunks_a)\n", "\n", "        silver_keywords = silver_labels[silver_label_name][sample_idx].keywords\n", "        silver_paths = silver_labels[silver_label_name][sample_idx].paths\n", "        silver_combined_paths = silver_labels[silver_label_name][\n", "            sample_idx\n", "        ].combined_paths\n", "\n", "        current_stats = {\n", "            \"silver_label_confidence\": silver_labels[silver_label_name][\n", "                sample_idx\n", "            ].confidence.value,\n", "            \"retrieval_set_pair\": f\"{retrieval_name_a}\",\n", "            \"gold_score\": (\n", "                keywords_mrr_scorer(\n", "                    retrieval_chunks_a,\n", "                    task.samples[sample_idx].keywords,\n", "                )\n", "            ),\n", "            f\"silver_{silver_label_name}_mrr_keywords\": (\n", "                keywords_mrr_scorer(\n", "                    retrieval_chunks_a,\n", "                    silver_keywords,\n", "                )\n", "            ),\n", "            f\"silver_{silver_label_name}_recall_keywords\": (\n", "                keywords_recall_scorer(\n", "                    retrieval_set_a,\n", "                    silver_keywords,\n", "                )\n", "            ),\n", "            f\"silver_{silver_label_name}_mrr_paths\": (\n", "                keywords_mrr_scorer(\n", "                    retrieval_chunks_a,\n", "                    silver_paths,\n", "                )\n", "            ),\n", "            f\"silver_{silver_label_name}_mrr_combined_paths\": (\n", "                keywords_mrr_scorer(\n", "                    retrieval_chunks_a,\n", "                    silver_combined_paths,\n", "                )\n", "            ),\n", "            f\"silver_{silver_label_name}_recall_paths\": (\n", "                keywords_recall_scorer(\n", "                    retrieval_set_a,\n", "                    silver_paths,\n", "                )\n", "            ),\n", "        }\n", "        current_stats[f\"silver_{silver_label_name}_recall_avg\"] = (\n", "            current_stats[f\"silver_{silver_label_name}_recall_keywords\"]\n", "            + current_stats[f\"silver_{silver_label_name}_recall_paths\"]\n", "        ) / 2\n", "        current_stats[f\"silver_{silver_label_name}_mrr_avg\"] = (\n", "            current_stats[f\"silver_{silver_label_name}_mrr_keywords\"]\n", "            + current_stats[f\"silver_{silver_label_name}_mrr_paths\"]\n", "        ) / 2\n", "        current_stats[f\"silver_{silver_label_name}_avg\"] = (\n", "            current_stats[f\"silver_{silver_label_name}_recall_avg\"]\n", "            + current_stats[f\"silver_{silver_label_name}_mrr_avg\"]\n", "        ) / 2\n", "        stats.append(current_stats)\n", "\n", "\n", "stats = pd.DataFrame(stats)\n", "stats.describe()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "\n", "plt.figure(figsize=(10, 6))\n", "plt.scatter(stats[\"gold_score\"], stats[\"silver_c120k_v2_mrr_paths\"], alpha=0.1)\n", "plt.xlabel(\"Gold Score\")\n", "plt.ylabel(\"Silver C129k V2 MRR Path\")\n", "plt.title(\"Gold Score (gold keywords MRR) vs Silver Score (silver paths MRR)\")\n", "plt.grid(True, linestyle=\"--\", alpha=0.7)\n", "# plt.xlim(0, 0.01)  # Set x-axis limits\n", "plt.ylim(0, 0.05)  # Set y-axis limits\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Downloading requests for i1-vanguard0 since 2024-01-01 to /mnt/efs/spark-data/shared/agentqa/v0/i1-vanguard0_since_20240101_to_20250303/requests_from_big_query.parquet\n"]}], "source": ["from pathlib import Path\n", "from datetime import datetime\n", "\n", "from base.datasets.tenants import (\n", "    DOGFOOD_SHARD,\n", "    VANGUARD,\n", "    VANGUARD_I1,\n", "    AITUTOR_MERCOR,\n", "    AITUTOR_TURING,\n", ")\n", "\n", "# TENANT = DOGFOOD_SHARD\n", "# TENANT = VANGUARD\n", "TENANT = VANGUARD_I1\n", "# TENANT = AITUTOR_MERCOR\n", "# TENANT = AITUTOR_TURING\n", "START_DATE = \"2024-01-01\"\n", "LIMIT = None\n", "\n", "today = datetime.today().strftime(\"%Y%m%d\")\n", "\n", "if LIMIT is None:\n", "    name = f\"{TENANT.name}_since_{START_DATE.replace('-', '')}_to_{today}\"\n", "else:\n", "    name = f\"{TENANT.name}_since_{START_DATE.replace('-', '')}_to_{today}_limit{LIMIT}\"\n", "\n", "OUTPUT_DIR = Path(\"/mnt/efs/spark-data/shared/agentqa/v0/\") / name\n", "OUTPUT_PATH = OUTPUT_DIR / \"requests_from_big_query.parquet\"\n", "\n", "OUTPUT_DIR.mkdir(parents=True, exist_ok=True)\n", "\n", "if OUTPUT_PATH.exists():\n", "    raise ValueError(f\"Output path {OUTPUT_PATH} already exists, skipping\")\n", "\n", "\n", "print(f\"Downloading requests for {TENANT.name} since {START_DATE} to {OUTPUT_PATH}\")"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Found 601465 allowed request IDs\n"]}], "source": ["from pathlib import Path\n", "import pandas as pd\n", "\n", "\n", "ALLOWED_RIDS_BASE_DIR = Path(\"/mnt/efs/spark-data/shared//vanguard/license_filter/permissive_rids/chat_host_request\")\n", "ALLOWED_RIDS_PATHS = list(ALLOWED_RIDS_BASE_DIR.glob(\"*.zstd.parquet\"))\n", "\n", "# Or read all parquet files into a single dataframe\n", "all_dfs = []\n", "for path in ALLOWED_RIDS_PATHS:\n", "    df = pd.read_parquet(path, engine='pyarrow')\n", "    all_dfs.append(df)\n", "\n", "# Combine all dataframes\n", "combined_df = pd.concat(all_dfs, ignore_index=True)\n", "\n", "allowed_rids = set(combined_df.request_id.unique())\n", "print(f\"Found {len(allowed_rids)} allowed request IDs\")"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["import dataclasses\n", "import pyarrow\n", "import pyarrow.parquet\n", "import tqdm\n", "from typing import Iterator\n", "\n", "from google.cloud import bigquery\n", "\n", "from base.datasets.gcp_creds import get_gcp_creds\n", "\n", "\n", "TENANT_NAME_TO_DATASET = {\n", "    \"dogfood-shard\": \"us_staging_request_insight_analytics_dataset\",\n", "    \"aitutor-mercor\": \"us_prod_request_insight_analytics_dataset\",\n", "    \"aitutor-turing\": \"us_prod_request_insight_analytics_dataset\",\n", "    \"i0-vanguard0\": \"us_prod_request_insight_analytics_dataset\",\n", "    \"i1-vanguard0\": \"us_prod_request_insight_analytics_dataset\",\n", "}\n", "\n", "PAGE_SIZE = 128\n", "\n", "\n", "@dataclasses.dataclass\n", "class BigQueryRequestMetadata:\n", "    tenant: str\n", "    user_id: str\n", "    timestamp: int\n", "    request_id: str\n", "\n", "\n", "def download_request_ids(\n", "    tenant, start_date, limit\n", ") -> Iterator[BigQueryRequestMetadata]:\n", "    query = f\"\"\"\\\n", "  SELECT\n", "    tenant, user_id, UNIX_MICROS(time) AS timestamp, request_id\n", "  FROM\n", "    `{TENANT_NAME_TO_DATASET[tenant.name]}.request_metadata`\n", "  WHERE\n", "    request_type = \"CHAT\"\n", "    AND tenant = \"{tenant.name}\"\n", "    AND time >= \"{start_date}\"\n", "    AND user_agent NOT IN (\"AugmentHealthCheck/0\", \"Augment-EvalHarness/0 (Regression Testing)\", \"api_proxy_client/0 (Python)\")\n", "    AND user_id NOT IN (\"health-check-1\", \"eval-determined-bot\")\n", "  ORDER BY tenant, user_id, time DESC\n", "  \"\"\"\n", "\n", "    if limit is not None:\n", "        query += f\"\\nLIMIT {limit}\"\n", "\n", "    gcp_creds, _ = get_gcp_creds(None)\n", "    bigquery_client = bigquery.Client(project=tenant.project_id, credentials=gcp_creds)\n", "\n", "    rows = bigquery_client.query_and_wait(query, page_size=PAGE_SIZE)\n", "    for row in rows:\n", "        if row.request_id in allowed_rids:\n", "          yield BigQueryRequestMetadata(\n", "              tenant=row.tenant,\n", "              user_id=row.user_id,\n", "              timestamp=row.timestamp,\n", "              request_id=row.request_id,\n", "          )"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["import contextlib\n", "import io\n", "import sys\n", "\n", "\n", "@contextlib.contextmanager\n", "def suppress_output():\n", "    \"\"\"Suppress stdout and stderr within a context.\n", "\n", "    Usage:\n", "        with suppress_output():\n", "            print(\"This will not be visible\")\n", "    \"\"\"\n", "    stdout, stderr = sys.stdout, sys.stderr\n", "    stream = io.StringIO()\n", "    sys.stdout = sys.stderr = stream\n", "    try:\n", "        yield\n", "    finally:\n", "        sys.stdout, sys.stderr = stdout, stderr\n", "\n", "\n", "with suppress_output():\n", "    print(\"This will not be visible\")"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["from enum import Enum\n", "\n", "from experimental.michiel.research.agentqa.retrieval import BlobGetter\n", "\n", "\n", "CHAT_THREAD_SUMMARY_MESSAGE = (\n", "    \"Please provide a clear and concise summary of our conversation so far.\"\n", ")\n", "\n", "XIAOLEI_EXPERIMENTS = \"Given the context below, answer this question:\"\n", "\n", "\n", "class FilterReason(str, Enum):\n", "    THREAD_SUMMARY = \"thread_summary\"\n", "    XIAOLEI_EXPERIMENTS = \"xiaolei_experiments\"\n", "    EMPTY_MESSAGE = \"empty_message\"\n", "    FAILED_TO_RETRIEVE_REQUEST = \"failed_to_retrieve_request\"\n", "    DUPLICATE_MESSAGE = \"duplicate_message\"\n", "\n", "\n", "@dataclasses.dataclass\n", "class RequestMetadata(BigQueryRequestMetadata):\n", "    message: str\n", "    path: str | None\n", "    n_blobs: int\n", "\n", "\n", "class Processor:\n", "    def __init__(self, tenant):\n", "        self.tenant = tenant\n", "\n", "    def initialize(self):\n", "        Processor.blob_getter = BlobGetter(self.tenant)\n", "\n", "    def __call__(self, request: BigQueryRequestMetadata):\n", "        with suppress_output():\n", "            chat_request = Processor.blob_getter.get_raw_chat_request(\n", "                request.request_id\n", "            )\n", "            if chat_request is None:\n", "                return None, FilterReason.FAILED_TO_RETRIEVE_REQUEST\n", "            message = chat_request.message\n", "            if len(message) == 0:\n", "                return None, FilterReason.EMPTY_MESSAGE\n", "            elif message.startswith(XIAOLEI_EXPERIMENTS):\n", "                return None, FilterReason.XIAOLEI_EXPERIMENTS\n", "            elif message.startswith(CHAT_THREAD_SUMMARY_MESSAGE):\n", "                return None, FilterReason.THREAD_SUMMARY\n", "            blob_names = Processor.blob_getter.get_blob_names_from_chat_request(\n", "                chat_request\n", "            )\n", "        request_metadata = RequestMetadata(\n", "            **dataclasses.asdict(request),\n", "            message=message,\n", "            path=chat_request.path,\n", "            n_blobs=len(blob_names),\n", "        )\n", "        return request_metadata, None"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["0it [00:00, ?it/s]"]}], "source": ["import dataclasses\n", "from collections import Counter\n", "\n", "from research.data.spark.pipelines.utils.safe_writer import SafeWriter\n", "from experimental.michiel.research.agentqa.data.utils import (\n", "    maybe_run_in_multiple_processes,\n", ")\n", "\n", "\n", "N_PROCESSES = 30\n", "\n", "writer = SafeWriter(str(OUTPUT_PATH))\n", "\n", "n_saved_requests = 0\n", "seen_requests = set()\n", "stats = Counter()\n", "requests = []\n", "\n", "\n", "def flush():\n", "    global writer\n", "    global requests\n", "    global n_saved_requests\n", "    if len(requests) > 0:\n", "        requests_dict = [dataclasses.asdict(request) for request in requests]\n", "        table_batch = pyarrow.Table.from_pylist(requests_dict).to_batches()[0]\n", "        writer.write(table_batch)\n", "        n_saved_requests += len(requests_dict)\n", "        requests = []\n", "\n", "\n", "data_iterator = tqdm.tqdm(\n", "    maybe_run_in_multiple_processes(\n", "        Processor(tenant=TENANT),\n", "        download_request_ids(TENANT, START_DATE, LIMIT),\n", "        num_processes=N_PROCESSES,\n", "    )\n", ")\n", "\n", "for request, filter_reason in data_iterator:\n", "    if request is None:\n", "        assert filter_reason is not None\n", "        stats[filter_reason] += 1\n", "        continue\n", "    else:\n", "        assert filter_reason is None\n", "\n", "    request_key = (request.path, request.message)\n", "    if request_key in seen_requests:\n", "        stats[FilterReason.DUPLICATE_MESSAGE] += 1\n", "        continue\n", "    else:\n", "        seen_requests.add(request_key)\n", "\n", "    requests.append(request)\n", "    stats[\"success\"] += 1\n", "    if len(requests) >= PAGE_SIZE:\n", "        flush()\n", "    data_iterator.set_postfix(stats)\n", "\n", "flush()\n", "writer.finalize()\n", "\n", "print(f\"Saved {n_saved_requests} regular requests\")\n", "\n", "print(\n", "    f\"Read back from {OUTPUT_PATH} {len(pyarrow.parquet.read_table(OUTPUT_PATH))} rows\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>user_id</th>\n", "      <th>tenant</th>\n", "      <th>timestamp</th>\n", "      <th>request_id</th>\n", "      <th>message</th>\n", "      <th>path</th>\n", "      <th>n_blobs</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>177</th>\n", "      <td>hfzunqvbtu1ayCK5/48J5Cbv9BN57W2ETalTvK5hbJ0=</td>\n", "      <td>3360</td>\n", "      <td>3360</td>\n", "      <td>3360</td>\n", "      <td>3360</td>\n", "      <td>3360</td>\n", "      <td>3360</td>\n", "    </tr>\n", "    <tr>\n", "      <th>112</th>\n", "      <td>SbVSnsbcZEwy39FsFnJmS5PX30TnVgmW0hE8dVv/H2w=</td>\n", "      <td>3100</td>\n", "      <td>3100</td>\n", "      <td>3100</td>\n", "      <td>3100</td>\n", "      <td>3100</td>\n", "      <td>3100</td>\n", "    </tr>\n", "    <tr>\n", "      <th>242</th>\n", "      <td>z/+9SpZ6MpYu7mVw7X+l2e467ONopiBrNwuTKW8mMMo=</td>\n", "      <td>2837</td>\n", "      <td>2837</td>\n", "      <td>2837</td>\n", "      <td>2837</td>\n", "      <td>2837</td>\n", "      <td>2837</td>\n", "    </tr>\n", "    <tr>\n", "      <th>246</th>\n", "      <td>zbU23LgcAkXhTzYLeJzVAYY0FzAzu6QGiKGnAm0+ggU=</td>\n", "      <td>2161</td>\n", "      <td>2161</td>\n", "      <td>2161</td>\n", "      <td>2161</td>\n", "      <td>2161</td>\n", "      <td>2161</td>\n", "    </tr>\n", "    <tr>\n", "      <th>181</th>\n", "      <td>iwCX6onAG+qCvc1IWvMdbD1VY1fbjn/LjvkweO9klKk=</td>\n", "      <td>2153</td>\n", "      <td>2153</td>\n", "      <td>2153</td>\n", "      <td>2153</td>\n", "      <td>2153</td>\n", "      <td>2153</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                          user_id  tenant  timestamp  \\\n", "177  hfzunqvbtu1ayCK5/48J5Cbv9BN57W2ETalTvK5hbJ0=    3360       3360   \n", "112  SbVSnsbcZEwy39FsFnJmS5PX30TnVgmW0hE8dVv/H2w=    3100       3100   \n", "242  z/+9SpZ6MpYu7mVw7X+l2e467ONopiBrNwuTKW8mMMo=    2837       2837   \n", "246  zbU23LgcAkXhTzYLeJzVAYY0FzAzu6QGiKGnAm0+ggU=    2161       2161   \n", "181  iwCX6onAG+qCvc1IWvMdbD1VY1fbjn/LjvkweO9klKk=    2153       2153   \n", "\n", "     request_id  message  path  n_blobs  \n", "177        3360     3360  3360     3360  \n", "112        3100     3100  3100     3100  \n", "242        2837     2837  2837     2837  \n", "246        2161     2161  2161     2161  \n", "181        2153     2153  2153     2153  "]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["data = pyarrow.parquet.read_table(OUTPUT_PATH)\n", "df = data.to_pandas()\n", "df.groupby(\"user_id\").count().reset_index().sort_values(\n", "    \"request_id\", ascending=False\n", ").head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Read back from /mnt/efs/spark-data/shared/agentqa/v0/i0-vanguard0_since_20240101_to_20250303/requests_from_big_query.parquet 45650 rows\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 600x400 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Summary statistics for n_blobs:\n", "count     45650.000000\n", "mean        973.593735\n", "std        4351.730637\n", "min           0.000000\n", "25%          52.000000\n", "50%         150.000000\n", "75%         673.000000\n", "max      143843.000000\n", "Name: n_blobs, dtype: float64\n"]}], "source": ["import matplotlib.pyplot as plt\n", "import numpy as np\n", "import pyarrow.parquet as pq\n", "\n", "table = pq.read_table(OUTPUT_PATH)\n", "df = table.to_pandas()\n", "print(f\"Read back from {OUTPUT_PATH} {len(df)} rows\")\n", "\n", "plt.figure(figsize=(6, 4))\n", "n_blobs = df[\"n_blobs\"][df[\"n_blobs\"] > 0]  # Filter out zeros for log scale\n", "plt.hist(\n", "    n_blobs, bins=np.logspace(np.log10(n_blobs.min()), np.log10(n_blobs.max()), 50)\n", ")\n", "plt.xscale(\"log\")\n", "plt.title(\"Distribution of Number of Blobs\")\n", "plt.xlabel(\"Number of blobs (log scale)\")\n", "plt.ylabel(\"Frequency\")\n", "plt.grid(True)\n", "plt.show()\n", "\n", "print(\"\\nSummary statistics for n_blobs:\")\n", "print(df[\"n_blobs\"].describe())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
import concurrent.futures
import contextlib
import io
import multiprocessing
import sys
from typing import Any, Callable, Iterator

from experimental.michiel.research.agentqa.lib.utils import interleave_sequences


def maybe_run_in_multiple_processes(
    processor_fn,
    inputs: Iterator[Any],
    num_processes: int,
) -> Iterator[Any]:
    """Run a processor in multiple processes if num_processes > 1."""
    assert num_processes > 0
    if num_processes == 1:
        processor_fn.initialize()
        yield from map(processor_fn, inputs)
    else:
        with multiprocessing.Pool(
            processes=num_processes, initializer=processor_fn.initialize
        ) as pool:
            yield from pool.imap_unordered(processor_fn, inputs)


def maybe_run_in_multiple_threads(
    processor_fn: Callable,
    inputs: Iterator[Any],
    num_threads: int,
) -> Iterator[Any]:
    """Run a processor in multiple threads if num_threads > 1."""
    assert num_threads > 0
    if num_threads == 1:
        yield from map(processor_fn, inputs)
    else:
        with concurrent.futures.ThreadPoolExecutor(max_workers=num_threads) as executor:
            futures = set()
            for item in inputs:
                if len(futures) >= num_threads:
                    done, futures = concurrent.futures.wait(
                        futures, return_when=concurrent.futures.FIRST_COMPLETED
                    )
                    for future in done:
                        yield future.result()
                futures.add(executor.submit(processor_fn, item))

            for future in concurrent.futures.as_completed(futures):
                yield future.result()


def extract_chunks(tool_result):
    list_of_lists_of_chunks = []
    for tool_output in tool_result.tool_output:
        list_of_lists_of_chunks.append(tool_output["retrieved_chunks"])
    return list(interleave_sequences(list_of_lists_of_chunks))


@contextlib.contextmanager
def suppress_all_output():
    """Suppress stdout and stderr within a context.

    Usage:
        with suppress_all_output():
            print("This will not be visible")
    """
    stdout, stderr = sys.stdout, sys.stderr
    stream = io.StringIO()
    sys.stdout = sys.stderr = stream
    try:
        yield
    finally:
        sys.stdout, sys.stderr = stdout, stderr


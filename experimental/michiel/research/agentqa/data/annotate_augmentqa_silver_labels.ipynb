{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.eval.harness.tasks.augment_qa_task import AugmentQATask\n", "\n", "task = AugmentQATask.from_yaml_config(\n", "    {\n", "        \"dataset_path\": \"/mnt/efs/augment/data/processed/augment_qa/v3\",\n", "        \"html_report_output_dir\": \"/mnt/efs/augment/public_html/michiel/augmentqa_v3\",\n", "    }\n", ")\n", "\n", "print(f\"There are {len(task)} examples in {task.name}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.eval.harness.factories import create_retriever\n", "\n", "\n", "RETRIEVER_TOP_K = 1024\n", "\n", "\n", "# load retriever\n", "retriever_config = {\n", "    \"scorer\": {\n", "        \"name\": \"dense_scorer_v2_ffwd\",\n", "        # \"checkpoint_path\": \"/mnt/efs/augment/user/igor/checkpoints/chatanol/chatanol1-18.hybrid/neox/global_step1468\",\n", "        \"checkpoint_path\": \"/home/<USER>/data/checkpoints/chatanol/chatanol1-18.hybrid/neox/global_step1468\",\n", "    },\n", "    \"chunker\": {\n", "        \"name\": \"smart_line_level\",\n", "        \"max_chunk_chars\": 768,\n", "        \"max_headers\": 3,\n", "    },\n", "    \"query_formatter\": {\n", "        \"name\": \"base:chatanol6-singleturnisspecial\",\n", "        \"tokenizer_name\": \"rogue\",\n", "        \"max_tokens\": 1024,\n", "    },\n", "    \"document_formatter\": {\n", "        \"name\": \"base:ethanol6-embedding-with-path-key\",\n", "        \"tokenizer_name\": \"rogue\",\n", "    },\n", "}\n", "\n", "\n", "retriever = create_retriever(retriever_config)\n", "retriever.load()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.eval.harness import utils\n", "from research.core.types import Document\n", "\n", "sample = task.samples[33]\n", "print(sample.message)\n", "\n", "repo_path = task.dataset_path / f\"repos/{sample.repo}.jsonl.zst\"\n", "docs = [Document(**d) for d in utils.read_jsonl_zst(repo_path)]\n", "\n", "print(f\"Loaded {len(docs)} files for {sample.repo}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["retriever.remove_all_docs()\n", "retriever.add_docs(docs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.third_party_clients.anthropic_vertexai_client import AnthropicVertexAiClient\n", "\n", "# See services/deploy/claude_sonnet_3_5_16k_chat_deploy.jsonnet\n", "REGION = \"us-east5\"\n", "PROJECT_ID = \"augment-387916\"\n", "# MODEL_NAME = \"claude-3-5-sonnet@20240620\"\n", "MODEL_NAME = \"claude-3-5-sonnet-v2@20241022\"\n", "TEMPERAURE = 0\n", "MAX_OUTPUT_TOKENS = 1024 * 8\n", "\n", "ANTHROPIC_CLIENT = AnthropicVertexAiClient(\n", "    project_id=PROJECT_ID,\n", "    region=REGION,\n", "    model_name=MODEL_NAME,\n", "    temperature=TEMPERAURE,\n", "    max_output_tokens=MAX_OUTPUT_TOKENS,\n", ")\n", "\n", "dialog = [\n", "    {\"role\": \"user\", \"content\": \"write a factorial function\"},\n", "]\n", "\n", "response = ANTHROPIC_CLIENT.client.messages.create(\n", "    model=MODEL_NAME,\n", "    max_tokens=MAX_OUTPUT_TOKENS,\n", "    messages=dialog,\n", "    temperature=TEMPERAURE,\n", ")\n", "response"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.prompt_format_chat.prompt_formatter import (\n", "    ChatTokenApportionment,\n", ")\n", "from base.prompt_format.common import Exchange, PromptChunk\n", "from base.prompt_format_chat.lib.token_counter_claude import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\n", "from base.prompt_format_chat.structured_binks_prompt_formatter import (\n", "    StructuredBinksPromptFormatter,\n", ")\n", "from base.prompt_format_chat.lib.system_prompts import (\n", "    get_claude_with_codeblocks_xml_system_prompt_formatter,\n", ")\n", "\n", "# See services/deploy/claude_sonnet_3_5_128k_v8_chat_deploy.jsonnet\n", "# as example of production token apportionment.\n", "\n", "# MODEL_NAME = \"claude-3-5-sonnet@20240620\"\n", "# OUTPUT_PATH = Path(\"/mnt/efs/augment/user/yury/agentqa/augmentqa_silver_c64k_v1.jsonl\")\n", "# 100%|██████████| 100/100 [40:02<00:00, 24.02s/it, keywords_recall_in_answer=0.615]\n", "# TOKEN_APPORTIONMENT = ChatTokenApportionment(\n", "#     prefix_len=1024 * 4,\n", "#     suffix_len=1024 * 4,\n", "#     path_len=256,\n", "#     message_len=-1,  # Deprecated field\n", "#     selected_code_len=-1,  # Deprecated field\n", "#     chat_history_len=1024 * 8,\n", "#     retrieval_len_per_each_user_guided_file=3000,\n", "#     retrieval_len_for_user_guided=8000,\n", "#     retrieval_len=-1,  # Fill the rest of the input prompt with retrievals\n", "#     max_prompt_len=1024 * 64,  # 64k for prompt\n", "#     inject_current_file_into_retrievals=True,\n", "# )\n", "\n", "# MODEL_NAME = \"claude-3-5-sonnet@20240620\"\n", "# OUTPUT_PATH = Path(\"/mnt/efs/augment/user/yury/agentqa/augmentqa_silver_c12k_v1.jsonl\")\n", "# 100%|██████████| 100/100 [21:04<00:00, 12.64s/it, keywords_recall_in_answer=0.695]\n", "# TOKEN_APPORTIONMENT = ChatTokenApportionment(\n", "#     prefix_len=1024 * 2,\n", "#     suffix_len=1024 * 2,\n", "#     path_len=256,\n", "#     message_len=-1,  # Deprecated field\n", "#     selected_code_len=-1,  # Deprecated field\n", "#     chat_history_len=1024 * 4,\n", "#     retrieval_len_per_each_user_guided_file=3000,\n", "#     retrieval_len_for_user_guided=8000,\n", "#     retrieval_len=-1,  # Fill the rest of the input prompt with retrievals\n", "#     max_prompt_len=1024 * 12,  # 12k for prompt\n", "#     inject_current_file_into_retrievals=True,\n", "# )\n", "\n", "# MODEL_NAME = \"claude-3-5-sonnet-v2@20241022\"\n", "# OUTPUT_PATH = Path(\"/mnt/efs/augment/user/yury/agentqa/augmentqa_silver_c120k_v2.jsonl\")\n", "# 100%|██████████| 100/100 [1:05:25<00:00, 39.26s/it, keywords_recall_in_answer=0.749]\n", "TOKEN_APPORTIONMENT = ChatTokenApportionment(\n", "    prefix_len=1024 * 4,\n", "    suffix_len=1024 * 4,\n", "    path_len=256,\n", "    message_len=-1,  # Deprecated field\n", "    selected_code_len=-1,  # Deprecated field\n", "    chat_history_len=1024 * 8,\n", "    retrieval_len_per_each_user_guided_file=3000,\n", "    retrieval_len_for_user_guided=8000,\n", "    retrieval_len=-1,  # Fill the rest of the input prompt with retrievals\n", "    max_prompt_len=1024 * 120,  # 64k for prompt\n", "    inject_current_file_into_retrievals=True,\n", ")\n", "\n", "\n", "# MODEL_NAME = \"claude-3-5-sonnet-v2@20241022\"\n", "# OUTPUT_PATH = Path(\"/mnt/efs/augment/user/yury/agentqa/augmentqa_silver_c64k_v2.jsonl\")\n", "# 100%|██████████| 100/100 [42:45<00:00, 25.66s/it, keywords_recall_in_answer=0.747]\n", "# TOKEN_APPORTIONMENT = ChatTokenApportionment(\n", "#     prefix_len=1024 * 4,\n", "#     suffix_len=1024 * 4,\n", "#     path_len=256,\n", "#     message_len=-1,  # Deprecated field\n", "#     selected_code_len=-1,  # Deprecated field\n", "#     chat_history_len=1024 * 8,\n", "#     retrieval_len_per_each_user_guided_file=3000,\n", "#     retrieval_len_for_user_guided=8000,\n", "#     retrieval_len=-1,  # Fill the rest of the input prompt with retrievals\n", "#     max_prompt_len=1024 * 64,  # 64k for prompt\n", "#     inject_current_file_into_retrievals=True,\n", "# )\n", "\n", "# MODEL_NAME = \"claude-3-5-sonnet-v2@20241022\"\n", "# OUTPUT_PATH = Path(\"/mnt/efs/augment/user/yury/agentqa/augmentqa_silver_c12k_v2.jsonl\")\n", "# 100%|██████████| 100/100 [21:35<00:00, 12.95s/it, keywords_recall_in_answer=0.732]\n", "# TOKEN_APPORTIONMENT = ChatTokenApportionment(\n", "#     prefix_len=1024 * 2,\n", "#     suffix_len=1024 * 2,\n", "#     path_len=256,\n", "#     message_len=-1,  # Deprecated field\n", "#     selected_code_len=-1,  # Deprecated field\n", "#     chat_history_len=1024 * 4,\n", "#     retrieval_len_per_each_user_guided_file=3000,\n", "#     retrieval_len_for_user_guided=8000,\n", "#     retrieval_len=-1,  # Fill the rest of the input prompt with retrievals\n", "#     max_prompt_len=1024 * 12,  # 12k for prompt\n", "#     inject_current_file_into_retrievals=True,\n", "# )\n", "\n", "TOKEN_COUNTER = ClaudeTokenCounter()\n", "\n", "\n", "PROMPT_FORMATTER = StructuredBinksPromptFormatter.create(\n", "    TOKEN_COUNTER,\n", "    TOKEN_APPORTIONMENT,\n", "    system_prompt_factory=get_claude_with_codeblocks_xml_system_prompt_formatter,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import copy\n", "import dataclasses\n", "import enum\n", "import re\n", "\n", "from base.prompt_format_retrieve.prompt_formatter import ChatRetrieverPromptInput\n", "from base.prompt_format_chat.legacy_binks.binks_llama3_prompt_formatter import (\n", "    DENSE_RETRIEVER_ORIGIN,\n", ")\n", "\n", "from research.core.chat_prompt_input import ResearchChatPromptInput\n", "from research.eval.harness.systems.abs_system import (\n", "    CompletionResult,\n", ")\n", "from research.eval.harness.tasks.augment_qa_task import score_response\n", "\n", "from experimental.michiel.research.agentqa.data.utils import suppress_all_output\n", "\n", "\n", "def add_retrieval(sample: ResearchChatPromptInput):\n", "    retriever_prompt_input = ChatRetrieverPromptInput(\n", "        prefix=sample.prefix,\n", "        suffix=sample.suffix,\n", "        path=sample.path,\n", "        message=sample.message,\n", "        selected_code=sample.selected_code,\n", "        chat_history=list(sample.chat_history),\n", "    )\n", "    with suppress_all_output():\n", "        retrieved_chunks, _ = retriever.query(\n", "            retriever_prompt_input,\n", "            doc_ids=sample.doc_ids,\n", "            top_k=RETRIEVER_TOP_K,\n", "        )\n", "    prompt_chunks = []\n", "    for chunk in retrieved_chunks:\n", "        path = \"\" if chunk.parent_doc.path is None else chunk.parent_doc.path\n", "        blob_name = \"\" if chunk.parent_doc.id is None else chunk.parent_doc.id\n", "        prompt_chunks.append(\n", "            PromptChunk(\n", "                text=chunk.text,\n", "                path=path,\n", "                unique_id=chunk.id,\n", "                origin=DENSE_RETRIEVER_ORIGIN,\n", "                blob_name=blob_name,\n", "                char_start=chunk.char_offset,\n", "                char_end=chunk.char_offset + chunk.length,\n", "            )\n", "        )\n", "    sample = dataclasses.replace(sample, retrieved_chunks=prompt_chunks)\n", "    return sample\n", "\n", "\n", "def run_claude(sample: ResearchChatPromptInput):\n", "    prompt_output = PROMPT_FORMATTER.format_prompt(sample)\n", "    messages = []\n", "    for exchange in prompt_output.chat_history:\n", "        messages.extend(\n", "            [\n", "                {\"role\": \"user\", \"content\": exchange.request_message},\n", "                {\"role\": \"assistant\", \"content\": exchange.response_text},\n", "            ]\n", "        )\n", "    messages.append({\"role\": \"user\", \"content\": prompt_output.message})\n", "    response = ANTHROPIC_CLIENT.client.messages.create(\n", "        model=MODEL_NAME,\n", "        max_tokens=MAX_OUTPUT_TOKENS,\n", "        messages=messages,\n", "        system=prompt_output.system_prompt,\n", "        temperature=TEMPERAURE,\n", "    )\n", "    if len(response.content) != 1:\n", "        raise ValueError(\n", "            f\"Expected 1 response, got {len(response.content)}: {response}\"\n", "        )\n", "    return response.content[0].text\n", "\n", "\n", "class ConfidenceLevel(enum.Enum):\n", "    BAD = \"bad\"\n", "    MEDIOCRE = \"mediocre\"\n", "    OK = \"ok\"\n", "    GOOD = \"good\"\n", "\n", "\n", "@dataclasses.dataclass(frozen=True)\n", "class Label:\n", "    answer: str\n", "    confidence: ConfidenceLevel\n", "    paths: list[str]\n", "    keywords: list[str]\n", "\n", "\n", "SILVER_LABEL_INSTRUCTIONS = \"\"\"\n", "Great! Now, let me follow up on your response.\n", "Please list three things:\n", "\n", "A self-assessment label of how confident you are that your answer is correct and comprehensive. Use exactly one of these labels: bad, mediocre, ok, good\n", "The full paths of files that were absolutely crucial for answering your question\n", "The technical keywords that represent the most essential core concepts of your answer. Be conservative - include only the technical terms, function names, or concepts without which the answer would be incomplete. File paths can be keywords too if the question was about locating something in the codebase. Important: keywords must be single words without spaces (use underscores or camelCase if needed, e.g., 'KLDivergence' not 'KL divergence'). For most questions, just 2-3 most critical keywords are enough. However, if the question was enumerative (e.g., \"list all authentication functions in the project\"), then your keywords should include all items from that enumeration.\n", "\n", "Format your response as exactly three lines:\n", "\n", "First line: confidence label\n", "Second line: comma-separated file paths\n", "Third line: comma-separated keywords\n", "\n", "Example format:\n", "good\n", "src/auth/login.py, tests/auth/test_login.py\n", "<PERSON><PERSON><PERSON><PERSON><PERSON>, authenticate_user, JWT_TOKEN\n", "\"\"\"\n", "\n", "\n", "def parse_model_response(response: str) -> tuple[ConfidenceLevel, list[str], list[str]]:\n", "    # Split into lines and handle potential empty input\n", "    lines = response.strip().split(\"\\n\")\n", "    if len(lines) != 3:\n", "        raise ValueError(\"Response must contain exactly three lines\")\n", "\n", "    # Parse confidence level\n", "    confidence = ConfidenceLevel(lines[0].strip().lower())\n", "\n", "    # Parse paths and keywords, stripping whitespace\n", "    paths = [p.strip() for p in lines[1].split(\",\") if p.strip()]\n", "    keywords = [k.strip() for k in lines[2].split(\",\") if k.strip()]\n", "\n", "    return confidence, paths, keywords\n", "\n", "\n", "def annotate_silver_labels(sample: ResearchChatPromptInput):\n", "    sample = add_retrieval(sample)\n", "\n", "    answer_response = run_claude(sample)\n", "    chat_history = sample.chat_history.copy()\n", "    chat_history.append(\n", "        Exchange(request_message=sample.message, response_text=answer_response)\n", "    )\n", "    sample = dataclasses.replace(sample, chat_history=chat_history)\n", "\n", "    sample = dataclasses.replace(sample, message=SILVER_LABEL_INSTRUCTIONS)\n", "\n", "    label_response = run_claude(sample)\n", "    confidence, paths, keywords = parse_model_response(label_response)\n", "\n", "    label = Label(\n", "        answer=answer_response, confidence=confidence, paths=paths, keywords=keywords\n", "    )\n", "    return label\n", "\n", "\n", "for index in range(0):\n", "    sample = task.samples[index]\n", "    print(f\"Sample {index}: {sample.message}\")\n", "\n", "    response = annotate_silver_labels(sample)\n", "\n", "    score_silver_answer = score_response(\n", "        sample=sample,\n", "        response=CompletionResult(\n", "            generated_text=response.answer,\n", "            prompt_tokens=[],\n", "            retrieved_chunks=[],\n", "        ),\n", "    )\n", "    keywords_recall_silver_answer = score_silver_answer.metrics[\n", "        \"keywords recall in answer\"\n", "    ]\n", "    print(f\"                 Self-confidence: {response.confidence}\")\n", "    print(f\"Keywords recall in silver answer: {keywords_recall_silver_answer}\")\n", "\n", "    print(f\"     Gold paths: {sorted(sample.gold_paths)}\")\n", "    print(f\"   Silver paths: {sorted(response.paths)}\")\n", "    print(f\"  Gold keywords: {sorted(sample.keywords)}\")\n", "    print(f\"Silver keywords: {sorted(response.keywords)}\")\n", "    print()\n", "    print(response.answer)\n", "    print()\n", "    print(\"-\" * 10)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import dataclasses\n", "import json\n", "from pathlib import Path\n", "import tqdm\n", "\n", "\n", "class EnumEncoder(json.J<PERSON>ncoder):\n", "    def default(self, o):\n", "        if isinstance(o, enum.Enum):\n", "            return o.value\n", "        return super().default(o)\n", "\n", "\n", "OUTPUT_PATH = Path(\"/mnt/efs/augment/user/yury/agentqa/augmentqa_silver_c120k_v2.jsonl\")\n", "\n", "stats = []\n", "\n", "with OUTPUT_PATH.open(\"w\") as f:\n", "    pbar = tqdm.tqdm(task.samples, total=len(task))\n", "    for sample in pbar:\n", "        response = annotate_silver_labels(sample)\n", "        score_silver_answer = score_response(\n", "            sample=sample,\n", "            response=CompletionResult(\n", "                generated_text=response.answer,\n", "                prompt_tokens=[],\n", "                retrieved_chunks=[],\n", "            ),\n", "        )\n", "        stats.append(score_silver_answer.metrics[\"keywords recall in answer\"])\n", "        json.dump(dataclasses.asdict(response), f, cls=EnumEncoder)\n", "        f.write(\"\\n\")\n", "        pbar.set_postfix(keywords_recall_in_answer=sum(stats) / len(stats))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
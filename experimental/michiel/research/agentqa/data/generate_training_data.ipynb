{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "\n", "# VANGUARD_DATA_PATH = \"/mnt/efs/spark-data/shared/agentqa/v0/i0-vanguard0_since_20240101_queryrouterlabels.jsonl\"\n", "# DOGFOOD_DATA_PATH = \"/mnt/efs/spark-data/shared/agentqa/v0/dogfood-shard_since_20241001_queryrouterlabels_limit1024.jsonl\"\n", "# VANGUARD_DATA_PATH = \"/mnt/efs/spark-data/shared/agentqa/v0/i0-vanguard0_since_20240101/queryrouterlabels.jsonl\"\n", "# VANGUARD_DATA_PATH = \"/mnt/efs/spark-data/shared/agentqa/v0/i-vanguard0_since_20240101/queryrouterlabels_v4.jsonl\"\n", "VANGUARD_DATA_PATH = \"/mnt/efs/spark-data/shared/agentqa/v0/i0-vanguard0_since_20240101_to_20250303/queryrouterlabels_v4.jsonl\"\n", "\n", "\n", "OUTPUT_DIR = Path(\n", "    \"/mnt/efs/spark-data/shared/agentqa/v0/i0-vanguard0_since_20240101_to_20250303/bin_v4\"\n", ")\n", "OUTPUT_DIR.mkdir(parents=True, exist_ok=True)\n", "\n", "MAX_PROMPT_LENGTH = 1024 * 12\n", "MAX_LENGTH = 16384 + 1\n", "N_EVAL_SAMPLES = 256"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import contextlib\n", "import io\n", "import sys\n", "from IPython.utils.capture import capture_output\n", "\n", "\n", "@contextlib.contextmanager\n", "def suppress_output():\n", "    \"\"\"Suppress stdout and stderr within a context.\n", "\n", "    Usage:\n", "        with suppress_output():\n", "            print(\"This will not be visible\")\n", "    \"\"\"\n", "    with capture_output() as captured:\n", "        yield captured\n", "\n", "\n", "# Test it\n", "print(\"This should be visible\")\n", "with suppress_output():\n", "    print(\"This will not be visible\")\n", "print(\"This should be visible too\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "from experimental.michiel.research.agentqa.tools import <PERSON><PERSON>, GeneralQueryRouter\n", "\n", "PATH = \"/home/<USER>/augment/services/integrations/docset/server/docsets_dump.json\"\n", "\n", "with open(PATH, \"r\") as f:\n", "    docsets_dict = json.load(f)\n", "\n", "\n", "DOCSETS = []\n", "for docset in docsets_dict:\n", "    docset_id = docset[\"docset_id\"].split(\"://\")[1]\n", "    DOCSETS.append(docset_id)\n", "\n", "print(f\"Found {len(DOCSETS)} docsets: {DOCSETS[:5]}, ...\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "\n", "from base.prompt_format_chat.prompt_formatter import (\n", "    ChatTokenApportionment,\n", ")\n", "from base.tokenizers.qwen25coder_tokenizer import Qwen25CoderTokenizer\n", "from base.prompt_format_router.pleasehold_prompt_formatter import (\n", "    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n", ")\n", "\n", "CATEGORY_LABELS = {\n", "    \"general\": 0,\n", "    \"edit\": 1,\n", "    \"current_file\": 2,\n", "    \"codebase\": 3,\n", "    \"overview\": 4,\n", "}\n", "\n", "\n", "def get_prompt_formatter(\n", "    tokenizer: Qwen25CoderTokenizer, available_docsets: list[str], target_docset: str\n", ") -> PleaseHoldPromptFormatter:\n", "    token_apportionment = ChatTokenApportionment(\n", "        prefix_len=1024 * 2,\n", "        suffix_len=1024 * 2,\n", "        path_len=256,\n", "        message_len=-1,  # Deprecated field\n", "        selected_code_len=-1,  # Deprecated field\n", "        chat_history_len=1024 * 4,\n", "        retrieval_len_per_each_user_guided_file=3000,\n", "        retrieval_len_for_user_guided=8000,\n", "        retrieval_len=-1,  # Fill the rest of the input prompt with retrievals\n", "        max_prompt_len=1024 * 12,  # 12k for prompt\n", "        inject_current_file_into_retrievals=True,\n", "        tool_results_len=1024 * 2,  # 2k for tool results\n", "    )\n", "\n", "    n_docsets_to_pick = np.random.randint(1, len(available_docsets) + 1)\n", "    docsets = np.random.choice(available_docsets, n_docsets_to_pick, replace=False)\n", "    docsets = list(docsets)\n", "    if target_docset != \"none\" and target_docset not in docsets:\n", "        docsets = docsets[:-1] + [target_docset]\n", "    np.random.shuffle(docsets)\n", "\n", "    return PleaseHoldPromptFormatter(\n", "        tokenizer=tokenizer,\n", "        token_apportionment=token_apportionment,\n", "        docsets=docsets,\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import torch\n", "from base.prompt_format_chat.prompt_formatter import ExceedContextLength\n", "from research.agentic_retrieval.router.common import QueryRouterResult\n", "\n", "\n", "# https://huggingface.co/Qwen/Qwen2.5-Coder-32B-Instruct/blob/main/tokenizer_config.json\n", "im_start_id = 151644\n", "im_end_id = 151645\n", "tool_start_id = 151657\n", "tool_end_id = 151658\n", "pad_token_id = 151643\n", "\n", "MASKED_ZERO_TOKEN = -(2**31)\n", "\n", "\n", "def format_label(tokenizer, query_router_response: QueryRouterResult):\n", "    # Fix about_specific_file\n", "    about_specific_file = query_router_response.about_specific_file\n", "    try:\n", "        if isinstance(about_specific_file, str):\n", "            about_specific_file = json.loads(about_specific_file.replace(\"\\\\\", \"\\\\\\\\\"))\n", "    except json.JSONDecodeError:\n", "        # print(f\"Failed to parse about_specific_file: {about_specific_file}\")\n", "        return None\n", "    assert isinstance(about_specific_file, list)\n", "    about_specific_file = [\n", "        path[1:] if path.startswith(\"/\") else path for path in about_specific_file\n", "    ]\n", "\n", "    label_str = f\"\"\"\\\n", "{CATEGORY_LABELS[query_router_response.category]}\n", "{\",\".join(about_specific_file)}\n", "{query_router_response.external_package}\n", "\"\"\"\n", "    label = tokenizer.tokenize_safe(label_str)\n", "    label += [im_end_id]\n", "    return label\n", "\n", "\n", "def mask_prompt_tokens(prompt_tokens):\n", "    return [-token if token > 0 else MASKED_ZERO_TOKEN for token in prompt_tokens]\n", "\n", "\n", "def add_padding(tokens, max_seq_len):\n", "    assert pad_token_id > 0\n", "    if len(tokens) < max_seq_len:\n", "        return tokens + [-pad_token_id] * (max_seq_len - len(tokens))\n", "    else:\n", "        return tokens\n", "\n", "\n", "def formal_sample(\n", "    prompt_formatter, tokenizer, chat_prompt_input, query_router_response\n", "):\n", "    prompt_output = prompt_formatter.format_prompt(chat_prompt_input)\n", "    prompt_tokens = prompt_output.tokens\n", "    masked_prompt_tokens = mask_prompt_tokens(prompt_tokens)\n", "    label_tokens = format_label(tokenizer, query_router_response)\n", "    if label_tokens is None:\n", "        return None\n", "    tokens = masked_prompt_tokens + label_tokens\n", "    if len(tokens) > MAX_LENGTH:\n", "        raise ExceedContextLength()\n", "    tokens = add_padding(tokens, MAX_LENGTH)\n", "    assert len(tokens) == MAX_LENGTH\n", "    tokens = torch.tensor(tokens, dtype=torch.int32)\n", "    return tokens\n", "\n", "\n", "def unmask_tokens(tokens):\n", "    unmasked = []\n", "    for token in tokens:\n", "        if token == MASKED_ZERO_TOKEN:\n", "            unmasked.append(0)\n", "        else:\n", "            unmasked.append(abs(token))\n", "    return unmasked\n", "\n", "\n", "# sample = annotated_data[6]\n", "# print(sample[\"label\"])\n", "# prompt_formatter = get_prompt_formatter(TOKENIZER, sample[\"label\"].external_package)\n", "# tokens = formal_sample(\n", "#     prompt_formatter,\n", "#     TOKENIZER,\n", "#     sample[\"chat_prompt_input\"],\n", "#     sample[\"label\"],\n", "# )\n", "# print(detokenize(tokens.tolist()))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from typing import Any\n", "import tqdm\n", "\n", "from experimental.michiel.research.agentqa.data.utils import (\n", "    maybe_run_in_multiple_processes,\n", ")\n", "\n", "import dataclasses\n", "import json\n", "\n", "from base.prompt_format.common import Exchange, PromptChunk\n", "from base.datasets.tenants import VANGUARD, VANGUARD_I1\n", "from research.tools.chat_replay.replay_utils import BlobGetter\n", "from research.core.chat_prompt_input import ResearchChatPromptInput\n", "\n", "from research.agentic_retrieval.router.common import QueryRouterResult\n", "\n", "\n", "def iterate_over_annotated_data(path):\n", "    with open(path) as f:\n", "        for line in tqdm.tqdm(f):\n", "            try:\n", "                yield json.loads(line)\n", "            except json.JSONDecodeError:\n", "                # Skip invalid lines\n", "                continue\n", "\n", "\n", "class Processor:\n", "    def __init__(self, tenant, docsets):\n", "        self.tenant = tenant\n", "        self.docsets = docsets\n", "\n", "    def initialize(self):\n", "        Processor.tokenizer = Qwen25CoderTokenizer()\n", "        Processor.blob_getter = BlobGetter(self.tenant)\n", "        Processor.available_docsets = self.docsets\n", "\n", "    def __call__(self, sample: dict[str, Any]) -> torch.Tensor | None:\n", "        request_id = sample[\"request_id\"]\n", "\n", "        with suppress_output():\n", "            chat_input: ResearchChatPromptInput | None = (\n", "                Processor.blob_getter.get_chat_request(\n", "                    request_id,\n", "                    raise_on_multiple=False,\n", "                )\n", "            )\n", "            assert chat_input is not None\n", "\n", "        retrieved_chunks = [\n", "            PromptChunk(**chunk) for chunk in sample[\"chat_request\"][\"retrieved_chunks\"]\n", "        ]\n", "        chat_input_with_retrievals = dataclasses.replace(\n", "            chat_input, retrieved_chunks=retrieved_chunks\n", "        )\n", "        label = QueryRouterResult(**sample[\"query_router_response\"])\n", "        prompt_formatter = get_prompt_formatter(\n", "            Processor.tokenizer,\n", "            Processor.available_docsets,\n", "            label.external_package,\n", "        )\n", "        try:\n", "            return formal_sample(\n", "                prompt_formatter,\n", "                Processor.tokenizer,\n", "                chat_input_with_retrievals,\n", "                label,\n", "            )\n", "        except ExceedContextLength:\n", "            return None\n", "\n", "\n", "N_PROCESSES = 30\n", "\n", "\n", "data = []\n", "n_failed = 0\n", "pbar = tqdm.tqdm(\n", "    maybe_run_in_multiple_processes(\n", "        Processor(VANGUARD, DOCSETS),\n", "        iterate_over_annotated_data(VANGUARD_DATA_PATH),\n", "        num_processes=N_PROCESSES,\n", "    ),\n", ")\n", "for tokens in pbar:\n", "    if tokens is None:\n", "        n_failed += 1\n", "        pbar.set_postfix({\"n_failed\": n_failed})\n", "    else:\n", "        data.append(tokens)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import tqdm\n", "\n", "from megatron.data.indexed_dataset import MMapIndexedDatasetBuilder\n", "from experimental.michiel.research.agentqa.data.utils import (\n", "    maybe_run_in_multiple_processes,\n", ")\n", "\n", "\n", "np.random.seed(31415)\n", "np.random.shuffle(data)\n", "\n", "dataset_dict = {\n", "    \"train\": data[N_EVAL_SAMPLES:],\n", "    \"valid\": data[:N_EVAL_SAMPLES],\n", "}\n", "\n", "\n", "for dataset_name, dataset in dataset_dict.items():\n", "    builder = MMapIndexedDatasetBuilder(\n", "        str(OUTPUT_DIR / dataset_name) + \".bin\", dtype=np.int32\n", "    )\n", "    for tokens in tqdm.tqdm(dataset, total=len(dataset), desc=dataset_name):\n", "        builder.add_item(tokens)\n", "        builder.end_document()\n", "    builder.finalize(str(OUTPUT_DIR / dataset_name) + \".idx\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "from megatron.data import indexed_dataset\n", "from colorama import Fore, Style\n", "\n", "from research.utils.inspect_indexed_dataset import highlight_special_tokens\n", "\n", "path = str(OUTPUT_DIR / \"valid\")\n", "\n", "ds = indexed_dataset.make_dataset(path, impl=\"mmap\", skip_warmup=True)\n", "print(len(ds))\n", "\n", "tokenizer = Qwen25CoderTokenizer()\n", "\n", "\n", "def detokenize(tokens):\n", "    return tokenizer.detokenize(unmask_tokens(tokens))\n", "\n", "\n", "# print(highlight_special_tokens(detokenize(ds[8]), tokenizer.special_tokens))\n", "print(detok<PERSON>ze(ds[8]))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
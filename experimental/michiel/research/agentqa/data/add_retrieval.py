import json
import logging
import os
import pickle
import time
from dataclasses import asdict, dataclass
from typing import Callable, Optional, Sequence

import torch
from pyspark.sql import functions as F

from base.datasets.tenants import get_tenant
from base.prompt_format_chat.prompt_formatter import ChatPromptInput
from base.prompt_format_retrieve.prompt_formatter import ChatRetrieverPromptInput
from experimental.michiel.research.agentqa.retrieval import BlobGetter
from research.core.types import Document
from research.core.utils_for_log import time_string
from research.data.rag import common as rag_common
from research.data.spark import get_local_session, k8s_session
from research.data.spark.pipelines.utils import map_parquet
from research.retrieval.retrieval_database import RetrievalDatabase

logger = logging.getLogger(__name__)


def get_spark_gpu(name: str, test: bool = False):
    if test:
        spark = get_local_session()

    else:
        spark = k8s_session(
            name=name,
            max_workers=128,
            conf={
                "spark.executor.pyspark.memory": "1000G",
                "spark.executor.memory": "80G",
                "spark.sql.parquet.columnarReaderBatchSize": "64",
                "spark.task.cpus": "5",
            },
            gpu_type="h100",
            skip_bazel_build=False,
        )
    return spark


def get_spark_cpu(name: str, test: bool = False):
    if test:
        spark = get_local_session()

    else:
        spark = k8s_session(
            name=name,
            max_workers=128,
            conf={
                "spark.executor.pyspark.memory": "64G",
                "spark.executor.memory": "32G",
                "spark.sql.parquet.columnarReaderBatchSize": "32",
                "spark.task.cpus": "2",
            },
        )
    return spark


@dataclass
class AddRetrievalConfig:
    """Config for retrieval stage."""

    input_path: str
    output_root: str
    random_seed: int
    num_retrieved_chunks: int
    retriever_config: dict
    tenant_list: Sequence[str]
    limit: Optional[int] = None
    max_n_blobs: Optional[int] = None


def sort_stage(config: AddRetrievalConfig, test: bool = False):
    """Sort samples by tenant, user and timestamp."""
    input_path = config.input_path
    output_path = config.output_root + "/sorted_samples/"
    print(f"{time_string()} Sorting samples...")
    print(f"Input: {input_path}")
    print(f"Output: {output_path}")

    spark_cpu = get_spark_cpu(name="sort-stage", test=test)
    parquet_files = map_parquet.list_files(
        spark_cpu, input_path, suffix="parquet", include_path=True, recursive=False
    )

    read_df = spark_cpu.read.parquet(*parquet_files)
    count = read_df.count()
    print(f"{time_string()} Dataset has {count} rows.")

    # Filter out samples with too many blobs
    if config.max_n_blobs is not None:
        read_df = read_df.filter(F.col("n_blobs") <= config.max_n_blobs)
        count = read_df.count()
        print(f"{time_string()} Limited to {count} rows after filtering on n_blobs.")

    read_df = read_df.orderBy("tenant", "user_id", "timestamp")
    read_df = read_df.withColumn("monotonic_id", F.monotonically_increasing_id())
    if config.limit is not None:
        read_df = read_df.limit(config.limit)
        count = read_df.count()
        print(f"{time_string()} Limiting to {count} rows.")

    num_partitions = max(count // 128, 10)
    num_partitions = min(num_partitions, 100)

    # Calculate the partition boundaries
    read_df = read_df.withColumn(
        "partition_id", F.floor(F.col("monotonic_id") / F.lit(count / num_partitions))
    )

    # Repartition using the calculated partition_id
    read_df = read_df.repartition(num_partitions, "partition_id")

    # Remove the temporary columns
    read_df = read_df.drop("monotonic_id", "partition_id")
    read_df.write.parquet(output_path)
    spark_cpu.stop()
    print(f"{time_string()} Sorted samples saved to {output_path}.")
    config_dict = asdict(config)
    config_dict["timestamp"] = time_string()
    config_file_path = os.path.join(output_path, "config.json")
    with open(config_file_path, "w") as f:
        f.write(json.dumps(config_dict, indent=2))
    print(f"{time_string()} Config saved to {config_file_path}.")


class SampleFetcher:
    def __init__(self, tenant_list: Sequence[str]):
        self.tenant_list = tenant_list
        self.fetchers = {tenant_name: None for tenant_name in tenant_list}

    def _lazy_init_fetchers(self):
        """Loads the fetchers."""
        for tenant_name in self.tenant_list:
            if self.fetchers[tenant_name] is None:
                tenant = get_tenant(tenant_name)
                self.fetchers[tenant_name] = BlobGetter(tenant)

    @map_parquet.passthrough_feature(bound=True)
    @map_parquet.allow_unused_args(bound=True)
    def __call__(self, request_id: str, tenant: str):
        start_time = time.time()
        self._lazy_init_fetchers()
        fetcher = self.fetchers[tenant]
        assert fetcher is not None
        chat_prompt_input = fetcher.get_chat_request_without_retrievals(request_id)
        documents = fetcher.get_blobs_from_request_id(request_id)
        end_time = time.time()
        print(f"SampleFetcher call took {end_time - start_time:.2f} seconds")
        return {
            "documents": documents,
            "chat_prompt_input": chat_prompt_input,
        }


class GenerateRetrievedChunksFromChat:
    """A class to generate retrieved chunks from chat problems."""

    def __init__(
        self,
        retrieval_database_factory: Callable[[], RetrievalDatabase],
        num_retrieved_chunks: int,
        max_failures: int = 100,
    ):
        self.retrieval_database_factory = retrieval_database_factory
        self._ret_db = None
        self.num_retrieved_chunks = num_retrieved_chunks
        self.max_failures = max_failures

    def _lazy_init_ret_db(self):
        """Loads the retrieval database."""
        if self._ret_db is None:
            self._ret_db = self.retrieval_database_factory()
            self._ret_db.load()

    @map_parquet.allow_unused_args(bound=True)
    def __call__(
        self,
        chat_prompt_input: ChatPromptInput,
        documents: Sequence[Document],
        request_id: str,
        tenant: str,
        user_id: str,
        timestamp: str,
    ) -> dict:
        """Add retrieval for chat sample.

        Args:

        Returns:
            serialized_chunks: A list of retrieved chunks.
        """
        start_time = time.time()
        self._lazy_init_ret_db()

        # Step 1: Index files that are not already in database, remove old files.
        assert self._ret_db is not None
        sample_doc_ids = {doc.id for doc in documents}
        database_doc_ids = self._ret_db.get_doc_ids()

        # Compute documents to be removed
        ids_to_remove = [
            doc_id for doc_id in database_doc_ids if doc_id not in sample_doc_ids
        ]

        # Compute documents to be added
        docs_to_add = [doc for doc in documents if doc.id not in database_doc_ids]

        # Remove old documents
        remove_time = time.time()
        self._ret_db.remove_docs(ids_to_remove)
        print(f"Removed {len(ids_to_remove)} files in {time.time() - remove_time:.2f} sec.")  # fmt: off
        print(f"GPU Memory Usage: {torch.cuda.memory_allocated() / 1e9:.2f} GB.")

        # Add new documents
        add_time = time.time()
        self._ret_db.add_docs(docs_to_add)
        print(f"Indexed {len(docs_to_add)} files in {time.time() - add_time:.2f} sec.")  # fmt: off
        print(f"GPU Memory Usage: {torch.cuda.memory_allocated() / 1e9:.2f} GB.")

        model_input = ChatRetrieverPromptInput(
            prefix="",
            suffix="",
            path=chat_prompt_input.path,
            message=chat_prompt_input.message,
            selected_code="",
            chat_history=list(chat_prompt_input.chat_history),
        )

        assert self._ret_db is not None
        retrieval_time = time.time()
        retrieved_chunks = self._ret_db.query(
            model_input=model_input,
            top_k=self.num_retrieved_chunks,
        )[0]
        print(f"Retrieved {len(retrieved_chunks)} chunks in {time.time() - retrieval_time:.2f} sec.")  # fmt: off

        serialized_chunks = rag_common.serialize_retrieved_chunks(retrieved_chunks)
        end_time = time.time()
        print(
            f"GenerateRetrievedChunksFromChat call took {end_time - start_time:.2f} seconds"
        )
        return {
            "serialized_chunks": serialized_chunks,
            "serialized_chat_prompt_input": pickle.dumps(chat_prompt_input),
            "request_id": request_id,
            "tenant": tenant,
            "user_id": user_id,
            "timestamp": timestamp,
        }


def retrieval_stage(
    config: AddRetrievalConfig, test: bool = False, first_stage: bool = False
):
    """Generate retrieval samples.

    Args:
        config: The config for the pipeline.
        test: Whether to use a smaller cluster.
        first_stage: Whether this is the first stage called in this run of the pipeline.
          Used to determine whether to read the input from different folder.
    """

    sample_fetcher = SampleFetcher(config.tenant_list)

    def _create_retriever(config) -> RetrievalDatabase:
        from research.eval.harness.factories import create_retriever

        retrieval_database = create_retriever(config)
        assert isinstance(retrieval_database, RetrievalDatabase)
        return retrieval_database

    retriever_config = config.retriever_config
    retriever_factory = lambda: _create_retriever(retriever_config)  # noqa: E731
    retriever_instance = GenerateRetrievedChunksFromChat(
        retrieval_database_factory=retriever_factory,
        num_retrieved_chunks=config.num_retrieved_chunks,
    )

    input_path = config.output_root + "/sorted_samples/"
    output_path = config.output_root + "/samples_with_retrieval/"

    print(f"{time_string()} Generating retrieval samples...")
    print(f"Input: {input_path}")
    print(f"Output: {output_path}")

    spark_gpu = get_spark_gpu(name="retrieval-stage", test=test)
    result = map_parquet.apply_pandas(
        spark_gpu,
        map_parquet.chain_processors(
            [
                sample_fetcher,
                retriever_instance,
            ]
        ),
        input_path=input_path,
        output_path=output_path,
        timeout=10 * 3600,  # 10 hour timeout
        batch_size=32,
        task_info_location=os.path.join(output_path, "task_info"),
        ignore_error=True if not test else False,
        allow_resume=True if not test else False,
        batch_timeout=5 * 3600,  # 5 hour timeout per batch
    )
    spark_gpu.stop()
    print(result["status_count"])
    print(result["task_info"]["stderr"][0])

    config_dict = asdict(config)
    config_dict["result"] = dict(status_count=result["status_count"])
    config_dict["timestamp"] = time_string()
    config_file_path = os.path.join(output_path, "config.json")
    with open(config_file_path, "w") as f:
        f.write(json.dumps(config_dict, indent=2))
    print(f"{time_string()} Config saved to {config_file_path}.")


CHATANOL_CONFIG = {
    "scorer": {
        "name": "dense_scorer_v2_ffwd",
        "checkpoint_path": "/mnt/efs/augment/user/igor/checkpoints/chatanol/chatanol1-18.hybrid/neox/global_step1468",
    },
    "chunker": {
        "name": "smart_line_level",
        "max_chunk_chars": 768,
        "max_headers": 3,
    },
    "query_formatter": {
        "name": "base:chatanol6-singleturnisspecial",
        "tokenizer_name": "rogue",
        "max_tokens": 1024,
    },
    "document_formatter": {
        "name": "base:ethanol6-embedding-with-path-key",
        "tokenizer_name": "rogue",
    },
}


ADD_RETRIEVAL_CONFIG = AddRetrievalConfig(
    input_path="",
    output_root="",
    random_seed=42,
    num_retrieved_chunks=100,
    retriever_config=CHATANOL_CONFIG,
    tenant_list=["dogfood-shard", "aitutor-turing", "aitutor-mercor", "i0-vanguard0", "i1-vanguard0"],
)

STAGE_DICT = {
    "sort": sort_stage,
    "retrieval": retrieval_stage,
}


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--input_path",
        "-i",
        type=str,
        help="The input path.",
    )
    parser.add_argument(
        "--output_root",
        "-o",
        type=str,
        help="The root for outputs.",
    )
    parser.add_argument(
        "--stages",
        "-s",
        type=str,
        help="The stages to run.",
        nargs="+",
        default=["sort", "retrieval"],
    )
    parser.add_argument(
        "--test",
        "-t",
        action="store_true",
        help="Whether to use a smaller cluster.",
    )
    parser.add_argument(
        "--limit",
        "-l",
        type=int,
        help="The limit for the number of samples.",
    )
    parser.add_argument(
        "--max_n_blobs",
        "-m",
        type=int,
        help="The maximum number of blobs per sample.",
    )
    args = parser.parse_args()

    if args.input_path is not None:
        ADD_RETRIEVAL_CONFIG.input_path = args.input_path

    if args.output_root is not None:
        ADD_RETRIEVAL_CONFIG.output_root = args.output_root

    if args.limit is not None:
        ADD_RETRIEVAL_CONFIG.limit = args.limit

    if args.max_n_blobs is not None:
        ADD_RETRIEVAL_CONFIG.max_n_blobs = args.max_n_blobs

    print(f"Running stages: {args.stages}")

    for stage in args.stages:
        STAGE_DICT[stage](config=ADD_RETRIEVAL_CONFIG, test=args.test)

{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "\n", "from research.models.fastforward_llama_models import (\n", "    FastForwardQwen25Coder_7B,\n", "    FastForwardQwen25Coder_14B,\n", ")\n", "from research.models import (\n", "    GenerationOptions,\n", ")\n", "\n", "\n", "# https://huggingface.co/Qwen/Qwen2.5-Coder-32B-Instruct/blob/main/tokenizer_config.json\n", "im_start_id = 151644\n", "im_end_id = 151645\n", "tool_start_id = 151657\n", "tool_end_id = 151658\n", "pad_token_id = 151643\n", "\n", "MASKED_ZERO_TOKEN = -(2**31)\n", "\n", "\n", "generation_options = GenerationOptions(\n", "    temperature=0,\n", "    top_k=0,\n", "    top_p=0,\n", "    max_generated_tokens=1024,\n", "    stop_tokens=[im_end_id],\n", ")\n", "\n", "# model = FastForwardQwen25Coder_7B(\n", "#     # checkpoint_path=Path(\"/mnt/efs/augment/checkpoints/pleasehold/pleasehold_qwen7b_v0_ff\"),\n", "#     # checkpoint_sha256=\"c7e749ce06ce3a3e2e4163facfd490cca3e4799a2446fe5ae179e9d38ae571d6\",\n", "#     # checkpoint_path=Path(\"/mnt/efs/augment/checkpoints/pleasehold/pleasehold_qwen7b_v2_bsz128_ff\"),\n", "#     # checkpoint_sha256=\"bb4e73628ae7461e0a5f169106e7e033a3efff0478b3ec24986abcf77dcade91\",\n", "#     # checkpoint_path=Path(\"/mnt/efs/augment/checkpoints/pleasehold/pleasehold_qwen7b_v3_ret_bsz128_ff\"),\n", "#     # checkpoint_sha256=\"44f108a3f133359dd4f1dd7ad9a97cc6fc771a1853f8c91752aaab00466a7ca6\",\n", "#     checkpoint_path=Path(\n", "#         \"/mnt/efs/augment/checkpoints/pleasehold/pleasehold_qwen7b_v4_bsz128_ff\"\n", "#     ),\n", "#     checkpoint_sha256=\"89c2fac4b7f07d546f8afa13bbe01ae8cd660efc00fad4d4113456226bd757a2\",\n", "#     sequence_length=16384,\n", "# )\n", "\n", "# model = FastForwardQwen25Coder_14B(\n", "#     checkpoint_path=Path(\"/mnt/efs/augment/checkpoints/pleasehold/pleasehold_v2_qwen14b_bsz128_ff\"),\n", "#     checkpoint_sha256=\"4a98839be54516a7a806cf72258a94ebb3ba7ab9d97eb5db586360aa658be8b9\",\n", "#     sequence_length=16384,\n", "# )\n", "model = FastForwardQwen25Coder_14B(\n", "    checkpoint_path=Path(\n", "        \"/mnt/efs/augment/checkpoints/pleasehold/pleasehold_v2_qwen14b_bsz128_ff_fp8\"\n", "    ),\n", "    checkpoint_sha256=\"301cb2137636abaee6a0dfb08c7e50d95aca9677cb33a7ccc72858c02938cad6\",\n", "    use_fp8=True,\n", "    sequence_length=16384,\n", ")\n", "\n", "\n", "model.load()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from megatron.data import indexed_dataset\n", "from transformers import AutoTokenizer\n", "\n", "# path = \"/mnt/efs/spark-data/shared/agentqa/v0/i0-vanguard0_since_20240101_bin_v2/dogfood\"\n", "# path = \"/mnt/efs/spark-data/shared/agentqa/v0/i0-vanguard0_since_20240101_bin_v2/vanguard_valid\"\n", "# path = \"/mnt/efs/spark-data/shared/agentqa/v0/i0-vanguard0_since_20240101/bin_v4/valid\"\n", "path = \"/mnt/efs/spark-data/shared/agentqa/v0/i0-vanguard0_since_20240101_to_20250303/bin_v4/valid\"\n", "\n", "ds = indexed_dataset.make_dataset(path, impl=\"mmap\", skip_warmup=True)\n", "print(len(ds))\n", "\n", "tokenizer = AutoTokenizer.from_pretrained(\n", "    pretrained_model_name_or_path=\"Qwen/Qwen2.5-Coder-32B-Instruct\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import dataclasses\n", "\n", "\n", "CATEGORY_LABELS = [\"general\", \"edit\", \"current_file\", \"codebase\", \"overview\"]\n", "\n", "\n", "@dataclasses.dataclass\n", "class QueryRouterResult:\n", "    category: str\n", "    about_specific_file: list[str]\n", "    external_package: str\n", "\n", "\n", "def rindex(lst, value):\n", "    for i in range(len(lst) - 1, -1, -1):\n", "        if lst[i] == value:\n", "            return i\n", "    return -1\n", "\n", "\n", "def unmask_tokens(tokens):\n", "    unmasked = []\n", "    for token in tokens:\n", "        if token == MASKED_ZERO_TOKEN:\n", "            unmasked.append(0)\n", "        else:\n", "            unmasked.append(abs(token))\n", "    return unmasked\n", "\n", "\n", "label_prefix = [im_start_id] + tokenizer.encode(\"assistant\\n\")\n", "\n", "\n", "def parse_label(label_str):\n", "    lines = label_str.split(\"\\n\")\n", "    return QueryR<PERSON>erResult(\n", "        category=CATEGORY_LABELS[int(lines[0])],\n", "        about_specific_file=lines[1].split(\",\"),\n", "        external_package=lines[2],\n", "    )\n", "\n", "\n", "def parse_sample(tokens):\n", "    unmasked = unmask_tokens(tokens)\n", "    im_start_position = rindex(unmasked, im_start_id)\n", "    assert im_start_position != -1\n", "    assert im_start_id not in unmasked[im_start_position + 1 :]\n", "    assert (\n", "        unmasked[im_start_position : im_start_position + len(label_prefix)]\n", "        == label_prefix\n", "    )\n", "    prompt_tokens = unmasked[: im_start_position + len(label_prefix)]\n", "\n", "    # parse request id\n", "    im_end_pos_first = unmasked.index(im_end_id)\n", "    assert im_end_pos_first >= 0\n", "    # system_prompt = tokenizer.decode(prompt_tokens[:im_end_pos_first])\n", "    # request_id = system_prompt.split(\"\\n\")[-1]\n", "    request_id = None\n", "\n", "    # parse label\n", "    label_tokens = unmasked[im_start_position + len(label_prefix) :]\n", "    label_tokens = [token for token in label_tokens if token != pad_token_id]\n", "    assert label_tokens[-1] == im_end_id\n", "    label_tokens = label_tokens[:-1]\n", "    label_str = tokenizer.decode(label_tokens)\n", "    label = parse_label(label_str)\n", "    return request_id, prompt_tokens, label\n", "\n", "\n", "request_id, prompt_tokens, label = parse_sample(ds[217].tolist())\n", "# print(tokenizer.decode(prompt_tokens))\n", "# print(\"---\")\n", "# print(tokenizer.decode(label_tokens))\n", "# label\n", "# tokenizer.decode(prompt_tokens)\n", "# request_id"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import tqdm\n", "import pandas as pd\n", "\n", "metrics = []\n", "\n", "# SUPPORT_URL = \"https://support.staging-shard-0.t.us-central1.prod.augmentcode.com/t/dogfood-shard/request/\"\n", "SUPPORT_URL = (\n", "    \"https://support.i0.t.us-central1.prod.augmentcode.com/t/i0-vanguard0/request/\"\n", ")\n", "\n", "# for i in tqdm.trange(len(ds)):\n", "for i in range(0, len(ds)):\n", "    request_id, prompt_tokens, label = parse_sample(ds[i].tolist())\n", "\n", "    predicted = parse_label(model.raw_generate(prompt_tokens, generation_options))\n", "    label_about_specific_file = [\n", "        path for path in label.about_specific_file if len(path) > 0\n", "    ]\n", "    metrics.append(\n", "        {\n", "            # \"request_id\": request_id,\n", "            \"label_category\": label.category,\n", "            \"predicted_category\": predicted.category,\n", "            \"category_accuracy\": int(predicted.category == label.category),\n", "            \"specific_file_present\": int(len(label_about_specific_file) > 0),\n", "            \"specific_file_accuracy\": int(\n", "                set(predicted.about_specific_file) == set(label.about_specific_file)\n", "            ),\n", "            \"external_package_present\": int(label.external_package != \"none\"),\n", "            \"external_package_accuracy\": int(\n", "                predicted.external_package == label.external_package\n", "            ),\n", "        }\n", "    )\n", "\n", "    # print(f\"Request ID: {request_id}\")\n", "    # print(f\"{SUPPORT_URL}{request_id}\")\n", "    # print()\n", "    # print(tokenizer.decode(prompt_tokens))\n", "    # print()\n", "\n", "    if label.category == predicted.category:\n", "        print(\"[Is general question]:\", label.category)\n", "    else:\n", "        print(\"DIFF [Is general question]:\", label.category, \"vs\", predicted.category)\n", "\n", "    if set(label.about_specific_file) == set(predicted.about_specific_file):\n", "        print(\"[About specific file]:\", label.about_specific_file)\n", "    else:\n", "        print(\n", "            \"DIFF [About specific file]:\",\n", "            label.about_specific_file,\n", "            \"vs\",\n", "            predicted.about_specific_file,\n", "        )\n", "\n", "    if label.external_package == predicted.external_package:\n", "        print(\"[External package]:\", label.external_package)\n", "    else:\n", "        print(\n", "            \"DIFF [External package]:\",\n", "            label.external_package,\n", "            \"vs\",\n", "            predicted.external_package,\n", "        )\n", "\n", "    print(\"\\n---\\n\")\n", "    # break\n", "\n", "df = pd.DataFrame(metrics)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "\n", "df = pd.DataFrame(metrics)\n", "print(df.mean(numeric_only=True))\n", "df.describe()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Group by specific_file_present and compute means\n", "specific_file_group = (\n", "    df.groupby(\"specific_file_present\").mean(numeric_only=True).reset_index()\n", ")\n", "print(\"Grouped by specific_file_present:\")\n", "print(specific_file_group.to_string(index=False))\n", "\n", "# Group by external_package_present and compute means\n", "external_package_group = (\n", "    df.groupby(\"external_package_present\").mean(numeric_only=True).reset_index()\n", ")\n", "print(\"\\nGrouped by external_package_present:\")\n", "print(external_package_group.to_string(index=False))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["label_category_results = (\n", "    df.groupby(\"label_category\")\n", "    .agg(\n", "        {\n", "            \"category_accuracy\": \"mean\",\n", "            \"specific_file_accuracy\": \"mean\",\n", "            \"external_package_accuracy\": \"mean\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "\n", "print(\"Results grouped by label_category:\")\n", "print(label_category_results.to_string(index=False))\n", "print(\"\\n\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Group by predicted_category\n", "predicted_category_results = (\n", "    df.groupby(\"predicted_category\")\n", "    .agg(\n", "        {\n", "            \"category_accuracy\": \"mean\",\n", "            \"specific_file_accuracy\": \"mean\",\n", "            \"external_package_accuracy\": \"mean\",\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "\n", "print(\"Results grouped by predicted_category:\")\n", "print(predicted_category_results)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\n", "    df[df[\"label_is_general_question\"] == 0].mean(numeric_only=True)[\n", "        \"general_question_accuracy\"\n", "    ]\n", ")\n", "print(\n", "    df[df[\"label_is_general_question\"] == 1].mean(numeric_only=True)[\n", "        \"general_question_accuracy\"\n", "    ]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
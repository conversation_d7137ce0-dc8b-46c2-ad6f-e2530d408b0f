{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from research.core.utils_for_file import read_jsonl_zst\n", "\n", "path = \"/mnt/efs/augment/eval/jobs/TbMfRqo7/michiel-h100__QuestionAgentSystem_5a3l/samples.jsonl.zst\"\n", "\n", "# load from zst\n", "loaded_samples = read_jsonl_zst(path)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from termcolor import colored\n", "from experimental.michiel.research.agentqa.agent import (\n", "    AgentFunctionState,\n", "    ModelCall,\n", "    ToolCallEntry,\n", ")\n", "\n", "\n", "def create_printp(prefix=\"\"):\n", "    def printp(*args, color=None):\n", "        text = \" \".join(map(str, args))\n", "        lines = text.splitlines()\n", "        for line in lines:\n", "            colored_line = colored(line, color) if color else line\n", "            print(prefix + colored_line)\n", "\n", "    return printp\n", "\n", "\n", "def output_state_overview(output_state):\n", "    # Get full overview\n", "    # print question\n", "    print(\"Question:\")\n", "    print(output_state[\"relevant_inputs\"][\"chat_input\"][\"message\"])\n", "\n", "    stack = [(output_state, 0)]\n", "    while stack:\n", "        current_event, depth = stack.pop()\n", "        prefix = \"   \" * depth\n", "        printp = create_printp(prefix)\n", "        if \"agent_metadata\" in current_event:\n", "            event_type = \"agent_state\"\n", "        elif \"tool_call\" in current_event:\n", "            event_type = \"tool_call\"\n", "        elif \"model_input\" in current_event:\n", "            event_type = \"model_call\"\n", "        else:\n", "            print(\"unknown : \", current_event)\n", "            raise ValueError(f\"Unknown type: {type(current_event)}\")\n", "        if event_type == \"agent_state\":\n", "            printp(f\"Agent call: {current_event['name']}\", color=\"blue\")\n", "            for event in reversed(current_event[\"combined_history\"]):\n", "                stack.append((event, depth + 1))\n", "        elif event_type == \"model_call\":\n", "            printp(\"Model call\", color=\"yellow\")\n", "        elif event_type == \"tool_call\":\n", "            tool_name = current_event[\"tool_call\"][\"tool_name\"]\n", "            printp(f\"Tool call: {tool_name}\", color=\"green\")\n", "            if tool_name != \"judge_answer\":\n", "                printp(f\"Tool input: {current_event['tool_call']['tool_input']}\")\n", "\n", "        else:\n", "            raise ValueError(f\"Unknown type: {type(current_event)}\")\n", "\n", "    # print answer\n", "    print(output_state[\"result\"][\"answer\"])\n", "\n", "\n", "sample = loaded_samples[8]\n", "output_state = sample[\"result\"][\"extra_output\"][\"additional_info\"][\"output_state\"]\n", "output_state_overview(output_state)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sample = loaded_samples[36]\n", "output_state = sample[\"result\"][\"extra_output\"][\"additional_info\"][\"output_state\"]\n", "combined_history = output_state[\"combined_history\"]\n", "for event in combined_history:\n", "    if \"tool_call\" in event:\n", "        print(event[\"tool_call\"][\"tool_name\"])\n", "\n", "import json\n", "\n", "\n", "def print_nested_list_or_dict(input_dict, nest_level=0):\n", "    prefix = \"   \" * nest_level\n", "    if isinstance(input_dict, list):\n", "        for item in input_dict:\n", "            print_nested_list_or_dict(item, nest_level + 1)\n", "    elif isinstance(input_dict, dict):\n", "        for key, value in input_dict.items():\n", "            if key == [\"tool_output\", \"relevant_inputs\"]:\n", "                continue\n", "            if isinstance(value, dict):\n", "                print(f\"{prefix}{key}:\")\n", "                print_nested_list_or_dict(value, nest_level + 1)\n", "            elif isinstance(value, list):\n", "                print(f\"{prefix}{key}:\")\n", "                for item in value:\n", "                    print_nested_list_or_dict(item, nest_level + 1)\n", "            elif isinstance(value, str):\n", "                print(f\"{prefix}{key}: {value[:50]}\")\n", "            else:\n", "                print(f\"{prefix}{key}: {value}\")\n", "\n", "\n", "print_nested_list_or_dict(output_state)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
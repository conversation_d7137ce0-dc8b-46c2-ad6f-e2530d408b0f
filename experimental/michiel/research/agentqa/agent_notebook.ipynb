{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from termcolor import colored\n", "\n", "from base.datasets.tenants import DOGFOOD_SHARD, DOGFOOD\n", "from base.prompt_format_retrieve.prompt_formatter import ChatRetrieverPromptInput\n", "from base.prompt_format_chat.prompt_formatter import ChatPromptInput\n", "\n", "from experimental.michiel.research.agentqa.retrieval import BlobGetter, create_retriever\n", "from experimental.michiel.research.agentqa.agent import (\n", "    <PERSON><PERSON><PERSON>,\n", "    ModelCall,\n", "    ToolCallEntry,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["request_id = \"b14b0e44-bd60-4b19-956f-ecb576c4822c\"\n", "# request_id = \"ad17e6db-94f2-4680-9fa7-6958751dc0ad\"\n", "\n", "blob_getter = BlobGetter(DOGFOOD_SHARD)\n", "documents = blob_getter.get_blobs_from_request_id(request_id)\n", "doc_ids = {doc.id for doc in documents}"]}, {"cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# load retriever\n", "retriever_config = {\n", "    \"scorer\": {\n", "        \"name\": \"dense_scorer_v2_ffwd\",\n", "        # \"checkpoint_path\": \"/mnt/efs/augment/user/igor/checkpoints/chatanol/chatanol1-18.hybrid/neox/global_step1468\",\n", "        \"checkpoint_path\": \"/home/<USER>/data/checkpoints/chatanol/chatanol1-18.hybrid/neox/global_step1468\",\n", "    },\n", "    \"chunker\": {\n", "        \"name\": \"smart_line_level\",\n", "        \"max_chunk_chars\": 768,\n", "        \"max_headers\": 3,\n", "    },\n", "    \"query_formatter\": {\n", "        \"name\": \"base:chatanol6-singleturnisspecial\",\n", "        \"tokenizer_name\": \"rogue\",\n", "        \"max_tokens\": 1024,\n", "    },\n", "    \"document_formatter\": {\n", "        \"name\": \"base:chatanol6-embedding\",\n", "        \"tokenizer_name\": \"rogue\",\n", "    },\n", "}\n", "retriever = create_retriever(retriever_config)\n", "retriever.load()"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["# retriever.add_docs(documents)\n", "retriever.add_docs(documents[:5])"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["def update_docs_new_request(\n", "    request_id: str,\n", "    directory_subtree_tool,\n", "):\n", "    new_documents = blob_getter.get_blobs_from_request_id(request_id)\n", "    new_doc_ids = {doc.id for doc in new_documents}\n", "    directory_subtree_tool.set_files(documents)\n", "    new_docs = [doc for doc in new_documents if doc.id not in doc_ids]\n", "    old_docs_ids = [doc.id for doc in documents if doc.id not in new_doc_ids]\n", "    print(f\"Removing {len(old_docs_ids)} old docs.\")\n", "    retriever.remove_docs(old_docs_ids)\n", "    print(f\"Adding {len(new_docs)} new docs.\")\n", "    retriever.add_docs(new_docs)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["##### Succeeds where base chat fails (or is meaningfully worse)\n", "\n", "# question = \"where does binks prompt formatter merge overlapping chunks?\"\n", "# question = \"list all registered model names in `research/models/fastforward_models.py`\"\n", "# question = \"which methods in utils_for_str.py are not covered by unit tests\"\n", "# question = \"How can I set multiple evals for the fastbackward training with custom names?\"\n", "\n", "# General reasoning is correct, but model confuses empty completion with empty list of completions\n", "# question = \"Do quality filtered completions show up in the history panel?\"\n", "\n", "# question = \"I want to modify a chunker to skip documents from the docset service. How do I do that?\"\n", "\n", "\n", "##### Fails where base chat succeeds (or is meaningfully better)\n", "\n", "# Failure - model doesn't discover flags.yaml\n", "# question = \"How do I change the default completion model in production?\"\n", "\n", "##### Both fail\n", "\n", "# Failure: ignores 'base' part\n", "# question = \"where is `suggested_prefix_char_count` in base\"\n", "\n", "# Failure: looks through part of code, confidently suggests it's nowhere. Cha<PERSON>l doesn't get it even if asked literally and restricted to base\n", "# question = \"where is `suggested_prefix_char_count` in base directory\"\n", "\n", "# Failure: gets confused by research code\n", "# question = \"How is the next edit output length propagated from the deploy config to the edit server\"\n", "\n", "# Failure: doesn't find eldenv3, gets confused with research, still answers confidently\n", "# question = \"Where is the base prompt formatter for eldenv3?\"\n", "\n", "# Failure: finds fastbackward instead\n", "# question = \"Does fastforward model in research use all available GPUs? Show exact lines.\"\n", "\n", "# Failure: doesn't call tool to look through entire directory. Agent does better than chat though.\n", "# question = \"which deployment configs are using neural speculative model?\"\n", "\n", "\n", "##### Both succeed\n", "\n", "# question = \"where is tutorial for str_diff\"\n", "# question = \"How do I serialize a python dataclass\"\n", "# question = \"Where is the default shortcut for accepting a code instruction set?\"\n", "# question = \"Where is eldenv3 configured?\"\n", "# question = \"Where is the prompt formatter for the eldenv5 model?\"\n", "# question = \"Do quality filtered completions show up in request insight?\"\n", "# question = \"How do I change the cluster when running evaluation jobs?\"\n", "\n", "# Agent has a more complete answer\n", "# question = \"How does the find-missing api decide which blobs are missing or unindexed?\"\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from experimental.michiel.research.agentqa.agent import (\n", "    TopLevelQuestionAnsweringAgent,\n", "    ProduceAnswerAgent,\n", "    JudgeAnswerAgent,\n", "    InformationAgent,\n", ")\n", "from research.llm_apis.llm_client import AnthropicVertexClient\n", "from research.llm_apis.llm_client import AnthropicDirectClient\n", "from experimental.michiel.research.agentqa.tools import (\n", "    CodebaseRetrievalTool,\n", "    DirectorySubTreeTool,\n", "    JudgeAnswerTool,\n", "    ToolSelectionTool,\n", ")\n", "\n", "model_name = \"claude-3-5-sonnet-20241022\"\n", "# model_name = \"claude-3-5-sonnet@20240620\"\n", "\n", "# client = AnthropicVertexClient(model_name=\"claude-3-5-sonnet-v2@20241022\")\n", "client = AnthropicDirectClient(model_name=model_name)\n", "\n", "chat_history_len = 2000\n", "produce_answer_agent = ProduceAnswerAgent(\n", "    model_client=client,\n", "    chat_history_len=chat_history_len,\n", ")\n", "judge_answer_tool = JudgeAnswerTool()\n", "judge_answer_agent = JudgeAnswerAgent(\n", "    model_client=client, judge_answer_tool=judge_answer_tool\n", ")\n", "codebase_retrieval_tool = CodebaseRetrievalTool(\n", "    retriever=retriever, max_tool_chars=20000\n", ")\n", "directory_subtree_tool = DirectorySubTreeTool(\n", "    documents, max_directory_chars=10000, max_file_chars=10000\n", ")\n", "tool_selection_tool = ToolSelectionTool()\n", "information_agent = InformationAgent(\n", "    model_client=client,\n", "    codebase_retrieval_tool=codebase_retrieval_tool,\n", "    directory_subtree_tool=directory_subtree_tool,\n", "    tool_selection_tool=tool_selection_tool,\n", "    chat_history_len=chat_history_len,\n", ")\n", "\n", "\n", "agent = TopLevelQuestionAnsweringAgent(\n", "    max_turns=10,\n", "    produce_answer_agent=produce_answer_agent,\n", "    judge_answer_agent=judge_answer_agent,\n", "    information_agent=information_agent,\n", ")\n", "\n", "# question = \"What is the difference between functions and classes in Python?\"\n", "\n", "\n", "# question = \"What determines the name of a kubernetes pod?\"\n", "\n", "\n", "# question = \"where do we do matrix multiplication in the production retrieval service.\"\n", "# question = \"How do I change the default completion model in production?\"\n", "# question = \"where is `suggested_prefix_char_count` in base\"\n", "# question = \"where does binks prompt formatter merge overlapping chunks?\"\n", "# question = \"How can I set multiple evals for the fastbackward training with custom names?\"\n", "# question = \"list all registered model names in `research/models/fastforward_models.py\"\n", "# question = \"Do quality filtered completions show up in the history panel?\"\n", "# question = \"I want to modify a chunker to skip documents from the docset service. How do I do that?\"\n", "# question = \"How can I set multiple evals for the fastbackward training with custom names?\"\n", "# question = \"Where is the base prompt formatter for eldenv3?\"\n", "# question = \"When using pythons .get() method on dictionaries, when does it return the default value?.\"\n", "# question = \"Where is the load balancing strategy for our embedding indexers defined?\"\n", "\n", "# question = \"which chat retrievers do we have deployed according to the metadata jsonnet file?\"\n", "# question = \"What does vscode client do with out of date files for a completion request?\"\n", "\n", "\n", "# question = \"suggested_prefix_char_count in vscode client\"\n", "# question = \"Does fastforward model in research use all available GPUs? Show exact lines\"\n", "# question = \"How do I change the default completion model in production?\"\n", "# question = \"which deployment configs are using neural speculative model?\"\n", "# question = \"find all deployment configs using neural speculative model\"\n", "# question = \"where is tutorial for str_diff\"\n", "# question = \"is before_lrange or after_lrange or before_crange or after_crange of DiffHunk used anywhere currently?\"\n", "# question = \"Does adding directory to .augmentignore mean that we no longer retrieve from already uploaded files?\"\n", "# question = \"How can I add a toggle with label text 'workspace rules' to the bottom left corner of the chat input box in vscode?\"\n", "# question = \"Which tool could I use to generate a comparison report for AugmentQA?\"\n", "\n", "null_agent_log = AgentLog()\n", "\n", "request_id = \"b14b0e44-bd60-4b19-956f-ecb576c4822c\"\n", "chat_input = blob_getter.get_chat_request_without_retrievals(request_id)\n", "\n", "# chat_input = ChatPromptInput(\n", "#     path=\"\",\n", "#     prefix=\"\",\n", "#     selected_code=\"\",\n", "#     suffix=\"\",\n", "#     message=question,\n", "#     chat_history=[],\n", "#     prefix_begin=0,\n", "#     suffix_end=0,\n", "#     retrieved_chunks=[],\n", "# )\n", "\n", "update_docs_new_request(request_id, directory_subtree_tool)\n", "\n", "answer = agent(null_agent_log, chat_input).answer\n", "top_level_logs = null_agent_log.child_logs[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(top_level_logs.error_state)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def create_printp(prefix=\"\"):\n", "    def printp(*args, color=None):\n", "        text = \" \".join(map(str, args))\n", "        lines = text.splitlines()\n", "        for line in lines:\n", "            colored_line = colored(line, color) if color else line\n", "            print(prefix + colored_line)\n", "\n", "    return printp\n", "\n", "\n", "# Get full overview\n", "stack = [(top_level_logs, 0)]\n", "while stack:\n", "    current_event, depth = stack.pop()\n", "    prefix = \"   \" * depth\n", "    printp = create_printp(prefix)\n", "    type_str = str(type(current_event))\n", "    if type_str == str(AgentLog):\n", "        printp(f\"Agent call: {current_event.name}\", color=\"blue\")\n", "        for event in reversed(current_event.combined_history):\n", "            stack.append((event, depth + 1))\n", "    elif type_str == str(ModelCall):\n", "        printp(\"Model call\", color=\"yellow\")\n", "    elif type_str == str(ToolCallEntry):\n", "        tool_name = current_event.tool_call.tool_name\n", "        printp(f\"Tool call: {tool_name}\", color=\"green\")\n", "        if tool_name != \"judge_answer\":\n", "            printp(f\"Tool input: {current_event.tool_call.tool_input}\")\n", "    else:\n", "        raise ValueError(f\"Unknown type: {type(current_event)}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def create_printp(prefix=\"\"):\n", "    def printp(*args, color=None):\n", "        text = \" \".join(map(str, args))\n", "        lines = text.splitlines()\n", "        for line in lines:\n", "            colored_line = colored(line, color) if color else line\n", "            print(prefix + colored_line)\n", "\n", "    return printp\n", "\n", "\n", "# Get full overview\n", "stack = [(top_level_logs, 0)]\n", "while stack:\n", "    current_event, depth = stack.pop()\n", "    prefix = \"   \" * depth\n", "    printp = create_printp(prefix)\n", "    type_str = str(type(current_event))\n", "    if type_str == str(AgentLog):\n", "        printp(f\"Agent call: {current_event.name}\", color=\"blue\")\n", "        for event in reversed(current_event.combined_history):\n", "            stack.append((event, depth + 1))\n", "    elif type_str == str(ModelCall):\n", "        printp(\"Model call\", color=\"yellow\")\n", "    elif type_str == str(ToolCallEntry):\n", "        tool_name = current_event.tool_call.tool_name\n", "        printp(f\"Tool call: {tool_name}\", color=\"green\")\n", "        if tool_name != \"judge_answer\":\n", "            printp(f\"Tool input: {current_event.tool_call.tool_input}\")\n", "        printp(\n", "            f\"Formatted tool output: {current_event.formatted_tool_output.tool_output if current_event.formatted_tool_output else None}\"\n", "        )\n", "    else:\n", "        raise ValueError(f\"Unknown type: {type(current_event)}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from experimental.michiel.research.agentqa.question_agent_system import (\n", "    QuestionAgentSystem,\n", ")\n", "\n", "model_name = \"claude-3-5-sonnet-v2@20241022\"\n", "\n", "system_config = {\n", "    \"model_config\": {\"model\": model_name},\n", "    \"retriever_config\": retriever_config,\n", "    \"agent_config\": {\"max_turns\": 10},\n", "    \"verbose\": True,\n", "}\n", "\n", "system = QuestionAgentSystem.from_yaml_config(system_config)\n", "\n", "system.load()\n", "system.add_docs(documents[:5])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["question = \"Do quality filtered completions show up in request insight?\"\n", "\n", "prompt_input = ChatPromptInput(\n", "    path=\"\",\n", "    prefix=\"\",\n", "    selected_code=\"\",\n", "    suffix=\"\",\n", "    message=question,\n", "    chat_history=[],\n", "    prefix_begin=0,\n", "    suffix_end=0,\n", "    retrieved_chunks=[],\n", ")\n", "\n", "result = system.generate(prompt_input)\n", "\n", "print(result.extra_output.additional_info[\"output_state\"][\"error_state\"][\"traceback\"])\n", "print(result.generated_text)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["subtree_tool = DirectorySubTreeTool(\n", "    documents, max_directory_chars=20000, max_file_chars=20000\n", ")\n", "subtree_str = subtree_tool.format_directory_subtree(\n", "    starting_dir_str=\"\",\n", "    max_directory_chars=200000,\n", "    max_file_chars=50000,\n", ")\n", "print(subtree_str)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
from __future__ import annotations

import functools
import traceback
from dataclasses import dataclass, field
from textwrap import dedent
from typing import Any, Optional, cast

from dataclasses_json import DataClassJsonMixin

from base.prompt_format_chat.lib.chat_history_builder import (
    format_chat_history,
    inject_selected_code_into_chat_history,
)
from base.prompt_format_chat.lib.token_counter import To<PERSON><PERSON>ounter
from base.prompt_format_chat.prompt_formatter import ChatPromptInput
from experimental.michiel.research.agentqa.tools import (
    Tool,
)
from research.llm_apis.llm_client import (
    AssistantContentBlock,
    GeneralContentBlock,
    LLMClient,
    TextPrompt,
    TextResult,
    ToolCall,
    ToolFormattedResult,
    ToolResult,
)


class CharCounter(TokenCounter):
    """A token counter that counts characters."""

    @functools.lru_cache(maxsize=128)
    def count_tokens(self, prompt_chars: str) -> int:
        return len(prompt_chars)


@dataclass
class ModelCall(DataClassJsonMixin):
    """A model call."""

    model_input: list[list[GeneralContent<PERSON>lock]]
    """The prompt."""

    model_response: list[Assistant<PERSON>ontentBlock]
    """The response."""

    model_response_metadata: dict[str, Any] = field(default_factory=dict)
    """Metadata about the model call."""


@dataclass
class ToolCallEntry(DataClassJsonMixin):
    """A tool call entry with input and result."""

    tool_call: ToolCall
    """The tool call."""

    tool_result: ToolResult
    """The tool result."""

    formatted_tool_output: ToolFormattedResult | None = None
    """Formatted tool output."""


@dataclass
class AgentLog(DataClassJsonMixin):
    """Log for an agent function."""

    name: str = ""

    agent_metadata: dict[str, Any] = field(default_factory=dict)
    """Metadata about the agent function."""

    relevant_inputs: dict[str, Any] = field(default_factory=dict)
    """Relevant inputs."""

    result: Any = None
    """Result of the agent function."""

    model_call_history: list[ModelCall] = field(default_factory=list)
    """History of model calls."""

    tool_history: list[ToolCallEntry] = field(default_factory=list)

    child_logs: list[AgentLog] = field(default_factory=list)
    """Children log calls."""

    combined_history: list[ModelCall | ToolCallEntry | AgentLog] = field(
        default_factory=list
    )

    error_state: Any = None
    """Error state."""

    def register_child(self, child_log: AgentLog) -> None:
        self.child_logs.append(child_log)
        self.combined_history.append(child_log)

    def register_model_call(self, model_call: ModelCall) -> None:
        self.model_call_history.append(model_call)
        self.combined_history.append(model_call)

    def register_tool_call(self, tool_call: ToolCallEntry) -> None:
        self.tool_history.append(tool_call)
        self.combined_history.append(tool_call)

    def register_result(self, result: Any) -> None:
        self.result = result


class AgentFunction:
    """Agent functions perform agentic tasks. They take an input state, perform some
    actions, and return an output state. Agent functions can call models, which may
    perform tool calls which the agent function then executes. Agent functions can also
    call other agent functions."""

    def __init__(self):
        pass

    def __call__(self, agent_log: AgentLog, *args, **kwargs) -> Any:
        raise NotImplementedError


@dataclass
class TopLevelQuestionAnsweringAgentResult(DataClassJsonMixin):
    """Result of the top level agent function."""

    answer: Optional[str] = None
    """The answer."""


class TopLevelQuestionAnsweringAgent(AgentFunction):
    """The top level agent function."""

    def __init__(
        self,
        max_turns: int,
        produce_answer_agent: AgentFunction,
        judge_answer_agent: AgentFunction,
        information_agent: AgentFunction,
    ):
        self.max_turns = max_turns
        self.produce_answer_agent = produce_answer_agent
        self.judge_answer_agent = judge_answer_agent
        self.information_agent = information_agent

    def __call__(
        self, empty_log: AgentLog, chat_input: ChatPromptInput
    ) -> TopLevelQuestionAnsweringAgentResult:
        """Run the top level agent function."""

        # empty_log.register_child(current_agent_log)
        current_agent_log = AgentLog(name="TopLevelQuestionAnsweringAgent")
        empty_log.register_child(current_agent_log)
        current_agent_log.agent_metadata = {
            "max_turns": self.max_turns,
        }
        try:
            result = self.run_agent(
                chat_input=chat_input,
                current_agent_log=current_agent_log,
            )
        except Exception as e:
            current_agent_log.error_state = {
                "error": str(e),
                "traceback": traceback.format_exc(),
            }
            result = TopLevelQuestionAnsweringAgentResult(answer=None)
        return result

    def run_agent(
        self,
        chat_input: ChatPromptInput,
        current_agent_log: AgentLog,
    ) -> TopLevelQuestionAnsweringAgentResult:
        current_agent_log.relevant_inputs["chat_input"] = chat_input
        information_call_list: list[ToolCallEntry] = []

        acceptable_answer = False
        candidate_answer = None
        answer_judgement = None

        for turn in range(self.max_turns):
            print(f"Starting turn {turn}")
            # Call information agent
            print("Calling information agent")
            information_agent_result = self.information_agent(
                agent_log=current_agent_log,
                chat_input=chat_input,
                information_call_list=information_call_list,
                candidate_answer=candidate_answer,
                answer_judgement=answer_judgement,
            )
            # Assimilate new information
            information_call_list.extend(
                information_agent_result.information_call_result
            )
            # Attempt to answer question
            print("Attempting to answer question")
            candidate_answer_result = self.produce_answer_agent(
                agent_log=current_agent_log,
                chat_input=chat_input,
                information_call_list=information_call_list,
            )
            candidate_answer = candidate_answer_result.answer_text or None
            print(f"Candidate answer: {candidate_answer}")

            # Judge answer
            answer_judgement = self.judge_answer_agent(
                agent_log=current_agent_log, candidate_answer=candidate_answer
            )
            print(f"Answer judgement: {answer_judgement}")
            if answer_judgement.is_complete and answer_judgement.is_supported:
                acceptable_answer = True
                break

        assert candidate_answer is not None, "No answer found"
        result = TopLevelQuestionAnsweringAgentResult(answer=candidate_answer)
        current_agent_log.register_result(result)

        if acceptable_answer:
            print("Acceptable answer found")
        else:
            print("Warning: answer was deemed incomplete or unsupported")

        return result


@dataclass
class ProduceAnswerAgentResult(DataClassJsonMixin):
    """Result of the produce answer agent function."""

    answer_text: Optional[str] = None
    """The answer."""


class ProduceAnswerAgent(AgentFunction):
    """The produce answer agent function."""

    answer_prompt = dedent(
        """\
        Please write an answer to the question.

        There are two types of question: general programming questions, and questions that relate to the specific codebase.
        If the question is general, please provide an answer in the form of a chain of thought with supporting facts.
            Do not use the provided code snippets or directory tree.

        If the question is specific to the codebase, please provide an answer in the form of a chain of thought that relates to the codebase.
            For each step in the chain of thought, please include:
            - The full file path of a supporting documentation or code snippet.
            - The content of the supporting documentation or code snippet.
            - How the supporting documentation or code snippet supports the step.

            Please keep in mind that not all the supporting information is relevant to the question.
            For example, if a question asks about 'classes in foo', exerpts in 'bar' may not be relevant even though they are similar.
            Moreover, the supporting information does not include the entire codebase, and crucial information may be missing.
            If you think any information might be missing, please point it out.
        """
    )

    def __init__(self, model_client: LLMClient, chat_history_len: int = 0):
        self.model_client = model_client
        self.chat_history_len = chat_history_len

    def format_prompt(
        self,
        chat_input: ChatPromptInput,
        information_call_list: list[ToolCallEntry],
    ) -> list[list[GeneralContentBlock]]:
        """Format the prompt for the produce answer agent function."""

        formatted_chat_input = format_chat_input(
            chat_input,
            chat_history_len=self.chat_history_len,
        )

        # Format information and question into user message
        user_message_string = ""
        user_message_string += (
            f"The user query we are trying to help with is:\n{formatted_chat_input}"
        )
        user_message_string += dedent(
            """\
            Here we provide some supporting information.
            """
        )
        for information_call in information_call_list:  # type: ignore
            user_message_string += (
                f"{information_call.formatted_tool_output.tool_output}\n"  # type: ignore
            )

        user_message_string += self.answer_prompt
        user_message_string += (
            f"As a reminder, the user query is:\n{formatted_chat_input}"
        )

        messages = []
        messages.append([TextPrompt(text=user_message_string)])
        return messages

    def __call__(
        self,
        agent_log: AgentLog,
        chat_input: ChatPromptInput,
        information_call_list: list[ToolCallEntry],
    ) -> ProduceAnswerAgentResult:
        """Produce an answer."""

        current_agent_log = AgentLog(name="ProduceAnswerAgent")
        agent_log.register_child(current_agent_log)
        current_agent_log.relevant_inputs = {
            "chat_input": chat_input,
            "information_call_list": information_call_list,
        }
        messages = self.format_prompt(chat_input, information_call_list)

        # Register model call without response first.
        model_call = ModelCall(
            model_input=messages,
            model_response=[],
        )
        current_agent_log.register_model_call(model_call)

        # Call model
        call_result, call_metadata = self.model_client.generate(
            messages=messages,
            max_tokens=2048,
        )
        model_call.model_response = call_result

        assert len(call_result) == 1
        assert str(type(call_result[0])) == str(TextResult)
        result = ProduceAnswerAgentResult(answer_text=call_result[0].text)
        current_agent_log.register_result(result)

        return result


@dataclass
class JudgeAnswerAgentResult(DataClassJsonMixin):
    """Result of the judge answer agent function."""

    completeness_feedback: str = ""
    """Verbal feedback on whether the answer is complete."""

    is_complete: bool = False
    """Whether the answer is complete."""

    support_feedback: str = ""
    """Verbal feedback on whether the answer is supported."""

    is_supported: bool = False
    """Whether the answer is supported."""


class JudgeAnswerAgent(AgentFunction):
    """The judge answer agent function."""

    judge_prompt = dedent(
        """\
        Please determine whether the answer is complete and supported by the chain of thought evidence.
        """
    )

    def __init__(self, model_client: LLMClient, judge_answer_tool: Tool):
        self.model_client = model_client
        self.judge_tool = judge_answer_tool

    def format_prompt(self, answer: str) -> list[list[GeneralContentBlock]]:
        """Format the prompt for the judge answer agent function."""

        messages = []
        user_message_string = ""
        user_message_string += f"The answer is:\n{answer}\n"
        user_message_string += self.judge_prompt
        messages.append([TextPrompt(text=user_message_string)])
        return messages

    def __call__(
        self, agent_log: AgentLog, candidate_answer: str
    ) -> JudgeAnswerAgentResult:
        """Judge an answer."""
        current_agent_log = AgentLog(
            name="JudgeAnswerAgent",
            relevant_inputs={"candidate_answer": candidate_answer},
        )
        agent_log.register_child(current_agent_log)
        current_agent_log.relevant_inputs = {
            "candidate_answer": candidate_answer,
        }

        messages = self.format_prompt(candidate_answer)

        # Register model call without response.
        model_call = ModelCall(
            model_input=messages,
            model_response=[],
        )
        current_agent_log.register_model_call(model_call)

        call_result, call_metadata = self.model_client.generate(
            messages=messages,
            max_tokens=2048,
            tools=[self.judge_tool.get_tool_param()],
            tool_choice={"type": "tool", "name": self.judge_tool.name},
        )
        model_call.model_response = call_result

        assert len(call_result) == 1, f"Expected one message but got {len(call_result)}"
        assert str(type(call_result[0])) == str(
            ToolCall
        ), f"Expected tool call but got: {type(call_result[0])}"
        tool_call = cast(ToolCall, call_result[0])
        tool_output, _ = self.judge_tool.activate_tool(tool_call)
        tool_call_entry = ToolCallEntry(
            tool_call=tool_call,
            tool_result=tool_output,
        )
        current_agent_log.register_tool_call(tool_call_entry)
        result = JudgeAnswerAgentResult(
            completeness_feedback=tool_output.tool_output["completeness_feedback"],
            is_complete=tool_output.tool_output["is_complete"],
            support_feedback=tool_output.tool_output["support_feedback"],
            is_supported=tool_output.tool_output["is_supported"],
        )
        current_agent_log.register_result(result)
        return result


@dataclass
class InformationAgentResult(DataClassJsonMixin):
    """Result of the information agent function."""

    information_exploration_result: Optional[str] = None
    """The information exploration result."""

    information_call_result: list[ToolCallEntry] = field(default_factory=list)
    """The new information."""


class InformationAgent(AgentFunction):
    """The information agent function."""

    def __init__(
        self,
        model_client: LLMClient,
        codebase_retrieval_tool: Tool,
        directory_subtree_tool: Tool,
        tool_selection_tool: Tool,
        exploration_client: Optional[LLMClient] = None,
        codebase_retrieval_client: Optional[LLMClient] = None,
        directory_subtree_client: Optional[LLMClient] = None,
        chat_history_len: int = 0,
    ):
        self.codebase_retrieval_tool = codebase_retrieval_tool
        self.directory_subtree_tool = directory_subtree_tool
        self.tool_selection_tool = tool_selection_tool
        self.exploration_client = exploration_client or model_client
        self.codebase_retrieval_client = codebase_retrieval_client or model_client
        self.directory_subtree_client = directory_subtree_client or model_client
        self.chat_history_len = chat_history_len

    def __call__(
        self,
        agent_log: AgentLog,
        chat_input: ChatPromptInput,
        information_call_list: list[ToolCallEntry],
        candidate_answer: str | None,
        answer_judgement: JudgeAnswerAgentResult | None = None,
    ) -> InformationAgentResult:
        """Produce new information."""

        current_agent_log = AgentLog(name="InformationAgent")
        agent_log.register_child(current_agent_log)
        current_agent_log.relevant_inputs = {
            "chat_input": chat_input,
            "information_call_list": information_call_list,
            "candidate_answer": candidate_answer,
            "answer_judgement": answer_judgement,
        }

        is_first_turn = candidate_answer is None
        current_agent_log.agent_metadata = {
            "is_first_turn": is_first_turn,
        }

        # For the first turn we don't use a model call to figure out what info to get
        if is_first_turn:
            return self.get_first_turn_information(
                current_agent_log=current_agent_log,
                chat_input=chat_input,
            )

        information_exploration_messages = self.format_exploration_prompt(
            chat_input=chat_input,
            information_call_list=information_call_list,
            candidate_answer=candidate_answer,
            answer_judgement=answer_judgement,
        )
        # Register model call without response first.
        information_exploration_model_call = ModelCall(
            model_input=information_exploration_messages,
            model_response=[],
        )
        current_agent_log.register_model_call(information_exploration_model_call)

        information_exploration_call_result, information_exploration_call_metadata = (
            self.exploration_client.generate(
                messages=information_exploration_messages,
                max_tokens=2048,
            )
        )
        assert (
            len(information_exploration_call_result) == 1
        ), f"Expected one message but got {len(information_exploration_call_result)}"
        assert str(type(information_exploration_call_result[0])) == str(TextResult)
        information_exploration_result = information_exploration_call_result[0]
        information_exploration_model_call.model_response = (
            information_exploration_call_result
        )

        print(
            "Information agent has received information exploration advice: ",
            information_exploration_result.text,
        )

        # Call model with directory tool
        directory_tool_messages = self.format_chosen_tool_prompt(
            chosen_tool=self.directory_subtree_tool,
            chat_input=chat_input,
            information_call_list=information_call_list,
            candidate_answer=candidate_answer,
            answer_judgement=answer_judgement,
            information_exploration_result=information_exploration_result,
        )
        # Register model call without response first.
        directory_tool_model_call = ModelCall(
            model_input=directory_tool_messages,
            model_response=[],
        )
        current_agent_log.register_model_call(directory_tool_model_call)
        directory_tool_call_result, directory_tool_call_metadata = (
            self.directory_subtree_client.generate(
                messages=directory_tool_messages,
                max_tokens=2048,
                tools=[
                    self.directory_subtree_tool.get_tool_param(),
                ],
                tool_choice={"type": "tool", "name": self.directory_subtree_tool.name},
            )
        )
        directory_tool_model_call.model_response = directory_tool_call_result
        assert (
            len(directory_tool_call_result) == 1
        ), f"Expected one message but got {len(directory_tool_call_result)}"
        assert str(type(directory_tool_call_result[0])) == str(ToolCall)
        directory_tool_call = cast(ToolCall, directory_tool_call_result[0])
        print("Information agent has decided to call directory tool with inputs:")
        print(directory_tool_call.tool_input, "\n")
        directory_tool_output, _ = self.directory_subtree_tool.activate_tool(
            directory_tool_call
        )
        formatted_directory_tool_output, _ = (
            self.directory_subtree_tool.format_tool_result(
                tool_result=directory_tool_output
            )
        )
        directory_tool_entry = ToolCallEntry(
            tool_call=directory_tool_call,
            tool_result=directory_tool_output,
            formatted_tool_output=formatted_directory_tool_output,
        )
        current_agent_log.register_tool_call(directory_tool_entry)

        # Call model with codebase retrieval tool
        retrieval_tool_messages = self.format_chosen_tool_prompt(
            chosen_tool=self.codebase_retrieval_tool,
            chat_input=chat_input,
            information_call_list=information_call_list,
            candidate_answer=candidate_answer,
            answer_judgement=answer_judgement,
            information_exploration_result=information_exploration_result,
        )
        # Register model call without response first.
        retrieval_tool_model_call = ModelCall(
            model_input=retrieval_tool_messages,
            model_response=[],
        )
        current_agent_log.register_model_call(retrieval_tool_model_call)

        retrieval_tool_call_result, retrieval_tool_call_metadata = (
            self.codebase_retrieval_client.generate(
                messages=retrieval_tool_messages,
                max_tokens=2048,
                tools=[
                    self.codebase_retrieval_tool.get_tool_param(),
                ],
                tool_choice={"type": "tool", "name": self.codebase_retrieval_tool.name},
            )
        )
        retrieval_tool_model_call.model_response = retrieval_tool_call_result
        assert (
            len(retrieval_tool_call_result) == 1
        ), f"Expected one message but got {len(retrieval_tool_call_result)}"
        assert str(type(retrieval_tool_call_result[0])) == str(ToolCall)
        retrieval_tool_call = cast(ToolCall, retrieval_tool_call_result[0])
        print("Information agent has decided to call retrieval tool with inputs:")
        print(retrieval_tool_call.tool_input, "\n")
        retrieval_tool_output, _ = self.codebase_retrieval_tool.activate_tool(
            retrieval_tool_call
        )
        formatted_retrieval_tool_output, _ = (
            self.codebase_retrieval_tool.format_tool_result(
                tool_result=retrieval_tool_output
            )
        )
        retrieval_tool_entry = ToolCallEntry(
            tool_call=retrieval_tool_call,
            tool_result=retrieval_tool_output,
            formatted_tool_output=formatted_retrieval_tool_output,
        )
        current_agent_log.register_tool_call(retrieval_tool_entry)

        result = InformationAgentResult(
            information_exploration_result=information_exploration_result.text,
            information_call_result=[
                directory_tool_entry,
                retrieval_tool_entry,
            ],
        )
        current_agent_log.register_result(result)

        return result

    def format_exploration_prompt(
        self,
        chat_input: ChatPromptInput,
        information_call_list: list[ToolCallEntry],
        candidate_answer: str | None,
        answer_judgement: JudgeAnswerAgentResult | None,
    ) -> list[list[GeneralContentBlock]]:
        """Format the prompt for the information agent function.

        Prompt consists of
        Question
        existing information
        attempted answer
        answer feedback
        adviser prompt
        """

        exploration_prompt = dedent(
            """\
            You are an information explorer that helps an agent answer a coding question.
            Please write advice for another agent that will try to collect more information to answer the question.
            This agent has access to two tools in each turn:
            - ask_for_directory_subtree:
                This tool will return the names of subdirectories and files in a given directory, recursively, until a character limit is reached.
                If a character limit was reached such that not all files or subdirectories can be displayed, that is indicated with '...'.
                If there are no ... under a directory, that means that all subdirectories and files were displayed.
                This is useful to find the locations of relevant information.
            - ask_for_codebase_snippets:
                This tool will use a retriever to look up snippets matching the description in the codebase.
                Optionally, a "path" field can be specified to look for snippets in a specific location.
                Also optionally, a "contains_string" field can be specified to look for snippets that contain that string.

            Please consider the following:
                Is there a clear piece of information, with a rough location in the codebase, that the agent has not looked for yet? If so, advise looking for this.
                If not, what is missing?
                    - Is it not clear where this information is? Consider advising to use the directory subtree tool.
                    - Is it not clear what the next step should be? Outline the possible next steps.
                    - Is the agent stuck repeatedly trying to find the same information? Think of something different for it to try.
                Please check the information gathered so far - do not suggest to repeat a step that has already been tried.
                For example, if the agent has already asked for the directory tree of foo/bar, and didn't find anything useful, think of somewhere else to look.

            Please conclude with advice on the most valuable way to use each tool next, as it can use each tool once per turn.
            """
        )

        messages = []
        formatted_chat_input = format_chat_input(
            chat_input,
            chat_history_len=self.chat_history_len,
        )
        user_message_string = ""
        user_message_string += (
            f"The user query we are trying to help with is:\n{formatted_chat_input}"
        )
        user_message_string += "The following information has been gathered so far:\n"
        for tool_call_entry in information_call_list:  # type: ignore
            user_message_string += (
                f"{tool_call_entry.formatted_tool_output.tool_output}\n"  # type: ignore
            )
        if candidate_answer is not None and answer_judgement is not None:
            user_message_string += f"The attempted answer is:\n{candidate_answer}\n"
            user_message_string += (
                f"The answer revealed the following feedback:\n"
                f"Complete: {answer_judgement.completeness_feedback}\n"
                f"Supported: {answer_judgement.support_feedback}\n"
            )
        user_message_string += exploration_prompt
        user_message_string += "So far, the agent has made the following tool calls:\n"
        for tool_call_entry in information_call_list:  # type: ignore
            user_message_string += f"Name: {tool_call_entry.tool_call.tool_name}\n"
            user_message_string += f"Input: {tool_call_entry.tool_call.tool_input}\n"
        user_message_string += (
            f"As a reminder, the user query is:\n{formatted_chat_input}"
        )
        messages.append([TextPrompt(text=user_message_string)])
        return messages

    def format_decision_prompt(
        self,
        chat_input: ChatPromptInput,
        information_call_list: list[ToolCallEntry],
        candidate_answer: str | None,
        answer_judgement: JudgeAnswerAgentResult | None,
        information_exploration_result: TextResult,
    ) -> list[list[GeneralContentBlock]]:
        """Format the prompt for the information agent function.

        Prompt consists of
        Question
        existing information
        question reminder
        attempted answer
        answer feedback

        """

        decision_prompt = dedent(
            """\
            You are a tool-calling agent that determines which tool to call next to gather information to answer a coding question.
            Please decide which tool should be called next to gather information.
            For codebase-specific information, generally favor to first call the "ask_for_directory_subtree" tool.
            If you already know the shape of the codebase, call the "ask_for_codebase_snippets" tool to see the actual code.
            """
        )

        messages = []
        formatted_chat_input = format_chat_input(
            chat_input,
            chat_history_len=self.chat_history_len,
        )
        user_message_string = ""
        user_message_string += (
            f"The user query we are trying to help with is:\n{formatted_chat_input}"
        )
        user_message_string += "The following information has been gathered so far:\n"
        for tool_call_entry in information_call_list:  # type: ignore
            user_message_string += (
                f"{tool_call_entry.formatted_tool_output.tool_output}\n"  # type: ignore
            )
        if candidate_answer is not None and answer_judgement is not None:
            user_message_string += f"The attempted answer is:\n{candidate_answer}\n"
            user_message_string += (
                f"The answer revealed the following feedback:\n"
                f"Complete: {answer_judgement.completeness_feedback}\n"
                f"Supported: {answer_judgement.support_feedback}\n"
            )
        user_message_string += decision_prompt
        user_message_string += f"An  information exploration agent has the following advice:\n{information_exploration_result.text}"
        user_message_string += (
            f"As a reminder, the user query is:\n{formatted_chat_input}"
        )
        messages.append([TextPrompt(text=user_message_string)])
        return messages

    def format_chosen_tool_prompt(
        self,
        chosen_tool: Tool,
        chat_input: ChatPromptInput,
        information_call_list: list[ToolCallEntry],
        candidate_answer: str | None,
        answer_judgement: JudgeAnswerAgentResult | None,
        information_exploration_result: TextResult,
    ) -> list[list[GeneralContentBlock]]:
        """Format the prompt for the information agent function.

        Prompt consists of
        Question
        existing information
        question reminder
        attempted answer
        answer feedback

        """

        chosen_prompt = dedent(
            """\
            You are a tool-calling agent that calls a specific tool to gather information to help answer a coding question.
            Please pay careful attention to the tool description to optimally use the tool.
            Listen to the advice of the information exploration agent, but do not blindly follow it.
            For example, it may not know the exact way to use the tool, and may suggest illegal inputs.
            Translate the advice into inputs for the tool.
            """
        )

        messages = []
        formatted_chat_input = format_chat_input(
            chat_input,
            chat_history_len=self.chat_history_len,
        )
        user_message_string = ""
        user_message_string += (
            f"The user query we are trying to help with is:\n{formatted_chat_input}"
        )
        user_message_string += "The following information has been gathered so far:\n"
        for tool_call_entry in information_call_list:  # type: ignore
            user_message_string += (
                f"{tool_call_entry.formatted_tool_output.tool_output}\n"  # type: ignore
            )
        if candidate_answer is not None and answer_judgement is not None:
            user_message_string += f"The attempted answer is:\n{candidate_answer}\n"
            user_message_string += (
                f"The answer revealed the following feedback:\n"
                f"Complete: {answer_judgement.completeness_feedback}\n"
                f"Supported: {answer_judgement.support_feedback}\n"
            )
        user_message_string += f"You have decided to call the tool {chosen_tool.name}."
        user_message_string += chosen_prompt
        user_message_string += f"An  information exploration agent has the following advice:\n{information_exploration_result.text}"
        user_message_string += (
            f"As a reminder, the user query is:\n{formatted_chat_input}"
        )
        messages.append([TextPrompt(text=user_message_string)])
        return messages

    def get_first_turn_information(
        self,
        current_agent_log: AgentLog,
        chat_input: ChatPromptInput,
    ) -> InformationAgentResult:
        """Get the first turn information."""

        # Perform top level directory search
        directory_tool_call = ToolCall(
            tool_name=self.directory_subtree_tool.name,
            tool_input={"directories": [""]},
            tool_call_id="",
        )
        directory_tool_output, _ = self.directory_subtree_tool.activate_tool(
            directory_tool_call
        )
        formatted_directory_tool_output, _ = (
            self.directory_subtree_tool.format_tool_result(
                tool_result=directory_tool_output
            )
        )
        directory_tool_entry = ToolCallEntry(
            tool_call=directory_tool_call,
            tool_result=directory_tool_output,
            formatted_tool_output=formatted_directory_tool_output,
        )
        current_agent_log.register_tool_call(directory_tool_entry)

        # Perform retrieval with chat query
        retrieval_tool_call = ToolCall(
            tool_name=self.codebase_retrieval_tool.name,
            tool_input={"code_section_requests": [{"description": chat_input.message}]},
            tool_call_id="",
        )
        retrieval_tool_output, _ = self.codebase_retrieval_tool.activate_tool(
            retrieval_tool_call
        )
        formatted_retrieval_tool_output, _ = (
            self.codebase_retrieval_tool.format_tool_result(
                tool_result=retrieval_tool_output
            )
        )
        retrieval_tool_entry = ToolCallEntry(
            tool_call=retrieval_tool_call,
            tool_result=retrieval_tool_output,
            formatted_tool_output=formatted_retrieval_tool_output,
        )
        current_agent_log.register_tool_call(retrieval_tool_entry)

        result = InformationAgentResult(
            information_call_result=[
                directory_tool_entry,
                retrieval_tool_entry,
            ],
        )
        current_agent_log.register_result(result)

        return result


def format_chat_input(chat_input: ChatPromptInput, chat_history_len: int = 0) -> str:
    """Format a chat input for use in a prompt."""
    cur_message = chat_input.message

    # Clip chat history to keep it within a budget
    chat_history, _ = format_chat_history(chat_input, CharCounter(), chat_history_len)

    # Handle selected code in chat history.
    # Following the chat prompt formatter pattern, we insert selected code
    # at the point where it was first selected in the conversation.
    # This is typically specified by `context_code_exchange_request_id`.
    # There are two special cases:
    # 1. Code selected in current turn: add to current message
    # 2. Selected code's original turn was trimmed from chat history:
    #    add as new turn at start of history
    if len(chat_input.selected_code) > 0:
        selected_code_section = f"""\
File {chat_input.path} is open in the editor and the following code is selected:

```
{chat_input.selected_code}
```
"""
        # See https://github.com/augmentcode/augment/blob/main/base/prompt_format_chat/lib/selected_code_prompt_formatter_v2.py#L130
        selected_code_response_message = "Noted. I'll output exactly the modified selected code for any edit requests. I won't add context from outside the selected code. I won't delete any context from inside the selected code, unless this is relevant to addressing the instruction."
        context_code_exchange_request_id = chat_input.context_code_exchange_request_id
        cur_message, chat_history = inject_selected_code_into_chat_history(
            cur_message,
            chat_history,
            selected_code_section,
            context_code_exchange_request_id,
            selected_code_response_message,
        )

    formatted_message = ""
    if len(chat_history) > 0:
        formatted_message += "Chat history:\n"
        for exchange in chat_history:
            formatted_message += f"User: {exchange.request_message}\n"
            formatted_message += f"Assistant: {exchange.response_text}\n"

    formatted_message += f"User: {cur_message}\n"
    return formatted_message

from collections import OrderedDict
from dataclasses import dataclass
from fnmatch import fnmatch
from pathlib import Path
from textwrap import dedent
from typing import Any, <PERSON><PERSON>

from dataclasses_json import DataClassJsonMixin

from base.prompt_format.common import PromptChunk
from base.prompt_format_chat.lib.abstract_formatted_file import AbstractFormattedFile
from base.prompt_format_chat.lib.formatted_file_v2 import FormattedFileV2
from base.prompt_format_chat.lib.token_counter import Rough<PERSON><PERSON><PERSON>ounter
from base.prompt_format_retrieve.prompt_formatter import ChatRetrieverPromptInput
from research.core.types import Chunk, Document
from research.data.external_context.search_slack import SlackMessage, SlackSearchClient
from research.llm_apis.llm_client import (
    ToolCall,
    ToolFormattedResult,
    ToolParam,
    ToolResult,
)
from research.retrieval.types import DocumentIndex


class Tool:
    """A tool that can be called by an LLM. Typically used in the context of agent
    functions which contain model calls. Tool activations can call agent"""

    name: str
    tool_description: str

    def get_input_schema(self):
        raise NotImplementedError

    def get_tool_param(self):
        tool = ToolParam(
            name=self.name,
            description=self.tool_description,
            input_schema=self.get_input_schema(),
        )
        return tool

    def activate_tool(self, tool_call: ToolCall) -> Tuple[ToolResult, dict[str, Any]]:
        raise NotImplementedError

    def format_tool_result(
        self, tool_result: ToolResult
    ) -> Tuple[ToolFormattedResult, dict[str, Any]]:
        raise NotImplementedError


class AnswerTool(Tool):
    name = "answer"

    tool_description = dedent(
        """\
        This returns the answer to the agent.
        Use when you are extremely confident the answer is correct, do not call otherwise.
        Provide the answer and the chain of reasoning used to arrive at the answer, with supporting code snippets or documentation.
        For each step in the chain, include:
        - A short description of the step.
        - A code or documentation snippet that supports the step.
        - The full file path where the code snippet comes from.
        """
    )

    answer_description = dedent(
        """\
        The answer to the question, along with the chain of reasoning used to arrive at the answer, with supporting code snippets or documentation.
        For each step in the chain, include:
        - A short description of the step.
        - A code or documentation snippet that supports the step.
        - The full file path where the code snippet comes from.
        """
    )

    def __init__(self):
        pass

    def get_input_schema(self):
        return {
            "type": "object",
            "properties": {
                "answer": {
                    "type": "string",
                    "description": self.answer_description,
                }
            },
            "required": ["answer"],
        }

    def get_tool_param(self):
        tool = ToolParam(
            name=self.name,
            description=self.tool_description,
            input_schema=self.get_input_schema(),
        )
        return tool


class CodebaseRetrievalTool(Tool):
    name = "ask_for_codebase_snippets"

    tool_description = dedent(
        """\
        When asked about a particular object or code section, this will use a retriever to look up snippets matching the description and optionally filters for snippets that match a path in the codebase and/or contain a string
        Use when you need more codebase-specific information to arrive at a correct answer.
        Do not use this for general programming questions, you already know about that.
        Make sure to ask for concrete sections of code, do not ask high-level questions.
        If you need to know how multiple parts of the codebase interact, ask about each part separately.
        Ask for at most three sections at one time.
        """
    )

    request_code_description = dedent(
        """\
        Description of the codebase section or snippet to ask for.
        Favor short low-level descriptions over high-level questions.
        Do not put paths in here - use the path field instead if you want to specify a path.

        Examples of good uses of description:
        {'description': 'inherits from foo class'}
        {'description': 'test for MainClass'}
        {'description': 'display of purchase button'}

        Examples of bad uses of description:
        Too high level
        {'description': 'code that deals with customers'}
        Describes multiple sections likely to be in different parts of code
        {'description': 'code that deals with customers and orders'}
        """
    )
    request_code_path = dedent(
        """\
        Optional path to the codebase section or snippet to ask for.
        May be a full path or a partial path (e.g. "**/foo.*").
        Do NOT specify unless you are completely certain the path exists in the codebase, either from previously seen snippets or from the subtree directory tool.
        If you are not certain, just leave out this field.

        Examples of good uses of path:
        You have seen that foo class is in a folder foo with subfolders and files.
        {'description': 'inherits from foo class', 'path': '**/foo/**'}
        The user asked "foo in src".
        {'description': 'foo', 'path': '**/src/**'}

        Examples of bad uses of path:
        Highly specific location. This is bad unless you are very sure the right snippet should be in this location.
        {'description': 'tests for MainClass', 'path': '**/foo/bar/test_main_class.py'}
        You are just guessing that folder foo exists with interesting files. In this case, instead leave out the path until you know for sure.
        {'description': 'inherits from foo class', 'path': '**/foo/**'}
        """
    )
    request_code_contains_string = dedent(
        """\
        Optional field to specify a string that must be present in the snippet.
        If specified, will reject any snippets that do not contain this string.
        Ask when you are very sure the right snippet should contain this string.
        The main reason to use this field is when the user explicitly asks for a specific string, e.g. a variable or an error.
        Other less common reasons to use this field are when you are looking for type of object that is always annotated with a specific string.
        Be VERY conservative about using this field for any reason other than the user explicitly asking for a specific string.
        Do NOT use this field unless you are COMPLETELY sure.

        Examples of good uses of contains_string:
        You are looking for a specific log line.
        {'description': 'baz exception log line', 'contains_string': 'type not found'}
        The user asked "foo_var in src".
        {'description': 'foo_var', 'path': '**/src/**', 'contains_string': 'foo_var'}

        Examples of bad uses of contains_string:
        You are not sure the right snippet should contain this string.
        {'description': 'inherits from foo class', 'contains_string': 'foo'}
        The user asked a general question: "test of MainClass", but you don't know what this test is called. Don't guess, leave out the contains_string.
        {'description': 'test of MainClass', 'contains_string': 'MainClassTest'}
        """
    )

    def __init__(
        self,
        retriever: DocumentIndex[ChatRetrieverPromptInput],
        max_tool_chars,
        max_num_chunks=20,
    ):
        self.retriever = retriever
        self.max_num_chunks = max_num_chunks
        self.max_tool_chars = max_tool_chars

    def get_input_schema(self):
        return {
            "type": "object",
            "properties": {
                "code_section_requests": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "description": {
                                "type": "string",
                                "description": self.request_code_description,
                            },
                            "path": {
                                "type": "string",
                                "description": self.request_code_path,
                            },
                            "contains_string": {
                                "type": "string",
                                "description": self.request_code_contains_string,
                            },
                        },
                        "required": [
                            "description",
                        ],
                    },
                },
            },
            "required": ["code_section_requests"],
        }

    def retrieve(  # type: ignore
        self, request_description: str, request_path: str, request_contains_string: str
    ) -> tuple[list[Chunk], list[float]]:
        """Perform retrieval for the current state."""
        query = ChatRetrieverPromptInput(
            prefix="",
            suffix="",
            path="",
            message=request_description,
            selected_code="",
            chat_history=[],
        )
        chunks, scores = self.retriever.query(query, top_k=None)
        filtered_chunks = []
        for chunk in chunks:
            if (
                chunk.path is not None
                and request_path != ""
                and not fnmatch(chunk.path, request_path)
                and not fnmatch("/" + chunk.path, request_path)
            ):
                continue
            if (
                request_contains_string != ""
                and request_contains_string not in chunk.text
            ):
                continue
            filtered_chunks.append(chunk)
        output_chunks = filtered_chunks[: self.max_num_chunks]
        return output_chunks, scores

    def activate_tool(self, tool_call: ToolCall) -> Tuple[ToolResult, dict[str, Any]]:
        tool_input_dict = tool_call.tool_input
        assert isinstance(tool_input_dict, dict)
        tool_content = []
        for code_section_request in tool_input_dict["code_section_requests"]:
            assert isinstance(code_section_request, dict), code_section_request
            request_description = code_section_request["description"]
            request_path = (
                code_section_request["path"] if "path" in code_section_request else ""
            )
            request_contains_string = (
                code_section_request["contains_string"]
                if "contains_string" in code_section_request
                else ""
            )
            chunks, scores = self.retrieve(
                request_description=request_description,
                request_path=request_path,
                request_contains_string=request_contains_string,
            )
            tool_content.append(
                {
                    "description": request_description,
                    "path": request_path,
                    "retrieved_chunks": chunks,
                }
            )

        tool_output = ToolResult(
            tool_name=self.name,
            tool_call_id=tool_call.tool_call_id,
            tool_output=tool_content,
        )
        metadata = {}
        return tool_output, metadata

    def format_tool_result(
        self, tool_result: ToolResult
    ) -> Tuple[ToolFormattedResult, dict[str, Any]]:
        tool_content = tool_result.tool_output
        assert isinstance(tool_content, list)

        tool_output_text = "The following code sections were retrieved:\n"
        max_request_chars = self.max_tool_chars // len(tool_content)
        all_used_chunks = []
        for code_section_request_response in tool_content:
            description = code_section_request_response["description"]
            path = code_section_request_response["path"]
            retrieved_chunks = code_section_request_response["retrieved_chunks"]
            tool_output_text += f"Request code description: {description}\n"
            if path != "":
                tool_output_text += f"Request code path: {path}\n"
            retrieval_text, used_chunks = self.format_retrieval(
                retrieved_chunks, max_request_chars
            )
            tool_output_text += retrieval_text
            all_used_chunks.extend(used_chunks)
            if len(retrieved_chunks) < self.max_num_chunks:
                tool_output_text += (
                    "No more results found that match request criteria.\n"
                )
            else:
                tool_output_text += (
                    "More results found, but only the top results are shown.\n"
                )

        metadata = {"used_chunks": all_used_chunks}
        return ToolFormattedResult(
            tool_call_id=tool_result.tool_call_id,
            tool_name=tool_result.tool_name,
            tool_output=tool_output_text,
        ), metadata

    def format_retrieval(
        self, chunks: list[Chunk], char_budget: int
    ) -> Tuple[str, list[Chunk]]:
        retrieval_string = ""
        used_chunks: list[Chunk] = []
        unneeded_token_counter = RoughTokenCounter()
        formatted_files: dict[str, AbstractFormattedFile] = OrderedDict()
        char_count = 0
        for chunk in chunks:
            # construct PromptChunk
            prompt_chunk = PromptChunk(
                text=chunk.text,
                path=chunk.path or "",
                unique_id=chunk.id,
                origin="dense_retriever",
                char_start=chunk.char_offset,
                char_end=chunk.char_offset + chunk.length,
                blob_name=chunk.parent_doc.id,
            )
            file_id = chunk.parent_doc.id
            if file_id not in formatted_files:
                old_formatted_file = FormattedFileV2(unneeded_token_counter)
            else:
                old_formatted_file = formatted_files[file_id]

            new_formatted_file = old_formatted_file.add_chunk(prompt_chunk)
            new_len = len(new_formatted_file.get_file_str_and_tok_ct()[0])
            old_len = len(old_formatted_file.get_file_str_and_tok_ct()[0])
            added_len = new_len - old_len
            if char_count + added_len > char_budget:
                break

            char_count += added_len
            formatted_files[file_id] = new_formatted_file
            used_chunks.append(chunk)

        for formatted_file in formatted_files.values():
            file_str, _ = formatted_file.get_file_str_and_tok_ct()
            file_path = formatted_file.sorted_chunks[0].path
            retrieval_string += f"Path: {file_path}\n{file_str}\n"

        return retrieval_string, used_chunks


class DirectorySubTreeTool(Tool):
    name = "ask_for_directory_subtree"

    tool_description = dedent(
        """\
        When asked about a directory, this will return the names of all subdirectories and files in that directory, recursively, until a character limit is reached.
        Use when you need to know what's in a directory or repository.
        Provide the directory to ask about.
        Lean towards being less specific rather than more specific.
        Instead of asking about a specific file or directory, ask about the parent directory or even higher level directory.
        """
    )

    request_directories = dedent("""Directories to ask about.""")

    def __init__(self, files, max_directory_chars, max_file_chars):
        self.max_directory_chars = max_directory_chars
        self.max_file_chars = max_file_chars
        self.set_files(files)

    def set_files(self, files):
        self.directory_structure = self.create_directory_structure(files)

    @staticmethod
    def create_directory_structure(files: list[Document]):
        directory_structure = {"": {"files": [], "dirs": [], "dir_name_set": set()}}
        file_paths = [Path(file.path) for file in files]

        for path in file_paths:
            is_file = True
            while path.name != "":
                parent = path.parent
                if str(parent) not in directory_structure:
                    directory_structure[str(parent)] = {
                        "files": [],
                        "dirs": [],
                        "dir_name_set": set(),
                    }

                # Check if already added
                if path.name not in directory_structure[str(parent)]["dir_name_set"]:
                    if is_file:
                        directory_structure[str(parent)]["files"].append(path)
                    else:
                        directory_structure[str(parent)]["dirs"].append(path)
                        directory_structure[str(parent)]["dir_name_set"].add(path.name)
                path = parent
                is_file = False

        # Sort alphabetically
        for key in directory_structure:
            directory_structure[key]["files"].sort()
            directory_structure[key]["dirs"].sort()

        return directory_structure

    def get_input_schema(self):
        return {
            "type": "object",
            "properties": {
                "directories": {
                    "type": "array",
                    "items": {
                        "type": "string",
                    },
                    "description": self.request_directories,
                },
            },
            "required": ["directories"],
        }

    def activate_tool(self, tool_call: ToolCall) -> Tuple[ToolResult, dict[str, Any]]:
        tool_input_dict = tool_call.tool_input
        assert isinstance(tool_input_dict, dict)
        tool_content = []
        for directory in tool_input_dict["directories"]:
            tool_content.append(
                {
                    "starting_directory": directory,
                }
            )

        tool_output = ToolResult(
            tool_name=self.name,
            tool_call_id=tool_call.tool_call_id,
            tool_output=tool_content,
        )
        metadata = {}
        return tool_output, metadata

    def format_tool_result(
        self, tool_result: ToolResult
    ) -> Tuple[ToolFormattedResult, dict[str, Any]]:
        tool_output_text = ""
        for directory_call in tool_result.tool_output:
            starting_directory = directory_call["starting_directory"]

            if starting_directory == "":
                tool_output_text += "The directory subtree is:\n"
            else:
                tool_output_text += (
                    f"The directory subtree starting from {starting_directory} is:\n"
                )

            format_directory_text, format_metadata = self.format_directory_subtree(
                starting_dir_str=starting_directory,
                max_directory_chars=self.max_directory_chars,
                max_file_chars=self.max_file_chars,
            )
            tool_output_text += format_directory_text
            truncated = format_metadata["truncated"]
            if truncated:
                tool_output_text += "\n"
                tool_output_text += dedent(
                    """\
                    Keep in mind that this may not be a full directory subtree, only up to a character limit.
                    If a character limit was reached such that not all files or subdirectories can be displayed, that is indicated with '...'.
                    If there are no ... under a directory, that means that all subdirectories and files were displayed.
                    """
                )
            else:
                tool_output_text += "\n"
                tool_output_text += "All subdirectories and files were displayed.\n"

        metadata = {}
        return ToolFormattedResult(
            tool_call_id=tool_result.tool_call_id,
            tool_name=tool_result.tool_name,
            tool_output=tool_output_text,
        ), metadata

    def format_directory_subtree(
        self,
        starting_dir_str: str,
        max_directory_chars: int,
        max_file_chars: int,
        max_depth: int = 50,
    ):
        spaces_per_level = 2

        # Find max level
        starting_dir = Path(starting_dir_str)
        if str(starting_dir) not in self.directory_structure:
            return f"Directory {starting_dir_str} not found.", {"truncated": False}

        current_dirs = [starting_dir]
        current_level_idx = 0
        total_dir_char_count = 0
        total_file_char_count = 0

        max_dirs_reached = False
        max_files_reached = False

        max_dir_level = -1
        max_file_level = -1

        for idx in range(max_depth):
            if len(current_dirs) == 0:
                break

            # Reset for next level
            current_dir_char_count = 0
            current_file_char_count = 0
            next_dirs = []

            for dir in current_dirs:
                subtree = self.directory_structure[str(dir)]
                files = subtree["files"]
                dirs = subtree["dirs"]

                for file in files:
                    current_file_char_count += (
                        len(file.name) + spaces_per_level * current_level_idx
                    )

                for dir in dirs:
                    current_dir_char_count += (
                        len(dir.name) + spaces_per_level * current_level_idx
                    )
                    next_dirs.append(dir)

            if (
                not max_dirs_reached
                and total_dir_char_count + current_dir_char_count > max_directory_chars
            ):
                max_dir_level = current_level_idx - 1
                max_dirs_reached = True
            if (
                not max_files_reached
                and total_file_char_count + current_file_char_count > max_file_chars
            ):
                max_file_level = current_level_idx - 1
                max_files_reached = True

            if max_dirs_reached:
                break
            elif len(next_dirs) == 0:
                if not max_dirs_reached:
                    max_dir_level = current_level_idx
                if not max_files_reached:
                    max_file_level = current_level_idx
            else:
                current_level_idx += 1
                current_dirs = next_dirs
                total_dir_char_count += current_dir_char_count
                total_file_char_count += current_file_char_count

        text = ""
        current_dirs = [starting_dir]

        def compute_count(starting_dir):
            stack = [starting_dir]
            file_count = 0
            dir_count = 0

            while len(stack) > 0:
                current_dir = stack.pop()
                subtree = self.directory_structure[str(current_dir)]
                files = subtree["files"]
                dirs = subtree["dirs"]

                for file in files:
                    file_count += 1

                for dir in dirs:
                    dir_count += 1
                    stack.append(dir)

            return file_count, dir_count

        def create_print_str(starting_dir):
            stack = []
            subtree = self.directory_structure[str(starting_dir)]
            if str(starting_dir) in self.directory_structure:
                stack.append((starting_dir, "dir", -1))
            dirs = subtree["dirs"]
            files = subtree["files"]

            text = ""

            while len(stack) > 0:
                current_entry, entry_type, level = stack.pop()
                single_indent = " " * spaces_per_level
                indent = single_indent * level
                if level >= 0:
                    text += indent + f"{current_entry.name}\n"
                if entry_type == "dir":
                    subtree = self.directory_structure[str(current_entry)]
                    dirs = subtree["dirs"]
                    files = subtree["files"]

                    if level >= max_dir_level:
                        text += indent + single_indent + "..." + "\n"

                    else:
                        # Add children in reverse order to keep the order consistent with the input
                        if level <= max_file_level - 1:
                            for file in reversed(files):
                                stack.append((file, "file", level + 1))

                        for dir in reversed(dirs):
                            stack.append((dir, "dir", level + 1))

            return text

        text = create_print_str(starting_dir)

        format_metadata = {
            "truncated": max_dirs_reached or max_files_reached,
        }
        return text, format_metadata


class ChainOfThoughtTool(Tool):
    name = "chain_of_thought"

    tool_description = dedent(
        """\
        This gives you the opportunity to perform reasoning necessary to arrive at the answer.
        """
    )

    new_information_processing_description = dedent(
        """\
        Think carefully about the new information you have been provided and how it relates to the question.
        Does it fill in any gaps in your current chain of thought?
        """
    )
    chain_of_thought_description = dedent(
        """\
        Write carefully and out loud a chain of thought answer to the question.
        Each step in the change should contain:
        - A short description of the step.
        - A code or documentation snippet that supports the step.
        - The full file path where the code snippet comes from
        If you cannot find a successful chain all the way to a satisfactory answer, mark where you get stuck.
        """
    )
    evaluation_description = dedent(
        """\
        Write whether the answer is complete or not, and how confident you are in the answer.
        A. Are you less than fully confident in your answer?
            For example, if there are some steps missing in the chain, missing implementation details, or if existing steps are missing snippets or have phrases such as "likely".
            Then attempt to obtain information to add a new step to the chain, or to improve an existing step.
            Be very conservative, if you have any doubt at all, get more information.
            Do this by preparing the information necessary to call the tool "ask_for_codebase_snippets" to get more information.
            Prepare the tool call with a description of the code section that would help add another step to the chain
            Write the result here, but do not call the tool yet, there will be another turn for that.
        B. Are you very confident in your answer?
            Then prepare the information necessary to call the tool "answer" to get the final answer and write it down here.
            Prepare the tool call with the answer and the chain of reasoning used to arrive at the answer, with supporting code snippets or documentation.
            Write the result here, but do not call the tool yet, there will be another turn for that.
        """
    )

    def __init__(self):
        pass

    def get_input_schema(self):
        return {
            "type": "object",
            "properties": {
                "new_information_processing": {
                    "type": "string",
                    "description": self.new_information_processing_description,
                },
                "chain_of_thought": {
                    "type": "string",
                    "description": self.chain_of_thought_description,
                },
                "evaluation": {
                    "type": "string",
                    "description": self.evaluation_description,
                },
            },
            "required": [
                "new_information_processing",
                "chain_of_thought",
                "evaluation",
            ],
        }

    def activate_tool(self, tool_call: ToolCall) -> Tuple[ToolResult, dict[str, Any]]:
        tool_input_dict = tool_call.tool_input
        assert isinstance(tool_input_dict, dict)
        new_information_processing = tool_input_dict["new_information_processing"]
        chain_of_thought = tool_input_dict["chain_of_thought"]
        evaluation = tool_input_dict["evaluation"]
        content = {
            "new_information_processing": new_information_processing,
            "chain_of_thought": chain_of_thought,
            "evaluation": evaluation,
        }
        tool_output = ToolResult(
            tool_name=self.name,
            tool_call_id=tool_call.tool_call_id,
            tool_output=content,
        )
        return tool_output, {}

    def format_tool_result(
        self, tool_result: ToolResult
    ) -> Tuple[ToolFormattedResult, dict[str, Any]]:
        content = tool_result.tool_output
        new_information_processing = content["new_information_processing"]
        chain_of_thought = content["chain_of_thought"]
        evaluation = content["evaluation"]
        text = (
            f"New information processing: {new_information_processing}\n"
            f"Chain of thought: {chain_of_thought}\n"
            f"Evaluation: {evaluation}"
        )
        return ToolFormattedResult(
            tool_call_id=tool_result.tool_call_id,
            tool_name=tool_result.tool_name,
            tool_output=text,
        ), {}


class DummyTool(Tool):
    name = "dummy"

    tool_description = dedent(
        """\
        This is a dummy tool that does nothing. Do not call this tool.
        """
    )

    def __init__(self):
        pass

    def get_input_schema(self):
        return {
            "type": "object",
            "properties": {
                "dummy_input": {
                    "type": "string",
                    "description": "Dummy input",
                },
            },
            "required": ["dummy_input"],
        }


class JudgeAnswerTool(Tool):
    name = "judge_answer"

    tool_description = dedent(
        """\
        Call this tool with feedback on whether an answer is complete, confident, and supported by the chain of thought.
        """
    )

    completeness_feedback_description = dedent(
        """\
        Provide verbal feedback on whether the answer is complete and why.
        If the answer is not complete, provide feedback on what is missing.
        If the answer states the model could not find something or mentions lack of evidence, the answer is not complete.
        Even if the answer explains reasonably why that is the case, the answer is still not complete.
        An answer is complete if and only if it successfully answers the full user question, without any speculation.
        If a question is exhaustive, e.g. 'find all' or 'find the classes that', the answer should not only show that the results it finds are valid, but also that there are no other results, by e.g. showing the files in the directory.
        Some example signs of incomplete answers:
            "I couldn't find foo.py, but bar.py looks relevant. bar.py contains..."
            "I only see ..."
            "I don't see any evidence of ..."
            "baz is not in the provided files"
        """
    )
    is_complete_description = dedent(
        """\
        Taking into account your verbal feedback, provide judgement on whether the answer is complete.
        If the model answers it could not find something or mentions lack of evidence, the answer is not complete.
        """
    )
    support_feedback_description = dedent(
        """\
        Provide verbal feedback on whether the answer is supported by the chain of thought and if each step in the chain of thought is valid.
        For each step in the chain of thought, check and write:
        - Does the snippet originate from a relevant part of the codebase?
            For example, if a question asks about 'classes in foo', exerpts in 'bar' may not be relevant even though they are similar.
        - Can you actually find the snippet in the supporting information, or did the answer hallucinate it?
        - Does the snippet support the step?
        - Is the snippet relevant to the question?

        Also, is the conclusion supported by the entire chain of thought?
        Be particularly vigilant about whether the model implies it is missing any information.
        Examples that almost certainly mean there is missing information:
            "However, we don't have access to ..."
            "... is not in the provides files"
            "... would be helpful to"
            "While we did not see x, y suggests ..."
        """
    )
    is_supported_description = dedent(
        """\
        Taking into account your verbal feedback, provide judgement on whether the answer is supported by the chain of thought.
        """
    )

    def get_input_schema(self):
        return {
            "type": "object",
            "properties": {
                "completeness_feedback": {
                    "type": "string",
                    "description": self.completeness_feedback_description,
                },
                "is_complete": {
                    "type": "boolean",
                    "description": self.is_complete_description,
                },
                "support_feedback": {
                    "type": "string",
                    "description": self.support_feedback_description,
                },
                "is_supported": {
                    "type": "boolean",
                    "description": self.is_supported_description,
                },
            },
            "required": [
                "completeness_feedback",
                "is_complete",
                "support_feedback",
                "is_supported",
            ],
        }

    def activate_tool(self, tool_call: ToolCall) -> Tuple[ToolResult, dict[str, Any]]:
        return ToolResult(
            tool_name=self.name,
            tool_call_id=tool_call.tool_call_id,
            tool_output=tool_call.tool_input,
        ), {}


class ToolSelectionTool(Tool):
    name = "tool_selection"

    tool_description = dedent(
        """\
        Call this tool to specify the tool that should be called next.
        Choose the tool that will most help you arrive at the answer.
        """
    )

    tool_name_description = dedent(
        """\
        The name of the tool to call next.
        ask_for_directory_subtree:
            Call this tool to receive information about the directory structure.
            Generally useful to call first to guide the codebase search.
        ask_for_codebase_snippets:
            Call this tool to receive information about the codebase.
            Generally useful to call second to get the actual code.
        """
    )

    def get_input_schema(self):
        return {
            "type": "object",
            "properties": {
                "tool_name": {
                    "type": "string",
                    "enum": [
                        "ask_for_codebase_snippets",
                        "ask_for_directory_subtree",
                    ],
                    "description": self.tool_name_description,
                },
            },
            "required": ["tool_name"],
        }


class SlackSearchTool(Tool):
    name = "search_slack"
    tool_description = dedent(
        """\
        Call this tool to search Slack for messages.
        """
    )
    query_description = dedent(
        """\
        The query to search for.
        """
    )

    def __init__(self, max_tool_chars: int):
        self.client = SlackSearchClient()
        self.max_tool_chars = max_tool_chars

    def get_input_schema(self):
        return {
            "type": "object",
            "properties": {
                "query": {
                    "type": "string",
                    "description": self.query_description,
                },
            },
            "required": ["query"],
        }

    def activate_tool(self, tool_call: ToolCall) -> Tuple[ToolResult, dict[str, Any]]:
        tool_input_dict = tool_call.tool_input
        assert isinstance(tool_input_dict, dict)
        query = tool_input_dict["query"]
        message_results = self.client.search_messages(query, count=100)
        content = {
            "query": query,
            "message_results": message_results,
        }
        tool_output = ToolResult(
            tool_name=self.name,
            tool_call_id=tool_call.tool_call_id,
            tool_output=content,
        )
        return tool_output, {}

    def format_tool_result(
        self, tool_result: ToolResult
    ) -> Tuple[ToolFormattedResult, dict[str, Any]]:
        tool_output = tool_result.tool_output
        assert isinstance(tool_output, dict)
        query = tool_output["query"]
        tool_output_text = f"Search query: {query}"

        for message in tool_output["message_results"]:
            formatted_message = self.format_message(message)
            if len(tool_output_text) + len(formatted_message) > self.max_tool_chars:
                break
            tool_output_text += f"\n\n{formatted_message}"

        return ToolFormattedResult(
            tool_name=self.name,
            tool_call_id=tool_result.tool_call_id,
            tool_output=tool_output_text,
        ), {}

    def format_message(self, message: SlackMessage) -> str:
        format_str = ""
        format_str += f"Slack message from {message.user} in {message.channel_name} at {message.timestamp}:\n"
        format_str += f"{message.text}"
        return format_str


class QueryRouterTool(Tool):
    name = "query_router"

    tool_description = dedent(
        """\
        Call this tool to specify whether the question is a general programming question that can be answered without looking at the codebase, or a question specific to a particular codebase.
        """
    )

    is_general_question_description = dedent(
        """\
        Whether the question is a general programming question, as opposed to a question that relates to a specific codebase.
        Be conservative: if you are not sure, assume it is not a general programming question.

        Example of a general programming question:
        "What is the difference between a list and a tuple in Python?"
        Example of a question specific to a codebase:
        "What is the purpose of the foo class in the bar module?"
        Example of ambiguous question where you should assume it is not general:
        "What is the purpose of foo?" where you don't know what foo is
        """
    )

    def get_input_schema(self):
        return {
            "type": "object",
            "properties": {
                "is_general_question": {
                    "type": "boolean",
                    "description": self.is_general_question_description,
                },
            },
            "required": ["is_general_question"],
        }


class GeneralQueryRouter(Tool):
    name = "general_query_router"

    tool_description = dedent(
        """\
        Call this tool to classify a user query along a number of dimensions.
        """
    )

    category_description = dedent(
        """\
        Whether the question is:
        - A general programming question ("general")
        - About understanding the current file or selected code ("current_file")
        - About editing selected code or the current file ("edit")
        - Requesting a high-level overview of project/components ("overview")
        - About the current codebase ("codebase")

        Possible values: "general", "current_file", "edit", "overview", "codebase"

        Be conservative and assume the question is specific to a codebase ("codebase") unless you are certain it is not. Specifically:
        - Use "edit" when the user explicitly asks to modify selected code or make changes to the current file
        - Use "current_file" when the question is about understanding the current file or selected code (but NOT about editing it)
        - Use "overview" when the user asks for architectural summary, component relationships, or high-level understanding of the project
        - Use "general" only for programming questions that are clearly independent of any specific codebase
        - Use "codebase" for all other questions that may involve understanding or working with the project's code

        Note: When multiple categories could apply:
        - If both "edit" and "current_file" could apply (i.e., the question is about modifying the current file/selected code), use "edit" as it is more specific
        - If both "overview" and "codebase" could apply (i.e., the question is about high-level understanding), use "overview" as it is more specific
        """
    )

    about_specific_file_description = dedent(
        """\
        If the query primarily requires information JUST from specific FEW files, please specify FULL PATHs to these files. Otherwise, just write empty list.
        Some examples where files should be specified:
        - The user asks for the contents of a file
        - The user asks to summarize a file
        - The user asks to write tests for a class in a specific file (both the file and another file with existing test should be specified)
        - The user asks to rewrite a function that calls an API in a specific file (both the file and another file with the API should be specified)
        - The user asks to edit a function in a specific file

        Some examples where the file should not be specified:
        - The user asks for the contents of a directory
        - The user doesn't mention any files
        - The user mentions a file, but it's clear the question is about the codebase as a whole.
        - The user has the file open, but the question is not related to that file.
        """
    )

    def __init__(self, docsets: list[str]):
        super().__init__()
        self.docsets = docsets
        self.external_package_description = dedent(
            f"""\
            If the query is about an external package (e.g. Python, JS, numpy), please specify the name of the package from the list below. Otherwise, just write none.
            The list of packages is: \n{",".join(self.docsets)}
            """
        )

    def get_input_schema(self):
        return {
            "type": "object",
            "properties": {
                "category": {
                    "type": "string",
                    "description": self.category_description,
                },
                "about_specific_file": {
                    "type": "array",
                    "items": {
                        "type": "string",
                    },
                    "description": self.about_specific_file_description,
                },
                "external_package": {
                    "type": "string",
                    "description": self.external_package_description,
                },
            },
            "required": [
                "category",
                "about_specific_file",
                "external_package",
            ],
        }


TOOLS = {
    "answer": AnswerTool,
    "ask_for_codebase_snippets": CodebaseRetrievalTool,
    "ask_for_directory_subtree": DirectorySubTreeTool,
    "chain_of_thought": ChainOfThoughtTool,
    "dummy": DummyTool,
    "judge_answer": JudgeAnswerTool,
    "tool_selection": ToolSelectionTool,
    "search_slack": SlackSearchTool,
    "query_router": QueryRouterTool,
}

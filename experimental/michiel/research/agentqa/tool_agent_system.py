"""System for anthropic agent."""

import dataclasses
import logging
import pickle
import typing
from pathlib import Path
from typing import Any

from experimental.michiel.research.agentqa.agentic_retrieval_agent import (
    AgenticRetrievalChatAgent,
    ChatAgent,
)
from experimental.michiel.research.agentqa.tools_new import (
    ChatanolLLMTool,
    HighLevelCodebaseMultiRetrievalAgent,
    HighLevelCodebaseRetrievalAgent,
    HighLevelCodebaseRetrievalRerankAgent,
    HighLevelCodebase3StepRetrievalAgent,
    ProduceAnswerTool,
    QwenQueryRewriterTool,
)
from research.agents.tools import LoggingLLMClient, ToolCallLogger, LoggedToolCall
from research.core.chat_prompt_input import ResearchChatPromptInput
from research.eval.harness import factories
from research.eval.harness.systems.abs_system import (
    ChatResult,
    ChatSystem,
    register_system,
)
from research.llm_apis.llm_client import (
    get_client,
)
from research.models.all_models import get_model
from research.models.meta_model import ExtraGenerationOutputs
from research.retrieval.types import Document, DocumentId, DocumentIndex

logger = logging.getLogger(__name__)


@register_system("question_answer_tool")
class QuestionAnswerToolSystem(ChatSystem):
    """Agent system."""

    def __init__(
        self,
        agent: ChatAgent,
        retriever: DocumentIndex,
        doc_subscribers: list[Any],
        tool_call_logger: ToolCallLogger,
        verbose: bool = False,
    ):
        self.agent = agent
        self.retriever = retriever
        self.doc_subscribers = doc_subscribers
        self.tool_call_logger = tool_call_logger
        self.verbose = verbose
        self.__loaded = False

        # We provide a dummy model to satisfy the AbstractSystem interface.
        self.model = self.get_model()

    def load(self):
        if not self.__loaded:
            self.retriever.load()
        self.__loaded = True

    def unload(self):
        if self.__loaded:
            self.retriever.unload()
            self.__loaded = False

    def add_docs(self, src_files: typing.Collection[Document]):
        """Ingest a copy of the source code repository.

        Args:
          src_files: list of Documents to add
        """
        logger.info(f"Adding {len(list(src_files))} documents.")
        self.retriever.add_docs(src_files)  # type: ignore
        for subscriber in self.doc_subscribers:
            subscriber.add_docs(src_files)
        doc_ids = self.retriever.get_doc_ids()
        total_docs = len(doc_ids)
        logger.info(f"There are now {total_docs} total docs.")

    def remove_docs(self, doc_ids: typing.Collection[DocumentId]):
        """Remove documents from the retriever."""
        logger.info(f"Removing {len(list(doc_ids))} documents.")
        self.retriever.remove_docs(doc_ids)
        all_doc_ids = self.retriever.get_doc_ids()
        for subscriber in self.doc_subscribers:
            subscriber.remove_docs(doc_ids)
        total_docs = len(all_doc_ids)
        logger.info(f"There are now {total_docs} total docs.")

    def get_doc_ids(self) -> typing.AbstractSet[DocumentId]:
        return self.retriever.get_doc_ids()

    def get_docs(self, doc_ids: typing.Iterable[DocumentId]) -> list[Document]:
        return self.retriever.get_docs(doc_ids)

    def clear_retriever(self):
        """Clear any stored documents from the retriever."""
        doc_ids = self.retriever.get_doc_ids()
        self.retriever.remove_all_docs()
        for subscriber in self.doc_subscribers:
            subscriber.remove_docs(doc_ids)

    def _extract_chunks_from_logged_calls(self):
        """Extract the chunks from the logged calls."""
        unique_chunks, chunks = set(), []
        for logged_call in self.tool_call_logger.logged_calls:
            if isinstance(logged_call, LoggedToolCall):
                retrieved_chunks = logged_call.auxiliary_data.get(
                    "retrieved_chunks", []
                )
                for chunk in retrieved_chunks:
                    if chunk not in unique_chunks:
                        unique_chunks.add(chunk)
                        chunks.append(chunk)
        return chunks

    def generate(
        self,
        model_input: ResearchChatPromptInput,
    ) -> ChatResult:
        """Generate a completion."""

        self.tool_call_logger.logged_calls = []
        answer = self.agent.run(model_input).output
        extra_output = ExtraGenerationOutputs(
            additional_info={
                "output_state": None,
                "logged_calls": self.tool_call_logger.get_html_representation(
                    truncate_long_outputs=False, use_tool_supplied_messages=True
                ),
            },
        )
        retrieved_chunks = self._extract_chunks_from_logged_calls()
        chat_result = ChatResult(
            generated_text=answer,
            prompt_tokens=[],
            retrieved_chunks=retrieved_chunks,
            extra_output=extra_output,
        )

        return chat_result

    def get_model(self):
        return get_model("null")

    @dataclasses.dataclass
    class _AgentSystemBuildConfig:
        """Schema for constructing a System."""

        retriever_config: dict
        model_config: dict
        agent_config: dict
        tool_config: dict = dataclasses.field(default_factory=dict)
        verbose: bool = False


@register_system("question_answer_agentic_retrieval_tool")
class QuestionAnswerAgenticRetrievalSystem(QuestionAnswerToolSystem):
    """Low latency agent system."""

    @classmethod
    def from_yaml_config(cls, config: dict) -> "QuestionAnswerAgenticRetrievalSystem":
        """Returns a System object constructed using a config dictionary."""
        xconfig = cls._AgentSystemBuildConfig(**config)
        retriever = factories.create_retriever(xconfig.retriever_config)
        tool_call_logger = ToolCallLogger()

        answer_tool = ProduceAnswerTool(tool_call_logger=tool_call_logger)
        retrieval_llm_client = get_client(**xconfig.model_config["retrieval"])
        logging_retrieval_llm_client = LoggingLLMClient(
            client=retrieval_llm_client, tool_call_logger=tool_call_logger
        )
        tool_config = xconfig.tool_config
        max_codebase_tool_chars = tool_config.pop("max_codebase_tool_chars", 50000)
        max_rerank_input_chars = tool_config.pop("max_rerank_input_chars", None)
        tool_type = tool_config.pop("tool_type", "base")

        def get_tool_type(tool_type: str, max_codebase_tool_chars: int):
            doc_subscribers = []
            if tool_type == "rerank":
                rerank_llm_client = get_client(**xconfig.model_config["rerank"])
                logging_rerank_llm_client = LoggingLLMClient(
                    client=rerank_llm_client, tool_call_logger=tool_call_logger
                )
                retrieval_tool = HighLevelCodebaseRetrievalRerankAgent(
                    tool_call_logger=tool_call_logger,
                    retriever=retriever,
                    llm_client=logging_retrieval_llm_client,
                    rerank_client=logging_rerank_llm_client,
                    max_tool_chars=max_codebase_tool_chars,
                    max_rerank_input_chars=max_rerank_input_chars,
                )
            elif tool_type == "base":
                retrieval_tool = HighLevelCodebaseRetrievalAgent(
                    tool_call_logger=tool_call_logger,
                    retriever=retriever,
                    llm_client=logging_retrieval_llm_client,
                    max_tool_chars=max_codebase_tool_chars,
                )
            elif tool_type == "multi":
                retrieval_tool = HighLevelCodebaseMultiRetrievalAgent(
                    tool_call_logger=tool_call_logger,
                    retriever=retriever,
                    llm_client=logging_retrieval_llm_client,
                    max_tool_chars=max_codebase_tool_chars,
                    max_intermediate_tool_chars=tool_config.pop(
                        "max_intermediate_tool_chars", 100_000
                    ),
                )
            elif tool_type == "3step":
                max_initial_retrieval_chars = tool_config.pop(
                    "max_initial_retrieval_chars", None
                )
                assert max_initial_retrieval_chars is not None
                max_directory_chars = tool_config.pop("max_directory_chars", None)
                assert max_directory_chars is not None
                retrieval_tool = HighLevelCodebase3StepRetrievalAgent(
                    tool_call_logger=tool_call_logger,
                    retriever=retriever,
                    llm_client=logging_retrieval_llm_client,
                    max_intermediate_tool_chars=tool_config.pop(
                        "max_intermediate_tool_chars", 100_000
                    ),
                    max_tool_chars=max_codebase_tool_chars,
                    max_initial_retrieval_chars=max_initial_retrieval_chars,
                    max_directory_chars=max_directory_chars,
                    verbose=tool_config.pop("verbose", False),
                )
            elif tool_type == "chatanol":
                retrieval_tool = ChatanolLLMTool(
                    tool_call_logger=tool_call_logger,
                    retriever=retriever,
                    max_tool_chars=max_codebase_tool_chars,
                )
            elif tool_type == "qwen":
                retrieval_tool = QwenQueryRewriterTool(
                    tool_call_logger=tool_call_logger,
                    retriever=retriever,
                    max_tool_chars=max_codebase_tool_chars,
                    max_init_retrieval_chars=max_codebase_tool_chars // 2,
                )
                doc_subscribers = [retrieval_tool]
            else:
                raise ValueError(f"Unknown tool type: {tool_type}")
            return retrieval_tool, doc_subscribers

        agent_config = xconfig.agent_config
        agent_llm_client = get_client(**xconfig.model_config["agent"])
        logging_llm_client = LoggingLLMClient(
            client=agent_llm_client, tool_call_logger=tool_call_logger
        )
        initial_turn_tool_type = tool_config.pop("initial_turn_tool_type", None)
        initial_turn_max_codebase_tool_chars = tool_config.pop(
            "initial_turn_max_codebase_tool_chars", max_codebase_tool_chars
        )
        initial_turn_tool, _ = (
            get_tool_type(initial_turn_tool_type, initial_turn_max_codebase_tool_chars)
            if initial_turn_tool_type is not None
            else None,
            [],
        )
        retrieval_tool, doc_subscribers = get_tool_type(
            tool_type, max_codebase_tool_chars
        )

        agent = AgenticRetrievalChatAgent(
            llm_client=logging_llm_client,
            answer_tool=answer_tool,
            retrieval_tool=retrieval_tool,
            initial_turn_tool=initial_turn_tool,
            **agent_config,
        )
        return QuestionAnswerAgenticRetrievalSystem(
            agent=agent,
            doc_subscribers=doc_subscribers,
            tool_call_logger=tool_call_logger,
            retriever=retriever,
            verbose=xconfig.verbose,
        )

from research.eval.harness.tasks.augment_qa_task import count_keywords
from research.eval.harness.tasks.augment_qa_utils import <PERSON><PERSON>


def score_formatted_retrievals(
    sample: Sample, formatter_output: str
) -> dict[str, float]:
    n_keywords = float(len(set(sample.keywords)))
    n_matched_keywords = count_keywords(formatter_output, sample.keywords)
    retrievals_keyword_recall = n_matched_keywords / n_keywords

    n_gold_paths = len(sample.gold_paths)
    n_matched_paths = count_keywords(formatter_output, sample.gold_paths)
    gold_paths_recall = n_matched_paths / n_gold_paths

    return {
        "retrievals_keyword_recall": retrievals_keyword_recall,
        "gold_paths_recall": gold_paths_recall,
    }

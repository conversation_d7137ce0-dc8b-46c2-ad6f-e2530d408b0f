from textwrap import dedent
from typing import Any, Tuple

from base.prompt_format_chat.prompt_formatter import (
    ChatPromptInput,
)
from experimental.michiel.research.agentqa.agent import ToolCallEntry, format_chat_input
from research.llm_apis.llm_client import Text<PERSON>rompt, ToolCall


class QueryRewriter:
    def __init__(self, client, codebase_retrieval_tool, chat_history_len, temperature):
        self.client = client
        self.codebase_retrieval_tool = codebase_retrieval_tool
        self.chat_history_len = chat_history_len
        self.temperature = temperature

    def format_prompt(
        self, chat_input: ChatPromptInput, tool_history: list[ToolCallEntry]
    ):
        """Format the prompt for the query rewriter."""

        formatted_chat_input = format_chat_input(
            chat_input,
            chat_history_len=self.chat_history_len,
        )
        # Try to look at the information gathered already, and determine what information is missing.
        # DIVERSIFY YOUR SEARCH -- look for multiple snippets using different descriptions, paths and contains_string. Remember, this is the last chance to find missing information.
        retrieval_tool_prompt = dedent(
            """\
            You are a tool-calling agent that calls a specific tool to gather information to help answer a coding question.
            Please pay careful attention to the tool description to optimally use the tool.
            Make sure to also look at the directory tree and previous codebase snippets to guide your search.
            Look at the information gathered already, and determine what additional information is missing.

            Some additional guidance for using this tool:
            - If the user gives you a log line or variable name, use the contains_string field to look for that exact string. For example, if user asks "error: xyz".
            - Otherwise, almost never use the contains_string field, only when you are absolutely certain the right snippet should contain this string.
            """
        )

        messages = []
        user_message_string = ""
        user_message_string += (
            f"The user query we are trying to help with is:\n{formatted_chat_input}"
        )
        user_message_string += "The following information has been gathered so far:\n"
        for tool_call_entry in tool_history:  # type: ignore
            # We currently expect this only to be called with past directory and retrieval tool, here we enforce that somebody should think about the right thing to do if that changes in the future
            assert tool_call_entry.tool_call.tool_name in [
                "ask_for_directory_subtree",
                "ask_for_codebase_snippets",
            ], f"Unexpected tool call: {tool_call_entry.tool_call.tool_name}"
            user_message_string += (
                f"{tool_call_entry.formatted_tool_output.tool_output}\n"  # type: ignore
            )
        user_message_string += retrieval_tool_prompt
        user_message_string += (
            f"As a reminder, the user query is:\n{formatted_chat_input}"
        )
        messages.append([TextPrompt(text=user_message_string)])
        return messages

    def rewrite_query(
        self, chat_input: ChatPromptInput, tool_history: list[ToolCallEntry]
    ) -> Tuple[list[ToolCall], dict[str, Any]]:
        retrieval_tool_messages = self.format_prompt(
            chat_input=chat_input,
            tool_history=tool_history,
        )

        retrieval_tool_call_results, retrieval_tool_call_metadata = (
            self.client.generate(
                messages=retrieval_tool_messages,
                max_tokens=2048,
                temperature=self.temperature,
                tools=[
                    self.codebase_retrieval_tool.get_tool_param(),
                ],
                tool_choice={"type": "tool", "name": self.codebase_retrieval_tool.name},
            )
        )

        return retrieval_tool_call_results, retrieval_tool_call_metadata

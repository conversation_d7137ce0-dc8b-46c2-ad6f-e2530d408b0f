"""Unit tests for retrieval_formatter.py."""

import pytest

from base.prompt_format.common import PromptChunk
from research.core.types import Chunk, Document
from experimental.michiel.research.agentqa.lib.retrieval_formatter import (
    RetrievalsFormatter,
)


@pytest.fixture
def sample_document1():
    """Create a sample document for testing."""
    return Document(id="doc1", text="Sample document text", path="test.py", meta={})


@pytest.fixture
def sample_document2():
    """Create another sample document for testing."""
    return Document(id="doc2", text="Sample document text", path="file2.py", meta={})


@pytest.fixture
def sample_chunks(sample_document1):
    """Create sample chunks for testing."""
    return [
        Chunk(
            id="chunk1",
            text="def hello():\n    print('world')\n",
            parent_doc=sample_document1,
            char_offset=0,
            length=31,
            line_offset=0,
            length_in_lines=2,
            meta={},
        ),
        Chunk(
            id="chunk2",
            text="def goodbye():\n    print('farewell')\n",
            parent_doc=sample_document1,
            char_offset=32,
            length=35,
            line_offset=2,
            length_in_lines=2,
            meta={},
        ),
    ]


@pytest.fixture
def sample_prompt_chunks():
    """Create sample prompt chunks for testing."""
    return [
        PromptChunk(
            text="def hello():\n    print('world')\n",
            path="test.py",
            unique_id="chunk1",
            origin="dense_retriever",
            char_start=0,
            char_end=31,
            blob_name="doc1",
        ),
        PromptChunk(
            text="def goodbye():\n    print('farewell')\n",
            path="test.py",
            unique_id="chunk2",
            origin="dense_retriever",
            char_start=32,
            char_end=67,
            blob_name="doc1",
        ),
    ]


def test_add_chunks(sample_chunks):
    """Test adding chunks to the formatter."""
    formatter = RetrievalsFormatter()
    formatter.add_chunks(sample_chunks, max_budget=1000)

    assert len(formatter.formatted_files) == 1
    assert "test.py" in formatter.formatted_files
    assert formatter.n_tokens > 0


def test_format_output(sample_chunks):
    """Test the format method output."""
    formatter = RetrievalsFormatter()
    formatter.add_chunks(sample_chunks, max_budget=1000)

    result = formatter.format()
    assert isinstance(result, str)
    assert "Path: test.py" in result
    assert "def hello()" in result
    assert "def goodbye()" in result


def test_max_budget_limit(sample_chunks):
    """Test that the max_budget parameter limits chunk addition."""
    formatter = RetrievalsFormatter()
    # Set a budget that should only allow one chunk
    # Budget needs to account for "Path: test.py\n" + chunk content
    formatter.add_chunks(sample_chunks, max_budget=50)

    result = formatter.format()
    # Should only contain the first chunk
    assert "def hello()" in result
    assert "def goodbye()" not in result


def test_multiple_files(sample_document1, sample_document2):
    """Test handling multiple files."""
    chunks = [
        Chunk(
            id="chunk1",
            text="def hello():\n    print('world')\n",
            parent_doc=sample_document1,
            char_offset=0,
            length=31,
            line_offset=0,
            length_in_lines=2,
            meta={},
        ),
        Chunk(
            id="chunk2",
            text="def goodbye():\n    print('farewell')\n",
            parent_doc=sample_document2,
            char_offset=0,
            length=35,
            line_offset=0,
            length_in_lines=2,
            meta={},
        ),
    ]

    formatter = RetrievalsFormatter()
    formatter.add_chunks(chunks, max_budget=1000)

    result = formatter.format()
    assert "Path: test.py" in result
    assert "Path: file2.py" in result
    assert "def hello()" in result
    assert "def goodbye()" in result


def test_track_added_chunks(sample_chunks):
    """Test that added chunks are tracked correctly."""
    formatter = RetrievalsFormatter()
    formatter.add_chunks(sample_chunks, max_budget=1000)

    assert len(formatter.chunks) == len(sample_chunks)
    assert all(chunk in formatter.chunks for chunk in sample_chunks)


def test_track_chunks_with_budget_limit(sample_chunks):
    """Test that only chunks within budget are tracked."""
    formatter = RetrievalsFormatter()
    # Set a budget that should only allow one chunk
    formatter.add_chunks(sample_chunks, max_budget=50)

    assert len(formatter.chunks) == 1
    assert formatter.chunks[0] == sample_chunks[0]


def test_track_chunks_multiple_files(sample_document1, sample_document2):
    """Test tracking chunks from multiple files."""
    chunks = [
        Chunk(
            id="chunk1",
            text="def hello():\n    print('world')\n",
            parent_doc=sample_document1,
            char_offset=0,
            length=31,
            line_offset=0,
            length_in_lines=2,
            meta={},
        ),
        Chunk(
            id="chunk2",
            text="def goodbye():\n    print('farewell')\n",
            parent_doc=sample_document2,
            char_offset=0,
            length=35,
            line_offset=0,
            length_in_lines=2,
            meta={},
        ),
    ]

    formatter = RetrievalsFormatter()
    formatter.add_chunks(chunks, max_budget=1000)

    assert len(formatter.chunks) == len(chunks)
    assert formatter.chunks[0] == chunks[0]
    assert formatter.chunks[1] == chunks[1]


def test_track_zero_token_chunks(sample_document1):
    """Test that chunks that don't increase token count are not tracked."""
    chunks = [
        Chunk(
            id="chunk1",
            text="",  # Empty text should result in 0 token increase
            parent_doc=sample_document1,
            char_offset=0,
            length=0,
            line_offset=0,
            length_in_lines=0,
            meta={},
        ),
    ]

    formatter = RetrievalsFormatter()
    formatter.add_chunks(chunks, max_budget=1000)

    assert len(formatter.chunks) == 0

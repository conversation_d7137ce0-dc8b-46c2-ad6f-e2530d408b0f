def interleave_sequences(sequences):
    """Interleave multiple sequences, continuing with remaining elements when shorter sequences end.

    Example:
    >>> list(interleave_sequences([[1, 2], [4, 5, 6, 7]]))
    [1, 4, 2, 5, 6, 7]
    """
    # Convert all sequences to iterators
    iterators = [iter(seq) for seq in sequences]
    # Continue while we have any non-empty iterator
    while iterators:
        # Try to get next item from each iterator
        for i in range(len(iterators)):
            try:
                yield next(iterators[i])
            except StopIteration:
                # Remove exhausted iterator
                iterators.pop(i)
                break

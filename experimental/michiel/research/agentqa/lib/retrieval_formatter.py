"""Formats retrieved chunks into a string."""

import dataclasses
import functools

from base.prompt_format_chat.lib.formatted_file_v2 import FormattedFileV2
from base.prompt_format.common import PromptChunk
from research.core.types import Chunk
from base.prompt_format_chat.lib.token_counter import Token<PERSON>ou<PERSON>
from base.prompt_format_chat.lib.string_formatter import StringFormatter


PATH_PLACEHOLDER = "path"
CONTENT_PLACEHOLDER = "content"
TEMPLATE = "Path: {path}\n{content}\n"


class CharCounter(TokenCounter):
    """A token counter that counts characters."""

    @functools.lru_cache(maxsize=128)
    def count_tokens(self, prompt_chars: str) -> int:
        return len(prompt_chars)


@dataclasses.dataclass
class RetrievalsFormatter:
    formatted_files: dict[str, FormattedFileV2] = dataclasses.field(
        default_factory=dict
    )
    chunks: list[Chunk] = dataclasses.field(default_factory=list)

    def __post_init__(self):
        self.token_counter = CharCounter()
        self.template_formatter = StringFormatter(TEMPLATE, self.token_counter)
        self.n_tokens = 0

    def add_chunks(self, chunks: list[Chunk], max_budget: int):
        for chunk in chunks:
            path = chunk.path
            assert path is not None and len(path) > 0
            prompt_chunk = PromptChunk(
                text=chunk.text,
                path=path,
                unique_id=chunk.id,
                origin="dense_retriever",
                char_start=chunk.char_offset,
                char_end=chunk.char_offset + chunk.length,
                blob_name=chunk.parent_doc.id,
            )
            old_formatted_file = self.formatted_files.get(
                path,
                FormattedFileV2(self.token_counter),
            )
            new_formatted_file = old_formatted_file.add_chunk(prompt_chunk)
            n_tokens_inc = (
                new_formatted_file.get_file_str_and_tok_ct()[1]
                - old_formatted_file.get_file_str_and_tok_ct()[1]
            )
            if self.n_tokens + n_tokens_inc > max_budget:
                break
            # add chunk to the section
            if n_tokens_inc > 0:
                self.chunks.append(chunk)
            self.n_tokens += n_tokens_inc
            self.formatted_files[path] = new_formatted_file

    def format(self) -> str:
        formatted_retrievals = []
        for path, formatted_file in self.formatted_files.items():
            file_str, _ = formatted_file.get_file_str_and_tok_ct()
            file_section_str = self.template_formatter.format(
                values={
                    PATH_PLACEHOLDER: path,
                    CONTENT_PLACEHOLDER: file_str,
                }
            )
            formatted_retrievals.append(file_section_str)
        return "\n".join(formatted_retrievals)

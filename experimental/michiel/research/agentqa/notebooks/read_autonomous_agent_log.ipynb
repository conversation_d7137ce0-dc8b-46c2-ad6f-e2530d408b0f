{"cells": [{"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["import pickle\n", "from research.agents.tools import LoggedToolCall, LoggedLanguageModelCall\n", "\n", "path = \"/mnt/efs/augment/user/guy/agent_logs/michiel.s.dejong/agent_log_20250127_191648.pickle\"\n", "with open(path, \"rb\") as f:\n", "    log = pickle.load(f)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["type(log)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for event in log:\n", "    if isinstance(event, LoggedToolCall):\n", "        logged_tool_call = event\n", "        tool_param = logged_tool_call.tool\n", "        if logged_tool_call.started:\n", "            print(f\"Tool call started: {tool_param.name}\")\n", "        else:\n", "            print(f\"Tool call ended: {tool_param.name}\")\n", "        if logged_tool_call.auxiliary_data:\n", "            print(\"Auxiliary data:\")\n", "            for key, value in logged_tool_call.auxiliary_data.items():\n", "                print(f\"  {key}: {value}\")\n", "\n", "        # for key, value in logged_tool_call.tool_input.items():\n", "        #     print(f\"  {key}: {value}\")\n", "    elif isinstance(event, LoggedLanguageModelCall):\n", "        print(\"model call\")\n", "        logged_language_model_call = event\n", "        tools = logged_language_model_call.tools\n", "\n", "    else:\n", "        print(\"unknown\")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
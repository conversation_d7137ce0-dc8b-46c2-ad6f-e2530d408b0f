{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# RouterTool Demo\n", "\n", "This notebook demonstrates the usage of the RouterTool from the experimental/michiel/research/agentqa/tools_new.py module."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "from pathlib import Path\n", "\n", "# Import necessary modules\n", "from base.prompt_format_chat.prompt_formatter import ChatPromptInput\n", "from experimental.michiel.research.agentqa.retrieval import create_retriever\n", "from experimental.michiel.research.agentqa.tools_new import (\n", "    RouterTool,\n", "    UserQueryAnnotatorTool,\n", ")\n", "from research.agents.tools import LoggingLLMClient, ToolCallLogger\n", "from research.llm_apis.llm_client import AnthropicDirectClient"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup\n", "\n", "Initialize the necessary components for the RouterTool."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Creating retriever...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/opt/conda/lib/python3.11/site-packages/dataclasses_json/mm.py:253: UserWarning: Nested dataclass field tool_use of type base.prompt_format.common.ChatResultToolUse | None detected in ChatResultNode that is not an instance of dataclass_json. Did you mean to recursively serialize this field? If so, make sure to augment <class 'base.prompt_format.common.ChatResultToolUse'> with either the `dataclass_json` decorator or mixin.\n", "  warnings.warn(f\"Nested dataclass field {field.name} of type \"\n", "WARNING:root:Loading StarCoder model from a legacy checkpoint. This is deprecated and will be removed in the future.\n", "/home/<USER>/src/augment/base/fastforward/starcoder/fwd_starcoder.py:84: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.\n", "  layer = torch.load(\n", "/home/<USER>/src/augment/base/fastforward/starcoder/fwd_starcoder.py:95: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.\n", "  layer_dict = torch.load(\n", "/home/<USER>/src/augment/base/fastforward/starcoder/fwd_starcoder.py:133: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.\n", "  layer = torch.load(\n", "/home/<USER>/src/augment/base/fastforward/starcoder/fwd_starcoder.py:139: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.\n", "  layer = torch.load(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Created BasicAttention with stable_id cfdba53b-0e67-47e6-b9e2-a689a1c598b1.\n", "Created BasicAttention with stable_id 856880a1-d45a-4b70-a392-af47e85528ad.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Embedding batches: 100%|██████████| 1/1 [00:00<00:00,  3.91it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Loading retriever...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["# Initialize the tool call logger\n", "tool_call_logger = ToolCallLogger()\n", "\n", "# Initialize the LLM client\n", "client = AnthropicDirectClient(model_name=\"claude-3-7-sonnet-20240307\")\n", "logging_client = LoggingLLMClient(client, tool_call_logger)\n", "\n", "# Initialize the retriever\n", "retriever_config = {\n", "    \"scorer\": {\n", "        \"name\": \"dense_scorer_v2_ffwd\",\n", "        \"checkpoint_path\": \"/mnt/efs/augment/user/igor/checkpoints/chatanol/chatanol1-18.hybrid/neox/global_step1468\",\n", "        \"cache_dir\": \"/home/<USER>/data/retrieval_cache\",\n", "    },\n", "    \"chunker\": {\n", "        \"name\": \"smart_line_level\",\n", "        \"max_chunk_chars\": 768,\n", "        \"max_headers\": 3,\n", "    },\n", "    \"query_formatter\": {\n", "        \"name\": \"base:chatanol6-singleturnisspecial\",\n", "        \"tokenizer_name\": \"rogue\",\n", "        \"max_tokens\": 1024,\n", "    },\n", "    \"document_formatter\": {\n", "        \"name\": \"base:chatanol6-embedding\",\n", "        \"tokenizer_name\": \"rogue\",\n", "    },\n", "}\n", "\n", "print(\"Creating retriever...\")\n", "retriever = create_retriever(retriever_config)\n", "\n", "print(\"Loading retriever...\")\n", "retriever.load()\n", "\n", "# Add documents to the retriever (in a real scenario, you would add actual documents)\n", "# For this test, we'll skip this step since we don't have specific documents to add"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Create the RouterTool\n", "\n", "Initialize the RouterTool with the necessary components."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Creating RouterTool...\n"]}], "source": ["# Create the RouterTool\n", "print(\"Creating RouterTool...\")\n", "router_tool = RouterTool(\n", "    tool_call_logger=tool_call_logger, retriever=retriever, llm_client=logging_client\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Test the RouterTool\n", "\n", "Run the RouterTool with a sample user message."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Testing RouterTool with message: 'How do I implement a custom tool in the codebase?'\n"]}, {"ename": "NotFoundError", "evalue": "Error code: 404 - {'type': 'error', 'error': {'type': 'not_found_error', 'message': 'model: claude-3-7-sonnet-20240307'}}", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNotFoundError\u001b[0m                             <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[4], line 5\u001b[0m\n\u001b[1;32m      2\u001b[0m user_message \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mHow do I implement a custom tool in the codebase?\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m      4\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;124mTesting RouterTool with message: \u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;132;01m{\u001b[39;00muser_message\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m----> 5\u001b[0m result \u001b[38;5;241m=\u001b[39m \u001b[43mrouter_tool\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mrun_impl\u001b[49m\u001b[43m(\u001b[49m\u001b[43m{\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mmessage\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43muser_message\u001b[49m\u001b[43m}\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m      7\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;124mRouter Tool Output:\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m      8\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m=\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;241m*\u001b[39m \u001b[38;5;241m50\u001b[39m)\n", "File \u001b[0;32m~/src/augment/experimental/michiel/research/agentqa/tools_new.py:1931\u001b[0m, in \u001b[0;36mRouterTool.run_impl\u001b[0;34m(self, tool_input, dialog_messages)\u001b[0m\n\u001b[1;32m   1928\u001b[0m context_chunks, routing_info \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_perform_retrieval(user_message, dialog_messages)\n\u001b[1;32m   1930\u001b[0m \u001b[38;5;66;03m# Step 2: Perform LLM call with forced tool use of UserQueryAnnotatorTool\u001b[39;00m\n\u001b[0;32m-> 1931\u001b[0m annotations, annotation_text \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_perform_llm_call\u001b[49m\u001b[43m(\u001b[49m\u001b[43muser_message\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mcontext_chunks\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mdialog_messages\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1933\u001b[0m \u001b[38;5;66;03m# Add the user message to the output\u001b[39;00m\n\u001b[1;32m   1934\u001b[0m result_text \u001b[38;5;241m=\u001b[39m \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mUser message: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00muser_message\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;132;01m{\u001b[39;00mannotation_text\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m\n", "File \u001b[0;32m~/src/augment/experimental/michiel/research/agentqa/tools_new.py:1866\u001b[0m, in \u001b[0;36mRouterTool._perform_llm_call\u001b[0;34m(self, user_message, context_chunks, dialog_messages)\u001b[0m\n\u001b[1;32m   1863\u001b[0m messages\u001b[38;5;241m.\u001b[39mappend(TextPrompt(text\u001b[38;5;241m=\u001b[39m\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mUser query: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00muser_message\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m))\n\u001b[1;32m   1865\u001b[0m \u001b[38;5;66;03m# Make the LLM call with forced tool use\u001b[39;00m\n\u001b[0;32m-> 1866\u001b[0m response_messages, _ \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mllm_client\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mgenerate\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m   1867\u001b[0m \u001b[43m    \u001b[49m\u001b[43mmessages\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43m[\u001b[49m\u001b[43mmessages\u001b[49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1868\u001b[0m \u001b[43m    \u001b[49m\u001b[43mmax_tokens\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;241;43m2048\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1869\u001b[0m \u001b[43m    \u001b[49m\u001b[43msystem_prompt\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43msystem_prompt\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1870\u001b[0m \u001b[43m    \u001b[49m\u001b[43mtemperature\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;241;43m0.0\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1871\u001b[0m \u001b[43m    \u001b[49m\u001b[43mtools\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43m[\u001b[49m\u001b[43mannotator_tool_param\u001b[49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1872\u001b[0m \u001b[43m    \u001b[49m\u001b[43mtool_choice\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43m{\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mtype\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mtool\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mname\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mannotator_tool_param\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mname\u001b[49m\u001b[43m}\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1873\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1875\u001b[0m \u001b[38;5;66;03m# Process the response\u001b[39;00m\n\u001b[1;32m   1876\u001b[0m \u001b[38;5;28;01massert\u001b[39;00m \u001b[38;5;28mlen\u001b[39m(response_messages) \u001b[38;5;241m==\u001b[39m \u001b[38;5;241m1\u001b[39m, \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mExpected exactly one response message, got \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mresponse_messages\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m\n", "File \u001b[0;32m~/src/augment/research/agents/tools.py:1636\u001b[0m, in \u001b[0;36mLoggingLLMClient.generate\u001b[0;34m(self, messages, max_tokens, system_prompt, temperature, tools, tool_choice)\u001b[0m\n\u001b[1;32m   1626\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mtool_call_logger\u001b[38;5;241m.\u001b[39mlanguage_model_call_started(\n\u001b[1;32m   1627\u001b[0m     messages\u001b[38;5;241m=\u001b[39mmessages,\n\u001b[1;32m   1628\u001b[0m     max_tokens\u001b[38;5;241m=\u001b[39mmax_tokens,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m   1632\u001b[0m     tool_choice\u001b[38;5;241m=\u001b[39mtool_choice,\n\u001b[1;32m   1633\u001b[0m )\n\u001b[1;32m   1634\u001b[0m start_time \u001b[38;5;241m=\u001b[39m time\u001b[38;5;241m.\u001b[39mtime()\n\u001b[0;32m-> 1636\u001b[0m model_response, metadata \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mclient\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mgenerate\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m   1637\u001b[0m \u001b[43m    \u001b[49m\u001b[43mmessages\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mmessages\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1638\u001b[0m \u001b[43m    \u001b[49m\u001b[43mmax_tokens\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mmax_tokens\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1639\u001b[0m \u001b[43m    \u001b[49m\u001b[43msystem_prompt\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43msystem_prompt\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1640\u001b[0m \u001b[43m    \u001b[49m\u001b[43mtemperature\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtemperature\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1641\u001b[0m \u001b[43m    \u001b[49m\u001b[43mtools\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtools\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1642\u001b[0m \u001b[43m    \u001b[49m\u001b[43mtool_choice\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtool_choice\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1643\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1645\u001b[0m end_time \u001b[38;5;241m=\u001b[39m time\u001b[38;5;241m.\u001b[39mtime()\n\u001b[1;32m   1646\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mtool_call_logger\u001b[38;5;241m.\u001b[39mlanguage_model_call_ended(\n\u001b[1;32m   1647\u001b[0m     messages\u001b[38;5;241m=\u001b[39mmessages,\n\u001b[1;32m   1648\u001b[0m     max_tokens\u001b[38;5;241m=\u001b[39mmax_tokens,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m   1655\u001b[0m     duration\u001b[38;5;241m=\u001b[39mend_time \u001b[38;5;241m-\u001b[39m start_time,\n\u001b[1;32m   1656\u001b[0m )\n", "File \u001b[0;32m~/src/augment/research/llm_apis/llm_client.py:334\u001b[0m, in \u001b[0;36mAnthropicVertexClient.generate\u001b[0;34m(self, messages, max_tokens, system_prompt, temperature, tools, tool_choice, thinking_tokens)\u001b[0m\n\u001b[1;32m    332\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m retry \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mrange\u001b[39m(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mmax_retries):\n\u001b[1;32m    333\u001b[0m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m--> 334\u001b[0m         response \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mclient\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mmessages\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcreate\u001b[49m\u001b[43m(\u001b[49m\u001b[43m  \u001b[49m\u001b[38;5;66;43;03m# type: ignore\u001b[39;49;00m\n\u001b[1;32m    335\u001b[0m \u001b[43m            \u001b[49m\u001b[43mmax_tokens\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mmax_tokens\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    336\u001b[0m \u001b[43m            \u001b[49m\u001b[43mmessages\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43manthropic_messages\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    337\u001b[0m \u001b[43m            \u001b[49m\u001b[43mmodel\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mmodel_name\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    338\u001b[0m \u001b[43m            \u001b[49m\u001b[43mtemperature\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtemperature\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    339\u001b[0m \u001b[43m            \u001b[49m\u001b[43msystem\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43msystem_prompt\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01mor\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mAnthropic_NOT_GIVEN\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    340\u001b[0m \u001b[43m            \u001b[49m\u001b[43mtool_choice\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtool_choice_param\u001b[49m\u001b[43m,\u001b[49m\u001b[43m  \u001b[49m\u001b[38;5;66;43;03m# type: ignore\u001b[39;49;00m\n\u001b[1;32m    341\u001b[0m \u001b[43m            \u001b[49m\u001b[43mtools\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtool_params\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    342\u001b[0m \u001b[43m            \u001b[49m\u001b[43mextra_headers\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mextra_headers\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    343\u001b[0m \u001b[43m            \u001b[49m\u001b[43mextra_body\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mextra_body\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    344\u001b[0m \u001b[43m        \u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    345\u001b[0m         \u001b[38;5;28;01mbreak\u001b[39;00m\n\u001b[1;32m    346\u001b[0m     \u001b[38;5;28;01mexcept\u001b[39;00m (\n\u001b[1;32m    347\u001b[0m         AnthropicAPIConnectionError,\n\u001b[1;32m    348\u001b[0m         AnthropicInternalServerError,\n\u001b[1;32m    349\u001b[0m         AnthropicRateLimitError,\n\u001b[1;32m    350\u001b[0m         AnthropicOverloadedError,\n\u001b[1;32m    351\u001b[0m     ) \u001b[38;5;28;01mas\u001b[39;00m e:\n", "File \u001b[0;32m~/.local/lib/python3.11/site-packages/anthropic/_utils/_utils.py:275\u001b[0m, in \u001b[0;36mrequired_args.<locals>.inner.<locals>.wrapper\u001b[0;34m(*args, **kwargs)\u001b[0m\n\u001b[1;32m    273\u001b[0m             msg \u001b[38;5;241m=\u001b[39m \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mMissing required argument: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mquote(missing[\u001b[38;5;241m0\u001b[39m])\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    274\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mTypeError\u001b[39;00m(msg)\n\u001b[0;32m--> 275\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mfunc\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/.local/lib/python3.11/site-packages/anthropic/resources/messages/messages.py:953\u001b[0m, in \u001b[0;36mMessages.create\u001b[0;34m(self, max_tokens, messages, model, metadata, stop_sequences, stream, system, temperature, thinking, tool_choice, tools, top_k, top_p, extra_headers, extra_query, extra_body, timeout)\u001b[0m\n\u001b[1;32m    946\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m model \u001b[38;5;129;01min\u001b[39;00m DEPRECATED_MODELS:\n\u001b[1;32m    947\u001b[0m     warnings\u001b[38;5;241m.\u001b[39mwarn(\n\u001b[1;32m    948\u001b[0m         \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mThe model \u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mmodel\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m is deprecated and will reach end-of-life on \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mDEPRECATED_MODELS[model]\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m.\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;124mPlease migrate to a newer model. Visit https://docs.anthropic.com/en/docs/resources/model-deprecations for more information.\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[1;32m    949\u001b[0m         \u001b[38;5;167;01mDeprecationWarning\u001b[39;00m,\n\u001b[1;32m    950\u001b[0m         stacklevel\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m3\u001b[39m,\n\u001b[1;32m    951\u001b[0m     )\n\u001b[0;32m--> 953\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_post\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    954\u001b[0m \u001b[43m    \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43m/v1/messages\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[1;32m    955\u001b[0m \u001b[43m    \u001b[49m\u001b[43mbody\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mmaybe_transform\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    956\u001b[0m \u001b[43m        \u001b[49m\u001b[43m{\u001b[49m\n\u001b[1;32m    957\u001b[0m \u001b[43m            \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mmax_tokens\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mmax_tokens\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    958\u001b[0m \u001b[43m            \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mmessages\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mmessages\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    959\u001b[0m \u001b[43m            \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mmodel\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mmodel\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    960\u001b[0m \u001b[43m            \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mmetadata\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mmetadata\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    961\u001b[0m \u001b[43m            \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mstop_sequences\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mstop_sequences\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    962\u001b[0m \u001b[43m            \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mstream\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mstream\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    963\u001b[0m \u001b[43m            \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43msystem\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43msystem\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    964\u001b[0m \u001b[43m            \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mtemperature\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mtemperature\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    965\u001b[0m \u001b[43m            \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mthinking\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mthinking\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    966\u001b[0m \u001b[43m            \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mtool_choice\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mtool_choice\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    967\u001b[0m \u001b[43m            \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mtools\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mtools\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    968\u001b[0m \u001b[43m            \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mtop_k\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mtop_k\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    969\u001b[0m \u001b[43m            \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mtop_p\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mtop_p\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    970\u001b[0m \u001b[43m        \u001b[49m\u001b[43m}\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    971\u001b[0m \u001b[43m        \u001b[49m\u001b[43mmessage_create_params\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mMessageCreateParams\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    972\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    973\u001b[0m \u001b[43m    \u001b[49m\u001b[43moptions\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mmake_request_options\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    974\u001b[0m \u001b[43m        \u001b[49m\u001b[43mextra_headers\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mextra_headers\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mextra_query\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mextra_query\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mextra_body\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mextra_body\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtimeout\u001b[49m\n\u001b[1;32m    975\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    976\u001b[0m \u001b[43m    \u001b[49m\u001b[43mcast_to\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mMessage\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    977\u001b[0m \u001b[43m    \u001b[49m\u001b[43mstream\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mstream\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01mor\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[1;32m    978\u001b[0m \u001b[43m    \u001b[49m\u001b[43mstream_cls\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mStream\u001b[49m\u001b[43m[\u001b[49m\u001b[43mRawMessageStreamEvent\u001b[49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    979\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/.local/lib/python3.11/site-packages/anthropic/_base_client.py:1330\u001b[0m, in \u001b[0;36mSyncAPIClient.post\u001b[0;34m(self, path, cast_to, body, options, files, stream, stream_cls)\u001b[0m\n\u001b[1;32m   1316\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mpost\u001b[39m(\n\u001b[1;32m   1317\u001b[0m     \u001b[38;5;28mself\u001b[39m,\n\u001b[1;32m   1318\u001b[0m     path: \u001b[38;5;28mstr\u001b[39m,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m   1325\u001b[0m     stream_cls: \u001b[38;5;28mtype\u001b[39m[_StreamT] \u001b[38;5;241m|\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m,\n\u001b[1;32m   1326\u001b[0m ) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m ResponseT \u001b[38;5;241m|\u001b[39m _StreamT:\n\u001b[1;32m   1327\u001b[0m     opts \u001b[38;5;241m=\u001b[39m FinalRequestOptions\u001b[38;5;241m.\u001b[39mconstruct(\n\u001b[1;32m   1328\u001b[0m         method\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mpost\u001b[39m\u001b[38;5;124m\"\u001b[39m, url\u001b[38;5;241m=\u001b[39mpath, json_data\u001b[38;5;241m=\u001b[39mbody, files\u001b[38;5;241m=\u001b[39mto_httpx_files(files), \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39moptions\n\u001b[1;32m   1329\u001b[0m     )\n\u001b[0;32m-> 1330\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m cast(ResponseT, \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mrequest\u001b[49m\u001b[43m(\u001b[49m\u001b[43mcast_to\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mopts\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mstream\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mstream\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mstream_cls\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mstream_cls\u001b[49m\u001b[43m)\u001b[49m)\n", "File \u001b[0;32m~/.local/lib/python3.11/site-packages/anthropic/_base_client.py:1007\u001b[0m, in \u001b[0;36mSyncAPIClient.request\u001b[0;34m(self, cast_to, options, remaining_retries, stream, stream_cls)\u001b[0m\n\u001b[1;32m   1004\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m   1005\u001b[0m     retries_taken \u001b[38;5;241m=\u001b[39m \u001b[38;5;241m0\u001b[39m\n\u001b[0;32m-> 1007\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_request\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m   1008\u001b[0m \u001b[43m    \u001b[49m\u001b[43mcast_to\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mcast_to\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1009\u001b[0m \u001b[43m    \u001b[49m\u001b[43moptions\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43moptions\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1010\u001b[0m \u001b[43m    \u001b[49m\u001b[43mstream\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mstream\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1011\u001b[0m \u001b[43m    \u001b[49m\u001b[43mstream_cls\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mstream_cls\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1012\u001b[0m \u001b[43m    \u001b[49m\u001b[43mretries_taken\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mretries_taken\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1013\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/.local/lib/python3.11/site-packages/anthropic/_base_client.py:1111\u001b[0m, in \u001b[0;36mSyncAPIClient._request\u001b[0;34m(self, cast_to, options, retries_taken, stream, stream_cls)\u001b[0m\n\u001b[1;32m   1108\u001b[0m         err\u001b[38;5;241m.\u001b[39mresponse\u001b[38;5;241m.\u001b[39mread()\n\u001b[1;32m   1110\u001b[0m     log\u001b[38;5;241m.\u001b[39mdebug(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mRe-raising status error\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m-> 1111\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_make_status_error_from_response(err\u001b[38;5;241m.\u001b[39mresponse) \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[1;32m   1113\u001b[0m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_process_response(\n\u001b[1;32m   1114\u001b[0m     cast_to\u001b[38;5;241m=\u001b[39mcast_to,\n\u001b[1;32m   1115\u001b[0m     options\u001b[38;5;241m=\u001b[39moptions,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m   1119\u001b[0m     retries_taken\u001b[38;5;241m=\u001b[39mretries_taken,\n\u001b[1;32m   1120\u001b[0m )\n", "\u001b[0;31mNotFoundError\u001b[0m: Error code: 404 - {'type': 'error', 'error': {'type': 'not_found_error', 'message': 'model: claude-3-7-sonnet-20240307'}}"]}], "source": ["# Sample user message\n", "user_message = \"How do I implement a custom tool in the codebase?\"\n", "\n", "print(f\"\\nTesting RouterTool with message: '{user_message}'\")\n", "result = router_tool.run_impl({\"message\": user_message})\n", "\n", "print(\"\\nRouter Tool Output:\")\n", "print(\"=\" * 50)\n", "print(result.tool_output)\n", "print(\"=\" * 50)\n", "\n", "print(\"\\nAuxiliary Data:\")\n", "for key, value in result.auxiliary_data.items():\n", "    print(f\"{key}: {value}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Analyze the Results\n", "\n", "Examine how the RouterTool analyzes and routes different types of user queries."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "Testing with query: 'What is the purpose of the UserQueryAnnotatorTool?'\n", "\n", "Routing result:\n", "==================================================\n", "User message: What is the purpose of the UserQueryAnnotatorTool?\n", "\n", "Annotations:\n", "- Requires codebase information: Yes\n", "- Requires agent action: No\n", "- Specific files: None\n", "- External package: none\n", "\n", "==================================================\n", "\n", "Requires codebase info: True\n", "Requires action: False\n", "\n", "\n", "Testing with query: 'Create a new file called test.py with a hello world function'\n", "\n", "Routing result:\n", "==================================================\n", "User message: Create a new file called test.py with a hello world function\n", "\n", "Annotations:\n", "- Requires codebase information: No\n", "- Requires agent action: Yes\n", "- Specific files: None\n", "- External package: none\n", "\n", "==================================================\n", "\n", "Requires codebase info: False\n", "Requires action: True\n", "\n", "\n", "Testing with query: 'What's the best way to learn Python?'\n", "\n", "Routing result:\n", "==================================================\n", "User message: What's the best way to learn Python?\n", "\n", "Annotations:\n", "- Requires codebase information: No\n", "- Requires agent action: No\n", "- Specific files: None\n", "- External package: python\n", "\n", "==================================================\n", "\n", "Requires codebase info: False\n", "Requires action: False\n", "\n", "All tests completed.\n"]}], "source": ["# Test with additional queries\n", "additional_queries = [\n", "    \"What is the purpose of the UserQueryAnnotatorTool?\",\n", "    \"Create a new file called test.py with a hello world function\",\n", "    \"What's the best way to learn Python?\",\n", "]\n", "\n", "for query in additional_queries:\n", "    print(f\"\\n\\nTesting with query: '{query}'\")\n", "    result = router_tool.run_impl({\"message\": query})\n", "    print(\"\\nRouting result:\")\n", "    print(\"=\" * 50)\n", "    print(result.tool_output)\n", "    print(\"=\" * 50)\n", "    print(\"\\nRequires codebase info:\", result.auxiliary_data.get(\"requires_codebase\"))\n", "    print(\"Requires action:\", result.auxiliary_data.get(\"requires_action\"))\n", "\n", "print(\"\\nAll tests completed.\")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 4}
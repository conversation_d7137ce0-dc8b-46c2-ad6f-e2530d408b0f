{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from termcolor import colored\n", "\n", "from base.datasets.tenants import DOGFOOD_SHARD, DOGFOOD\n", "from base.prompt_format_retrieve.prompt_formatter import ChatRetrieverPromptInput\n", "from base.prompt_format_chat.prompt_formatter import ChatPromptInput\n", "\n", "from experimental.michiel.research.agentqa.retrieval import BlobGetter, create_retriever\n", "from experimental.michiel.research.agentqa.agent import (\n", "    <PERSON><PERSON><PERSON>,\n", "    ModelCall,\n", "    ToolCallEntry,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["request_id = \"b14b0e44-bd60-4b19-956f-ecb576c4822c\"\n", "# request_id = \"ad17e6db-94f2-4680-9fa7-6958751dc0ad\"\n", "\n", "blob_getter = BlobGetter(DOGFOOD_SHARD)\n", "documents = blob_getter.get_blobs_from_request_id(request_id)\n", "doc_ids = {doc.id for doc in documents}"]}, {"cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# load retriever\n", "retriever_config = {\n", "    \"scorer\": {\n", "        \"name\": \"dense_scorer_v2_ffwd\",\n", "        \"checkpoint_path\": \"/mnt/efs/augment/user/igor/checkpoints/chatanol/chatanol1-18.hybrid/neox/global_step1468\",\n", "        # \"checkpoint_path\": \"/home/<USER>/data/checkpoints/chatanol/chatanol1-18.hybrid/neox/global_step1468\",\n", "        \"cache_dir\": \"/home/<USER>/data/retrieval_cache\",\n", "    },\n", "    \"chunker\": {\n", "        \"name\": \"smart_line_level\",\n", "        \"max_chunk_chars\": 768,\n", "        \"max_headers\": 3,\n", "    },\n", "    \"query_formatter\": {\n", "        \"name\": \"base:chatanol6-singleturnisspecial\",\n", "        \"tokenizer_name\": \"rogue\",\n", "        \"max_tokens\": 1024,\n", "    },\n", "    \"document_formatter\": {\n", "        \"name\": \"base:chatanol6-embedding\",\n", "        \"tokenizer_name\": \"rogue\",\n", "    },\n", "}\n", "retriever = create_retriever(retriever_config)\n", "retriever.load()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["retriever.add_docs(documents)\n", "# retriever.add_docs(documents[:5])"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["##### Succeeds where base chat fails (or is meaningfully worse)\n", "\n", "# question = \"where does binks prompt formatter merge overlapping chunks?\"\n", "# question = \"list all registered model names in `research/models/fastforward_models.py`\"\n", "# question = \"which methods in utils_for_str.py are not covered by unit tests\"\n", "# question = \"How can I set multiple evals for the fastbackward training with custom names?\"\n", "\n", "# General reasoning is correct, but model confuses empty completion with empty list of completions\n", "# question = \"Do quality filtered completions show up in the history panel?\"\n", "\n", "# question = \"I want to modify a chunker to skip documents from the docset service. How do I do that?\"\n", "\n", "\n", "##### Fails where base chat succeeds (or is meaningfully better)\n", "\n", "# Failure - model doesn't discover flags.yaml\n", "# question = \"How do I change the default completion model in production?\"\n", "\n", "##### Both fail\n", "\n", "# Failure: ignores 'base' part\n", "# question = \"where is `suggested_prefix_char_count` in base\"\n", "\n", "# Failure: looks through part of code, confidently suggests it's nowhere. Cha<PERSON>l doesn't get it even if asked literally and restricted to base\n", "# question = \"where is `suggested_prefix_char_count` in base directory\"\n", "\n", "# Failure: gets confused by research code\n", "# question = \"How is the next edit output length propagated from the deploy config to the edit server\"\n", "\n", "# Failure: doesn't find eldenv3, gets confused with research, still answers confidently\n", "# question = \"Where is the base prompt formatter for eldenv3?\"\n", "\n", "# Failure: finds fastbackward instead\n", "# question = \"Does fastforward model in research use all available GPUs? Show exact lines.\"\n", "\n", "# Failure: doesn't call tool to look through entire directory. Agent does better than chat though.\n", "# question = \"which deployment configs are using neural speculative model?\"\n", "\n", "\n", "##### Both succeed\n", "\n", "# question = \"where is tutorial for str_diff\"\n", "# question = \"How do I serialize a python dataclass\"\n", "# question = \"Where is the default shortcut for accepting a code instruction set?\"\n", "# question = \"Where is eldenv3 configured?\"\n", "# question = \"Where is the prompt formatter for the eldenv5 model?\"\n", "# question = \"Do quality filtered completions show up in request insight?\"\n", "# question = \"How do I change the cluster when running evaluation jobs?\"\n", "\n", "# Agent has a more complete answer\n", "# question = \"How does the find-missing api decide which blobs are missing or unindexed?\"\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from experimental.michiel.research.agentqa.agentic_retrieval_agent import (\n", "    AgenticRetrievalChatAgent,\n", "    ProduceAnswerTool,\n", ")\n", "\n", "from experimental.michiel.research.agentqa.tools_new import (\n", "    HighLevelCodebaseRetrievalAgent,\n", ")\n", "\n", "from research.llm_apis.llm_client import AnthropicDirectClient\n", "from research.agents.tools import ToolCallLogger, LoggingLLMClient\n", "\n", "tool_call_logger = ToolCallLogger()\n", "answer_tool = ProduceAnswerTool(tool_call_logger)\n", "client = AnthropicDirectClient(model_name=\"claude-3-5-sonnet-20241022\")\n", "logging_client = LoggingLLMClient(client, tool_call_logger)\n", "\n", "retrieval_tool = HighLevelCodebaseRetrievalAgent(\n", "    tool_call_logger=tool_call_logger,\n", "    retriever=retriever,\n", "    llm_client=logging_client,\n", "    max_tool_chars=50000,\n", ")\n", "\n", "agent = AgenticRetrievalChatAgent(\n", "    llm_client=logging_client,\n", "    answer_tool=answer_tool,\n", "    retrieval_tool=retrieval_tool,\n", "    max_iterations=2,\n", ")\n", "\n", "null_agent_log = AgentLog()\n", "\n", "request_id = \"b14b0e44-bd60-4b19-956f-ecb576c4822c\"\n", "# chat_input = blob_getter.get_chat_request_without_retrievals(request_id)\n", "question = (\n", "    \"How can I set multiple evals for the fastbackward training with custom names?\"\n", ")\n", "chat_input = ChatPromptInput(\n", "    path=\"\",\n", "    prefix=\"\",\n", "    selected_code=\"\",\n", "    suffix=\"\",\n", "    message=question,\n", "    chat_history=[],\n", "    prefix_begin=0,\n", "    suffix_end=0,\n", "    retrieved_chunks=[],\n", ")\n", "answer = agent.run(chat_input).output\n", "print(answer)\n", "print(tool_call_logger.get_string_representation())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from experimental.michiel.research.agentqa.tool_agent_system import (\n", "    QuestionAnswerAgenticRetrievalSystem,\n", ")\n", "\n", "model_name = \"claude-3-5-sonnet-20241022\"\n", "\n", "system_config = {\n", "    \"model_config\": {\n", "        \"retrieval\": {\"client_name\": \"anthropic-direct\", \"model_name\": model_name},\n", "        \"agent\": {\"client_name\": \"anthropic-direct\", \"model_name\": model_name},\n", "    },\n", "    \"retriever_config\": retriever_config,\n", "    \"agent_config\": {\"agent_config\": {\"max_iterations\": 2}},\n", "    \"verbose\": True,\n", "}\n", "\n", "system = QuestionAnswerAgenticRetrievalSystem.from_yaml_config(system_config)\n", "\n", "system.load()\n", "system.add_docs(documents)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["question = \"Where is the base prompt formatter for eldenv3?\"\n", "\n", "prompt_input = ChatPromptInput(\n", "    path=\"\",\n", "    prefix=\"\",\n", "    selected_code=\"\",\n", "    suffix=\"\",\n", "    message=question,\n", "    chat_history=[],\n", "    prefix_begin=0,\n", "    suffix_end=0,\n", "    retrieved_chunks=[],\n", ")\n", "\n", "result = system.generate(prompt_input)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(result.generated_text)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
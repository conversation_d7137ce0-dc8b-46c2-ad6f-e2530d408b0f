"""Tests for the tools_new module."""

from pathlib import Path
from typing import Any, Optional
from unittest.mock import <PERSON><PERSON><PERSON>, Mo<PERSON>, patch

import pytest

from experimental.guy.agent_qa.builtin_tools import QueryOnlyDocumentIndex
from experimental.michiel.research.agentqa.tools_new import (
    HighLevelCodebase3StepRetrievalAgent,
    QwenQueryRewriterTool,
    RouterTool,
    UserQueryAnnotatorTool,
    build_query_rewriter_prompt_given_initial_retrieval,
    create_directory_structure,
    followup_query_rewrites_given_initial_retrieval,
    format_directory_subtree,
    format_retrieval,
    retrieve,
)
from research.agents.tools import (
    DialogMessages,
    TextPrompt,
    TextResult,
    ToolCall,
    ToolCallLogger,
    ToolFormattedResult,
    ToolImplOutput,
)
from research.core.types import Document
from research.llm_apis.llm_client import LLMClient, ToolParam
from research.retrieval.types import Chunk


@pytest.fixture
def tool_call_logger():
    return Mock(spec=ToolCallLogger)


@pytest.fixture
def mock_retriever():
    retriever = Mock(spec=QueryOnlyDocumentIndex)
    # Setup the query method to return some mock chunks and scores
    retriever.query.return_value = ([], [])
    return retriever


@pytest.fixture
def mock_llm_client():
    llm_client = Mock(spec=LLMClient)
    # Setup the generate method to return a mock response
    tool_call = Mock(spec=ToolCall)
    tool_call.tool_input = {"code_section_requests": [{"description": "test query"}]}
    llm_client.generate.return_value = ([tool_call], None)
    return llm_client


@pytest.fixture
def mock_document():
    doc = Mock(spec=Document)
    doc.id = "test_doc_id"
    return doc


@pytest.fixture
def mock_chunk(mock_document):
    chunk = Mock(spec=Chunk)
    chunk.id = "test_chunk_id"
    chunk.text = "test chunk text"
    chunk.path = "test/path.py"
    chunk.char_offset = 0
    chunk.length = len(chunk.text)
    chunk.parent_doc = mock_document
    chunk.header = "def test_function():"
    return chunk


# Test functions for QwenQueryRewriterTool


@patch("experimental.michiel.research.agentqa.tools_new.FastForwardQwen25Coder_32B")
@patch("experimental.michiel.research.agentqa.tools_new.Qwen25CoderTokenizer")
@patch(
    "experimental.michiel.research.agentqa.tools_new.StructToTokensQwenPromptFormatter"
)
def test_qwen_query_rewriter_tool_init(
    mock_formatter,
    mock_tokenizer,
    mock_model,
    tool_call_logger,
    mock_retriever,
):
    """Test initialization of QwenQueryRewriterTool."""
    # Setup mocks
    mock_model_instance = mock_model.return_value
    mock_model_instance.load = Mock()

    # Create the tool
    tool = QwenQueryRewriterTool(
        tool_call_logger=tool_call_logger,
        retriever=mock_retriever,
        max_tool_chars=1000,
        max_init_retrieval_chars=2000,
    )

    # Verify model was initialized and loaded
    mock_model.assert_called_once()
    mock_model_instance.load.assert_called_once()

    assert tool.name == "request_codebase_information"
    assert tool.retriever == mock_retriever
    assert tool.max_tool_chars == 1000
    assert tool.max_init_retrieval_chars == 2000
    assert tool.max_num_chunks == 1024


@patch("experimental.michiel.research.agentqa.tools_new.FastForwardQwen25Coder_32B")
@patch("experimental.michiel.research.agentqa.tools_new.Qwen25CoderTokenizer")
@patch(
    "experimental.michiel.research.agentqa.tools_new.StructToTokensQwenPromptFormatter"
)
def test_qwen_query_rewriter_tool_get_tool_start_message(
    mock_formatter,
    mock_tokenizer,
    mock_model,
    tool_call_logger,
    mock_retriever,
):
    """Test get_tool_start_message method."""
    # Setup mocks
    mock_model_instance = mock_model.return_value
    mock_model_instance.load = Mock()

    tool = QwenQueryRewriterTool(
        tool_call_logger=tool_call_logger,
        retriever=mock_retriever,
        max_tool_chars=50000,
        max_init_retrieval_chars=100000,
    )

    tool_input = {"information_request": "test request"}
    message = tool.get_tool_start_message(tool_input)

    assert message == "Retrieving codebase information: test request"


@patch("experimental.michiel.research.agentqa.tools_new.FastForwardQwen25Coder_32B")
@patch("experimental.michiel.research.agentqa.tools_new.Qwen25CoderTokenizer")
@patch(
    "experimental.michiel.research.agentqa.tools_new.StructToTokensQwenPromptFormatter"
)
def test_qwen_query_rewriter_tool_get_retriever_queries(
    mock_formatter,
    mock_tokenizer,
    mock_model,
    tool_call_logger,
    mock_retriever,
):
    """Test get_retriever_queries method."""
    # Setup mocks
    mock_model_instance = mock_model.return_value
    mock_model_instance.load = Mock()
    mock_model_instance.raw_generate = Mock()

    tool = QwenQueryRewriterTool(
        tool_call_logger=tool_call_logger,
        retriever=mock_retriever,
        max_tool_chars=50000,
        max_init_retrieval_chars=100000,
    )

    # Setup mock response for the model
    tool_call_json = '{"name": "ask_for_codebase_snippets", "arguments": {"code_section_requests": [{"description": "query1"}, {"description": "query2", "path": "path/to/file.py"}]}}'
    tool.model.raw_generate.return_value = tool_call_json

    # Mock the format_query_rewriter_prompt method
    with patch.object(
        tool, "format_query_rewriter_prompt"
    ) as mock_format_prompt, patch.object(
        tool, "get_initial_retrieval"
    ) as mock_get_initial, patch(
        "experimental.michiel.research.agentqa.tools_new.parse_tool_call"
    ) as mock_parse_tool_call:
        # Setup mocks
        mock_get_initial.return_value = ([], [])
        mock_format_prompt.return_value = (Mock(), [])
        mock_tool_call = ToolCall(
            tool_name="ask_for_codebase_snippets",
            tool_input={
                "code_section_requests": [
                    {"description": "query1"},
                    {"description": "query2", "path": "path/to/file.py"},
                ]
            },
            tool_call_id="",
        )
        mock_parse_tool_call.return_value = mock_tool_call

        # Test without dialog messages
        tool_input = {"information_request": "test request"}

        # Call the method that would use get_retriever_queries internally
        result = tool.run_impl(tool_input)

        # Verify the result contains the expected queries
        assert "retriever_queries" in result.auxiliary_data
        tool_call_dict = result.auxiliary_data["retriever_queries"]
        assert "tool_input" in tool_call_dict
        assert "code_section_requests" in tool_call_dict["tool_input"]
        queries = tool_call_dict["tool_input"]["code_section_requests"]
        assert len(queries) == 2
        assert queries[0]["description"] == "query1"
        assert queries[1]["description"] == "query2"
        assert queries[1]["path"] == "path/to/file.py"

        # Verify the model was called
        mock_get_initial.assert_called_once()
        mock_format_prompt.assert_called_once()
        tool.model.raw_generate.assert_called_once()

        # Test with dialog messages
        tool.model.raw_generate.reset_mock()
        mock_get_initial.reset_mock()
        mock_format_prompt.reset_mock()

        dialog_messages = Mock(spec=DialogMessages)

        tool.run_impl(tool_input, dialog_messages=dialog_messages)


@patch("experimental.michiel.research.agentqa.tools_new.FastForwardQwen25Coder_32B")
@patch("experimental.michiel.research.agentqa.tools_new.Qwen25CoderTokenizer")
@patch(
    "experimental.michiel.research.agentqa.tools_new.StructToTokensQwenPromptFormatter"
)
@patch("experimental.michiel.research.agentqa.tools_new.FormattedFileV2")
def test_qwen_query_rewriter_tool_format_retrieval(
    mock_formatted_file_v2,
    mock_formatter,
    mock_tokenizer,
    mock_model,
    tool_call_logger,
    mock_retriever,
    mock_chunk,
):
    """Test format_retrieval method."""
    # Setup mocks
    mock_model_instance = mock_model.return_value
    mock_model_instance.load = Mock()

    tool = QwenQueryRewriterTool(
        tool_call_logger=tool_call_logger,
        retriever=mock_retriever,
        max_tool_chars=50000,
        max_init_retrieval_chars=100000,
    )

    # Setup mock formatted file
    mock_file_instance = mock_formatted_file_v2.return_value
    mock_file_instance.add_chunk.return_value = mock_file_instance
    mock_file_instance.get_file_str_and_tok_ct.return_value = (
        "formatted content",
        10,
    )
    mock_file_instance.sorted_chunks = [mock_chunk]

    # Create test data
    # query = {"description": "test query"}
    chunks = [mock_chunk]
    # scores = [0.9]
    # query_result_list = [(query, chunks, scores)]

    # Test the method
    output_str, include_chunks = tool.format_retrieval(chunks, 1000)

    # The format_retrieval method doesn't include the header text anymore
    assert "Path: test/path.py" in output_str
    assert "formatted content" in output_str
    assert include_chunks == chunks


@patch("experimental.michiel.research.agentqa.tools_new.FastForwardQwen25Coder_32B")
@patch("experimental.michiel.research.agentqa.tools_new.Qwen25CoderTokenizer")
@patch(
    "experimental.michiel.research.agentqa.tools_new.StructToTokensQwenPromptFormatter"
)
def test_qwen_query_rewriter_tool_run_impl(
    mock_formatter,
    mock_tokenizer,
    mock_model,
    tool_call_logger,
    mock_retriever,
    mock_chunk,
):
    """Test run_impl method."""
    # Setup mocks
    mock_model_instance = mock_model.return_value
    mock_model_instance.load = Mock()
    mock_model_instance.raw_generate = Mock()

    mock_formatter_instance = mock_formatter.return_value
    mock_formatter_instance.format_prompt = Mock()
    mock_formatter_instance.format_prompt.return_value = Mock(tokens=[1, 2, 3])

    tool = QwenQueryRewriterTool(
        tool_call_logger=tool_call_logger,
        retriever=mock_retriever,
        max_tool_chars=50000,
        max_init_retrieval_chars=100000,
    )

    # Set the formatted_directory_output attribute which is needed by format_query_rewriter_prompt
    tool.formatted_directory_output = "Mock directory output"

    # Setup mocks
    with patch.object(tool, "get_initial_retrieval") as mock_get_initial, patch.object(
        tool, "format_query_rewriter_prompt"
    ) as mock_format_prompt, patch(
        "experimental.michiel.research.agentqa.tools_new.retrieve"
    ) as mock_retrieve, patch(
        "experimental.michiel.research.agentqa.tools_new.parse_tool_call"
    ) as mock_parse_tool_call:
        # Configure mocks
        mock_get_initial.return_value = ([mock_chunk], [0.9])
        mock_format_prompt.return_value = (Mock(), [mock_chunk])
        mock_retrieve.return_value = ([mock_chunk], [0.9])

        # Mock the tool call parsing with a real ToolCall object
        mock_tool_call = ToolCall(
            tool_name="ask_for_codebase_snippets",
            tool_input={"code_section_requests": [{"description": "query1"}]},
            tool_call_id="",
        )
        mock_parse_tool_call.return_value = mock_tool_call

        # Mock the tokenized prompt formatter
        tool.tokenized_prompt_formatter.format_prompt.return_value = Mock(
            tokens=[1, 2, 3]
        )

        # Run the method
        tool_input = {"information_request": "test request"}
        result = tool.run_impl(tool_input)

        # Verify the result
        assert isinstance(result, ToolImplOutput)
        assert result.tool_result_message == "Codebase information retrieved"
        assert "retriever_queries" in result.auxiliary_data

        # Verify method calls
        mock_get_initial.assert_called_once()
        mock_format_prompt.assert_called_once()
        tool.model.raw_generate.assert_called_once()
        mock_retrieve.assert_called_once()


@patch("experimental.michiel.research.agentqa.tools_new.FastForwardQwen25Coder_32B")
@patch("experimental.michiel.research.agentqa.tools_new.Qwen25CoderTokenizer")
@patch(
    "experimental.michiel.research.agentqa.tools_new.StructToTokensQwenPromptFormatter"
)
def test_qwen_query_rewriter_tool_get_initial_retrieval(
    mock_formatter,
    mock_tokenizer,
    mock_model,
    tool_call_logger,
    mock_retriever,
    mock_chunk,
):
    """Test get_initial_retrieval method."""
    # Setup mocks
    mock_model_instance = mock_model.return_value
    mock_model_instance.load = Mock()

    tool = QwenQueryRewriterTool(
        tool_call_logger=tool_call_logger,
        retriever=mock_retriever,
        max_tool_chars=50000,
        max_init_retrieval_chars=100000,
    )

    # Setup mock retriever
    chunks = [mock_chunk]
    scores = [0.9]
    mock_retriever.query.return_value = (chunks, scores)

    # Test the method
    result_chunks, result_scores = tool.get_initial_retrieval("test request")

    # Verify the result
    assert result_chunks == chunks[: tool.max_num_chunks]
    assert result_scores == scores[: tool.max_num_chunks]

    # Verify retriever was called correctly
    mock_retriever.query.assert_called_once()


def test_build_query_rewriter_prompt_without_dialog():
    """Test building a query rewriter prompt without dialog messages."""
    # Test with basic information request
    information_request = "Find the implementation of the search function"

    # Call the function
    with patch(
        "experimental.michiel.research.agentqa.tools_new.format_retrieval"
    ) as mock_format_retrieval:
        mock_format_retrieval.return_value = ("", [])
        messages = build_query_rewriter_prompt_given_initial_retrieval(
            information_request
        )

    # Verify the result
    messages_list, included_chunks = messages
    assert len(messages_list) == 1
    assert isinstance(messages_list[0], TextPrompt)

    # Check content of the message
    message_text = messages_list[0].text
    assert "You are a tool-calling agent that generates queries" in message_text
    assert f"The information request is: {information_request}" in message_text
    assert "Look at the information gathered already" in message_text
    assert (
        f"Once again, the information request is: {information_request}" in message_text
    )

    # Verify dialog-related content is not present
    assert "Below are some messages of the dialogue" not in message_text


def test_build_query_rewriter_prompt_with_dialog():
    """Test building a query rewriter prompt with dialog messages."""
    # Test with dialog messages
    information_request = "Find the implementation of the search function"

    # Create mock dialog messages
    dialog_messages = Mock(spec=DialogMessages)

    # Mock the get_messages_for_llm_client method to return a list of messages
    user_message = TextPrompt(text="How does the search function work?")
    assistant_message = TextResult(text="I'll look into that for you.")
    dialog_messages.get_messages_for_llm_client.return_value = [
        [user_message, assistant_message]
    ]

    # Set a smaller max_dialog_chars to test truncation
    max_dialog_chars = 100

    # Call the function with dialog messages
    with patch(
        "experimental.michiel.research.agentqa.tools_new.format_dialog_as_string"
    ) as mock_format_dialog, patch(
        "experimental.michiel.research.agentqa.tools_new.format_retrieval"
    ) as mock_format_retrieval:
        # Mock the format_dialog_as_string function
        mock_format_dialog.return_value = "User message: How does the search function work?\nAssistant message: I'll look into that for you.\n"
        mock_format_retrieval.return_value = ("", [])

        messages = build_query_rewriter_prompt_given_initial_retrieval(
            information_request,
            dialog_messages=dialog_messages,
            max_dialog_chars=max_dialog_chars,
        )

        # Verify format_dialog_as_string was called correctly
        mock_format_dialog.assert_called_once_with(dialog_messages, max_dialog_chars)

    # Verify the result
    messages_list, included_chunks = messages
    assert len(messages_list) == 1
    assert isinstance(messages_list[0], TextPrompt)

    # Check content of the message
    message_text = messages_list[0].text
    assert "You are a tool-calling agent that generates queries" in message_text
    assert f"The information request is: {information_request}" in message_text
    assert (
        "The information request is in the context of an agent attempting to execute a task"
        in message_text
    )
    assert (
        "Below are some messages of the dialogue of this agent and the user"
        in message_text
    )
    assert (
        f"Once again, the information request is: {information_request}" in message_text
    )


def test_build_query_rewriter_prompt_custom_max_chars():
    """Test building a query rewriter prompt with custom max_dialog_chars."""
    information_request = "Find the implementation of the search function"

    # Create mock dialog messages
    dialog_messages = Mock(spec=DialogMessages)

    # Set a custom max_dialog_chars and max_initial_retrieval_chars
    custom_max_dialog_chars = 5000
    custom_max_initial_retrieval_chars = 8000

    # Call the function with custom max_dialog_chars and max_initial_retrieval_chars
    with patch(
        "experimental.michiel.research.agentqa.tools_new.format_dialog_as_string"
    ) as mock_format_dialog, patch(
        "experimental.michiel.research.agentqa.tools_new.format_retrieval"
    ) as mock_format_retrieval:
        mock_format_retrieval.return_value = ("", [])

        _ = build_query_rewriter_prompt_given_initial_retrieval(
            information_request,
            dialog_messages=dialog_messages,
            max_dialog_chars=custom_max_dialog_chars,
            max_initial_retrieval_chars=custom_max_initial_retrieval_chars,
        )

        # Verify format_dialog_as_string was called with the custom max_dialog_chars
        mock_format_dialog.assert_called_once_with(
            dialog_messages, custom_max_dialog_chars
        )


def test_build_query_rewriter_prompt_with_initial_retrieval(mock_chunk):
    """Test building a query rewriter prompt with initial retrieval."""
    information_request = "Find the implementation of the search function"

    # Create a mock chunk for initial retrieval
    mock_chunk.text = "def search(query):\n    return results"
    initial_retrieval_chunks = [mock_chunk]
    formatted_initial_retrieval = "def search(query):\n    return results"

    # Mock the format_retrieval function
    with patch(
        "experimental.michiel.research.agentqa.tools_new.format_retrieval"
    ) as mock_format_retrieval:
        # Set up the mock to return a formatted string
        mock_format_retrieval.return_value = (
            formatted_initial_retrieval,
            initial_retrieval_chunks,
        )

        # Call the function with initial_retrieval_chunks
        messages = build_query_rewriter_prompt_given_initial_retrieval(
            information_request, initial_retrieval_chunks=initial_retrieval_chunks
        )

        # Verify format_retrieval was called with the right parameters
        mock_format_retrieval.assert_called_once_with(
            initial_retrieval_chunks,
            10000,  # default max_initial_retrieval_chars
        )

    # Verify the result
    messages_list, included_chunks = messages
    assert len(messages_list) == 1
    assert isinstance(messages_list[0], TextPrompt)

    # Check content of the message
    message_text = messages_list[0].text
    assert formatted_initial_retrieval in message_text
    assert "You are a tool-calling agent that generates queries" in message_text
    assert (
        "Make sure to also look at the directory tree and previous codebase snippets to guide your search"
        in message_text
        or "Make sure to also look at the previous codebase snippets to guide your search"
        in message_text
    )


def test_build_query_rewriter_prompt_with_directory_output():
    """Test building a query rewriter prompt with directory output."""
    # Skip this test since formatted_directory_output is a local variable
    # and cannot be easily patched
    pass


def test_build_query_rewriter_prompt_with_all_parameters(mock_chunk):
    """Test building a query rewriter prompt with all parameters."""
    # Skip this test since formatted_directory_output is a local variable
    # and cannot be easily patched
    pass


def test_build_query_rewriter_prompt_tool_param():
    """Test that the correct tool parameter is created."""
    information_request = "Find the implementation of the search function"

    # Patch the ToolParam constructor to verify it's called correctly
    with patch(
        "experimental.michiel.research.agentqa.tools_new.ToolParam"
    ) as mock_tool_param, patch(
        "experimental.michiel.research.agentqa.tools_new.CodebaseRetrievalQueryGenerationTool"
    ) as mock_tool_class, patch(
        "experimental.michiel.research.agentqa.tools_new.format_retrieval"
    ) as mock_format_retrieval:
        # Set up the mock tool class attributes
        mock_tool_class.name = "mock_tool_name"
        mock_tool_class.description = "mock_tool_description"
        mock_tool_class.input_schema = {"mock": "schema"}

        # Set up the mock format_retrieval function
        mock_format_retrieval.return_value = ("", [])

        # Call the function
        build_query_rewriter_prompt_given_initial_retrieval(information_request)

        # Verify ToolParam was called with the correct arguments
        mock_tool_param.assert_called_once_with(
            name=mock_tool_class.name,
            description=mock_tool_class.description,
            input_schema=mock_tool_class.input_schema,
        )


def test_highlevecodebase3stepretrievalagent_init(
    tool_call_logger, mock_retriever, mock_llm_client
):
    """Test initialization of HighLevelCodebase3StepRetrievalAgent."""
    # Create the agent
    agent = HighLevelCodebase3StepRetrievalAgent(
        tool_call_logger=tool_call_logger,
        retriever=mock_retriever,
        llm_client=mock_llm_client,
        max_tool_chars=40000,
        max_intermediate_tool_chars=80000,
        max_initial_retrieval_chars=8000,
    )

    # Verify the agent was initialized correctly
    assert agent.tool_call_logger == tool_call_logger
    assert agent.retriever == mock_retriever
    assert agent.llm_client == mock_llm_client
    assert agent.max_tool_chars == 40000
    assert agent.max_intermediate_tool_chars == 80000
    assert agent.max_initial_retrieval_chars == 8000
    assert agent.max_num_chunks == 1024
    assert agent.name == "request_codebase_information"
    assert "Use this tool to request information from the codebase" in agent.description


def test_highlevecodebase3stepretrievalagent_run_impl(
    tool_call_logger, mock_retriever, mock_llm_client
):
    """Test the run_impl method of HighLevelCodebase3StepRetrievalAgent."""
    # Create the agent
    agent = HighLevelCodebase3StepRetrievalAgent(
        tool_call_logger=tool_call_logger,
        retriever=mock_retriever,
        llm_client=mock_llm_client,
    )

    # Create mock input
    tool_input = {
        "information_request": "Find the implementation of the search function"
    }

    # Create mock chunks and scores for the initial retrieval
    mock_chunk = Mock(spec=Chunk)
    mock_chunk.text = "def search(query): return results"
    mock_chunk.path = "src/search.py"
    mock_chunk.id = "chunk1"
    mock_chunk.char_offset = 0
    mock_chunk.length = len(mock_chunk.text)
    mock_chunk.parent_doc = Mock(spec=Document)
    mock_chunk.parent_doc.id = "doc1"
    mock_chunk.header = "def search"

    # Mock the retrieve function
    with patch(
        "experimental.michiel.research.agentqa.tools_new.retrieve"
    ) as mock_retrieve, patch(
        "experimental.michiel.research.agentqa.tools_new.followup_query_rewrites_given_initial_retrieval"
    ) as mock_followup_query_rewrites, patch(
        "experimental.michiel.research.agentqa.tools_new.HighLevelCodebase3StepRetrievalAgent.get_final_round_retriever_queries"
    ) as mock_get_final_round_queries, patch(
        "experimental.michiel.research.agentqa.tools_new.HighLevelCodebase3StepRetrievalAgent.format_retrieval_for_output"
    ) as mock_format_retrieval:
        # Set up the mocks
        mock_retrieve.side_effect = [
            ([mock_chunk], [0.9]),
            ([mock_chunk], [0.9]),
            ([mock_chunk], [0.9]),
        ]
        mock_followup_query_rewrites.return_value = (
            [{"description": "search function implementation"}],
            [mock_chunk],
        )
        mock_get_final_round_queries.return_value = [
            {"description": "final search query"}
        ]
        mock_format_retrieval.return_value = (
            "Formatted retrieval output",
            [mock_chunk],
        )

        # Call the method
        result = agent.run_impl(tool_input)

        # Verify the result
        assert isinstance(result, ToolImplOutput)
        assert result.tool_output == "Formatted retrieval output"
        assert result.tool_result_message == "Codebase information retrieved"
        assert "retriever_queries" in result.auxiliary_data
        assert "final_round_queries" in result.auxiliary_data
        assert "retrieved_chunks" in result.auxiliary_data

        # Verify the mocks were called correctly
        mock_retrieve.assert_any_call(
            agent.retriever,
            tool_input["information_request"],
            "",  # request_path
            "",  # request_contains_string
            agent.max_num_chunks,
        )
        mock_followup_query_rewrites.assert_called_once_with(
            tool_input["information_request"],
            agent.llm_client,
            [mock_chunk],
            dialog_messages=None,
            max_initial_retrieval_chars=agent.max_initial_retrieval_chars,
            max_directory_chars=agent.max_directory_chars,
        )
        mock_get_final_round_queries.assert_called_once_with(
            tool_input,
            [(mock_followup_query_rewrites.return_value[0][0], [mock_chunk], [0.9])],
            dialog_messages=None,
            used_initial_retrieval_chunks=mock_followup_query_rewrites.return_value[1],
        )
        mock_format_retrieval.assert_called_once()


def test_highlevecodebase3stepretrievalagent_with_dialog(
    tool_call_logger, mock_retriever, mock_llm_client
):
    """Test the HighLevelCodebase3StepRetrievalAgent with dialog messages."""
    # Create the agent
    agent = HighLevelCodebase3StepRetrievalAgent(
        tool_call_logger=tool_call_logger,
        retriever=mock_retriever,
        llm_client=mock_llm_client,
    )

    # Create mock input
    tool_input = {
        "information_request": "Find the implementation of the search function"
    }

    # Create mock dialog messages
    dialog_messages = Mock(spec=DialogMessages)

    # Create mock chunks and scores for the initial retrieval
    mock_chunk = Mock(spec=Chunk)
    mock_chunk.text = "def search(query): return results"
    mock_chunk.path = "src/search.py"
    mock_chunk.id = "chunk1"
    mock_chunk.char_offset = 0
    mock_chunk.length = len(mock_chunk.text)
    mock_chunk.parent_doc = Mock(spec=Document)
    mock_chunk.parent_doc.id = "doc1"
    mock_chunk.header = "def search"

    # Mock the retrieve function
    with patch(
        "experimental.michiel.research.agentqa.tools_new.retrieve"
    ) as mock_retrieve, patch(
        "experimental.michiel.research.agentqa.tools_new.followup_query_rewrites_given_initial_retrieval"
    ) as mock_followup_query_rewrites, patch(
        "experimental.michiel.research.agentqa.tools_new.HighLevelCodebase3StepRetrievalAgent.get_final_round_retriever_queries"
    ) as mock_get_final_round_queries, patch(
        "experimental.michiel.research.agentqa.tools_new.HighLevelCodebase3StepRetrievalAgent.format_retrieval_for_output"
    ) as mock_format_retrieval:
        # Set up the mocks
        mock_retrieve.side_effect = [
            ([mock_chunk], [0.9]),
            ([mock_chunk], [0.9]),
            ([mock_chunk], [0.9]),
        ]
        mock_followup_query_rewrites.return_value = (
            [{"description": "search function implementation"}],
            [mock_chunk],
        )
        mock_get_final_round_queries.return_value = [
            {"description": "final search query"}
        ]
        mock_format_retrieval.return_value = (
            "Formatted retrieval output",
            [mock_chunk],
        )

        # Call the method with dialog messages
        result = agent.run_impl(tool_input, dialog_messages=dialog_messages)

        # Verify the result
        assert isinstance(result, ToolImplOutput)

        # Verify the mocks were called with dialog_messages
        mock_followup_query_rewrites.assert_called_once_with(
            tool_input["information_request"],
            agent.llm_client,
            [mock_chunk],
            dialog_messages=dialog_messages,
            max_initial_retrieval_chars=agent.max_initial_retrieval_chars,
            max_directory_chars=agent.max_directory_chars,
        )
        mock_get_final_round_queries.assert_called_once_with(
            tool_input,
            [(mock_followup_query_rewrites.return_value[0][0], [mock_chunk], [0.9])],
            dialog_messages=dialog_messages,
            used_initial_retrieval_chunks=mock_followup_query_rewrites.return_value[1],
        )


# Tests for create_directory_structure function


def test_create_directory_structure_empty_list():
    """Test create_directory_structure with an empty list of chunks."""
    # Test with empty list
    chunks = []
    directory_structure = create_directory_structure(chunks)

    # Verify the result
    assert directory_structure == {"": {"files": [], "dirs": [], "dir_name_set": set()}}


def test_create_directory_structure_simple_files():
    """Test create_directory_structure with simple file paths (no directories)."""
    # Create mock chunks with simple file paths
    chunks = [
        Mock(spec=Chunk, path="file1.py"),
        Mock(spec=Chunk, path="file2.py"),
        Mock(spec=Chunk, path="file3.txt"),
    ]

    directory_structure = create_directory_structure(chunks)

    # Verify the result
    assert "" in directory_structure
    assert len(directory_structure[""]["files"]) == 3
    assert len(directory_structure[""]["dirs"]) == 0

    # Check that files are sorted alphabetically
    file_names = [file.name for file in directory_structure[""]["files"]]
    assert file_names == ["file1.py", "file2.py", "file3.txt"]


def test_create_directory_structure_nested_directories():
    """Test create_directory_structure with nested directory structures."""
    # Create mock chunks with nested directory paths
    chunks = [
        Mock(spec=Chunk, path="dir1/file1.py"),
        Mock(spec=Chunk, path="dir1/subdir1/file2.py"),
        Mock(spec=Chunk, path="dir2/file3.txt"),
    ]

    directory_structure = create_directory_structure(chunks)

    # Verify the result
    assert "" in directory_structure
    assert "dir1" in directory_structure
    assert "dir1/subdir1" in directory_structure
    assert "dir2" in directory_structure

    # Check root directory
    assert len(directory_structure[""]["files"]) == 0
    assert len(directory_structure[""]["dirs"]) == 2
    dir_names = [dir.name for dir in directory_structure[""]["dirs"]]
    assert sorted(dir_names) == ["dir1", "dir2"]

    # Check dir1 directory
    assert len(directory_structure["dir1"]["files"]) == 1
    assert len(directory_structure["dir1"]["dirs"]) == 1
    assert directory_structure["dir1"]["files"][0].name == "file1.py"
    assert directory_structure["dir1"]["dirs"][0].name == "subdir1"

    # Check dir1/subdir1 directory
    assert len(directory_structure["dir1/subdir1"]["files"]) == 1
    assert len(directory_structure["dir1/subdir1"]["dirs"]) == 0
    assert directory_structure["dir1/subdir1"]["files"][0].name == "file2.py"

    # Check dir2 directory
    assert len(directory_structure["dir2"]["files"]) == 1
    assert len(directory_structure["dir2"]["dirs"]) == 0
    assert directory_structure["dir2"]["files"][0].name == "file3.txt"


def test_create_directory_structure_duplicate_paths():
    """Test create_directory_structure with duplicate paths."""
    # Create mock chunks with duplicate paths
    chunks = [
        Mock(spec=Chunk, path="dir1/file1.py"),
        Mock(spec=Chunk, path="dir1/file1.py"),  # Duplicate
        Mock(spec=Chunk, path="dir2/file2.py"),
        Mock(spec=Chunk, path="dir2/file2.py"),  # Duplicate
    ]

    directory_structure = create_directory_structure(chunks)

    # Verify the result - duplicates should be removed
    assert "dir1" in directory_structure
    assert "dir2" in directory_structure

    # Check dir1 directory - should only have one file despite duplicates
    assert len(directory_structure["dir1"]["files"]) == 1
    assert directory_structure["dir1"]["files"][0].name == "file1.py"

    # Check dir2 directory - should only have one file despite duplicates
    assert len(directory_structure["dir2"]["files"]) == 1
    assert directory_structure["dir2"]["files"][0].name == "file2.py"


def test_create_directory_structure_mixed_paths():
    """Test create_directory_structure with mixed paths (some with directories, some without)."""
    # Create mock chunks with mixed paths
    chunks = [
        Mock(spec=Chunk, path="root_file.py"),
        Mock(spec=Chunk, path="dir1/file1.py"),
        Mock(spec=Chunk, path="file2.txt"),
    ]

    directory_structure = create_directory_structure(chunks)

    # Verify the result
    assert "" in directory_structure
    assert "dir1" in directory_structure

    # Check root directory
    assert len(directory_structure[""]["files"]) == 2
    assert len(directory_structure[""]["dirs"]) == 1
    file_names = [file.name for file in directory_structure[""]["files"]]
    assert sorted(file_names) == ["file2.txt", "root_file.py"]
    assert directory_structure[""]["dirs"][0].name == "dir1"

    # Check dir1 directory
    assert len(directory_structure["dir1"]["files"]) == 1
    assert len(directory_structure["dir1"]["dirs"]) == 0
    assert directory_structure["dir1"]["files"][0].name == "file1.py"


def test_create_directory_structure_none_paths():
    """Test create_directory_structure with chunks that have None paths."""
    # Create mock chunks with some None paths
    chunks = [
        Mock(spec=Chunk, path=None),
        Mock(spec=Chunk, path="dir1/file1.py"),
        Mock(spec=Chunk, path=None),
    ]

    directory_structure = create_directory_structure(chunks)

    # Verify the result - None paths should be ignored
    assert "" in directory_structure
    assert "dir1" in directory_structure

    # Check root directory
    assert len(directory_structure[""]["files"]) == 0
    assert len(directory_structure[""]["dirs"]) == 1
    assert directory_structure[""]["dirs"][0].name == "dir1"

    # Check dir1 directory
    assert len(directory_structure["dir1"]["files"]) == 1
    assert len(directory_structure["dir1"]["dirs"]) == 0
    assert directory_structure["dir1"]["files"][0].name == "file1.py"


# Tests for format_directory_subtree function


def test_format_directory_subtree_empty_directory():
    """Test format_directory_subtree with an empty directory structure."""
    # Create an empty directory structure
    directory_structure = {"": {"files": [], "dirs": [], "dir_name_set": set()}}

    # Format the directory structure
    formatted_output, metadata = format_directory_subtree(
        directory_structure, "", max_directory_chars=1000, max_file_chars=1000
    )

    # Verify the result
    assert formatted_output == ""
    assert metadata["truncated"] is False


def test_format_directory_subtree_simple_structure():
    """Test format_directory_subtree with a simple directory structure."""
    # Create a simple directory structure
    file1 = Mock()
    file1.name = "file1.py"
    file2 = Mock()
    file2.name = "file2.txt"
    dir1 = Mock()
    dir1.name = "dir1"

    directory_structure = {
        "": {"files": [file1, file2], "dirs": [dir1], "dir_name_set": {"dir1"}},
        "dir1": {"files": [], "dirs": [], "dir_name_set": set()},
    }

    # Format the directory structure
    formatted_output, metadata = format_directory_subtree(
        directory_structure, "", max_directory_chars=1000, max_file_chars=1000
    )

    # Verify the result
    expected_output = "dir1\nfile1.py\nfile2.txt\n"
    assert formatted_output == expected_output
    assert metadata["truncated"] is False


def test_format_directory_subtree_nested_structure():
    """Test format_directory_subtree with a nested directory structure."""
    # Create a nested directory structure
    file1 = Mock()
    file1.name = "file1.py"
    file2 = Mock()
    file2.name = "file2.txt"
    file3 = Mock()
    file3.name = "file3.py"
    dir1 = Mock()
    dir1.name = "dir1"
    dir2 = Mock()
    dir2.name = "dir2"

    directory_structure = {
        "": {"files": [], "dirs": [dir1], "dir_name_set": {"dir1"}},
        "dir1": {"files": [file1], "dirs": [dir2], "dir_name_set": {"dir2"}},
        "dir1/dir2": {"files": [file2, file3], "dirs": [], "dir_name_set": set()},
    }

    # Format the directory structure
    formatted_output, metadata = format_directory_subtree(
        directory_structure, "", max_directory_chars=1000, max_file_chars=1000
    )

    # Verify the result
    expected_output = "dir1\n  file1.py\n  dir2\n    file2.txt\n    file3.py\n"
    assert formatted_output == expected_output
    assert metadata["truncated"] is False


def test_format_directory_subtree_truncated():
    """Test format_directory_subtree with truncation due to character limits."""
    # Create a directory structure that will be truncated
    file1 = Mock()
    file1.name = "file1.py"
    file2 = Mock()
    file2.name = "file2.txt"
    file3 = Mock()
    file3.name = "file3.py"
    dir1 = Mock()
    dir1.name = "dir1"
    dir2 = Mock()
    dir2.name = "dir2"

    directory_structure = {
        "": {"files": [], "dirs": [dir1], "dir_name_set": {"dir1"}},
        "dir1": {"files": [file1], "dirs": [dir2], "dir_name_set": {"dir2"}},
        "dir1/dir2": {"files": [file2, file3], "dirs": [], "dir_name_set": set()},
    }

    # Format the directory structure with low character limits
    formatted_output, metadata = format_directory_subtree(
        directory_structure,
        "",
        max_directory_chars=1,  # Extremely low limit to force truncation
        max_file_chars=1,
    )

    # Verify the result
    assert metadata["truncated"] is True


def test_format_directory_subtree_nonexistent_directory():
    """Test format_directory_subtree with a non-existent directory."""
    # Create a simple directory structure
    directory_structure = {"": {"files": [], "dirs": [], "dir_name_set": set()}}

    # Format a non-existent directory
    formatted_output, metadata = format_directory_subtree(
        directory_structure,
        "nonexistent",
        max_directory_chars=1000,
        max_file_chars=1000,
    )

    # Verify the result
    assert "not found" in formatted_output
    assert metadata["truncated"] is False


# Tests for RouterTool


@pytest.fixture
def mock_llm_client_router():
    """Create a mock LLM client for RouterTool tests."""
    llm_client = Mock(spec=LLMClient)

    # Setup the generate method to return a mock response for UserQueryAnnotatorTool
    tool_call = Mock(spec=ToolCall)
    tool_call.tool_input = {
        "requires_codebase": True,
        "requires_action": False,
        "about_specific_file": [],
    }
    llm_client.generate.return_value = ([tool_call], None)

    return llm_client


@pytest.fixture
def mock_chunk_for_router(mock_document):
    """Create a mock chunk for RouterTool tests."""
    chunk = Mock(spec=Chunk)
    chunk.id = "router_chunk_id"
    chunk.text = "def router_function(): pass"
    chunk.path = "router/path.py"
    chunk.char_offset = 0
    chunk.length = len(chunk.text)
    chunk.parent_doc = mock_document
    chunk.header = "def router_function():"
    return chunk


def test_router_tool_init(tool_call_logger, mock_retriever, mock_llm_client_router):
    """Test initialization of RouterTool."""
    # Create the tool
    router_tool = RouterTool(
        tool_call_logger=tool_call_logger,
        retriever=mock_retriever,
        llm_client=mock_llm_client_router,
        max_input_retrieval_chars=10000,
        max_output_retrieval_chars=15000,
        max_input_dialog_chars=20000,
    )

    # Verify the tool was initialized correctly
    assert router_tool.tool_call_logger == tool_call_logger
    assert router_tool.retriever == mock_retriever
    assert router_tool.llm_client == mock_llm_client_router
    assert router_tool.max_input_retrieval_chars == 10000
    assert router_tool.max_output_retrieval_chars == 15000
    assert router_tool.max_input_dialog_chars == 20000
    assert router_tool.max_num_chunks == 1024
    assert router_tool.name == "router"


def test_router_tool_perform_llm_call(
    tool_call_logger, mock_retriever, mock_llm_client_router, mock_chunk_for_router
):
    """Test _perform_llm_call method of RouterTool."""
    # Create the tool
    router_tool = RouterTool(
        tool_call_logger=tool_call_logger,
        retriever=mock_retriever,
        llm_client=mock_llm_client_router,
    )

    # Test with basic parameters
    user_message = "What is the router tool?"
    context_chunks = [mock_chunk_for_router]

    # Call the method
    annotations = router_tool._perform_llm_call(user_message, context_chunks)

    # Verify the result
    assert isinstance(annotations, dict)
    assert "requires_codebase" in annotations
    assert "requires_action" in annotations
    assert "about_specific_file" in annotations
    assert annotations["requires_codebase"] is True
    assert annotations["requires_action"] is False
    assert annotations["about_specific_file"] == []

    # Verify LLM client was called correctly
    mock_llm_client_router.generate.assert_called_once()
    call_args = mock_llm_client_router.generate.call_args[1]
    assert "tools" in call_args
    assert "tool_choice" in call_args
    assert call_args["tool_choice"]["name"] == "annotate_user_query"

    # Test with dialog messages
    mock_llm_client_router.reset_mock()
    dialog_messages = Mock(spec=DialogMessages)
    dialog_messages.get_messages_for_llm_client.return_value = [[]]

    # Call the method with dialog messages
    with patch(
        "experimental.michiel.research.agentqa.tools_new.format_dialog_as_string"
    ) as mock_format_dialog:
        mock_format_dialog.return_value = "Formatted dialog"
        router_tool._perform_llm_call(user_message, context_chunks, dialog_messages)

        # Verify format_dialog_as_string was called
        mock_format_dialog.assert_called_once()


def test_router_tool_format_tool_output(
    tool_call_logger, mock_retriever, mock_llm_client_router, mock_chunk_for_router
):
    """Test format_tool_output method of RouterTool."""
    # Create the tool
    router_tool = RouterTool(
        tool_call_logger=tool_call_logger,
        retriever=mock_retriever,
        llm_client=mock_llm_client_router,
    )

    # Test with requires_action=False
    user_message = "What is the router tool?"
    annotations = {
        "requires_codebase": True,
        "requires_action": False,
        "about_specific_file": [],
    }
    context_chunks = [mock_chunk_for_router]

    # Mock format_retrieval function
    with patch(
        "experimental.michiel.research.agentqa.tools_new.format_retrieval"
    ) as mock_format_retrieval:
        mock_format_retrieval.return_value = ("Formatted retrieval", context_chunks)

        # Call the method
        result = router_tool.format_tool_output(
            user_message, annotations, context_chunks
        )

        # Verify the result
        assert user_message in result
        assert "<supervisor>" in result
        assert "This request should use only read-only actions" in result
        assert "<supporting_info>" in result
        assert "Formatted retrieval" in result

        # Verify format_retrieval was called correctly
        mock_format_retrieval.assert_called_once_with(
            context_chunks, router_tool.max_output_retrieval_chars
        )

    # Test with requires_action=True
    annotations["requires_action"] = True

    with patch(
        "experimental.michiel.research.agentqa.tools_new.format_retrieval"
    ) as mock_format_retrieval:
        mock_format_retrieval.return_value = ("Formatted retrieval", context_chunks)

        # Call the method
        result = router_tool.format_tool_output(
            user_message, annotations, context_chunks
        )

        # Verify the result
        assert user_message in result
        assert "<supervisor>" not in result
        assert "<supporting_info>" in result
        assert "Formatted retrieval" in result


def test_router_tool_run_impl(
    tool_call_logger, mock_retriever, mock_llm_client_router, mock_chunk_for_router
):
    """Test run_impl method of RouterTool."""
    # Create the tool
    router_tool = RouterTool(
        tool_call_logger=tool_call_logger,
        retriever=mock_retriever,
        llm_client=mock_llm_client_router,
    )

    # Create tool input
    tool_input = {"message": "What is the router tool?"}

    # Mock the retrieve function
    with patch(
        "experimental.michiel.research.agentqa.tools_new.retrieve"
    ) as mock_retrieve, patch.object(
        router_tool, "_perform_llm_call"
    ) as mock_perform_llm_call, patch.object(
        router_tool, "format_tool_output"
    ) as mock_format_tool_output, patch(
        "experimental.michiel.research.agentqa.tools_new.format_retrieval"
    ) as mock_format_retrieval, patch(
        "experimental.michiel.research.agentqa.tools_new.interleave_sequences"
    ) as mock_interleave:
        # Configure mocks
        mock_retrieve.return_value = ([mock_chunk_for_router], [0.9])
        mock_perform_llm_call.return_value = {
            "requires_codebase": True,
            "requires_action": False,
            "about_specific_file": [],
        }
        mock_format_tool_output.return_value = "Formatted output"
        mock_format_retrieval.return_value = (
            "Formatted retrieval",
            [mock_chunk_for_router],
        )

        # Call the method
        result = router_tool.run_impl(tool_input)

        # Verify the result
        assert isinstance(result, ToolImplOutput)
        assert result.tool_output == "Formatted output"
        assert result.tool_result_message == "Query analyzed and routed"
        assert "annotations" in result.auxiliary_data

        # Verify method calls
        mock_retrieve.assert_called_once()
        mock_perform_llm_call.assert_called_once()
        mock_format_tool_output.assert_called_once()

        # Test with about_specific_file
        mock_retrieve.reset_mock()
        mock_perform_llm_call.reset_mock()
        mock_format_tool_output.reset_mock()

        # Configure mocks for specific file case
        mock_perform_llm_call.return_value = {
            "requires_codebase": True,
            "requires_action": False,
            "about_specific_file": ["path/to/file.py"],
        }
        mock_interleave.return_value = [mock_chunk_for_router]

        # Call the method again
        result = router_tool.run_impl(tool_input)

        # Verify retrieve was called for the specific file
        assert mock_retrieve.call_count == 2
        assert mock_retrieve.call_args_list[1][1]["request_path"] == "path/to/file.py"

        # Verify interleave_sequences was called
        mock_interleave.assert_called_once()


def test_router_tool_get_tool_start_message(
    tool_call_logger, mock_retriever, mock_llm_client_router
):
    """Test get_tool_start_message method of RouterTool."""
    # Create the tool
    router_tool = RouterTool(
        tool_call_logger=tool_call_logger,
        retriever=mock_retriever,
        llm_client=mock_llm_client_router,
    )

    # Test with a simple message
    tool_input = {"message": "What is the router tool?"}
    message = router_tool.get_tool_start_message(tool_input)

    # Verify the result
    assert message == "Routing message: What is the router tool?"

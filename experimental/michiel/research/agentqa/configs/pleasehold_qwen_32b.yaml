determined:
  description: "Pleasehold training."
  workspace: Dev
  project: yury

augment:
  project_group: finetuning
  podspec_path: "8xH100.yaml"
  gpu_count: 64

fastbackward_configs:
 - configs/qwen25coder_32b.py

fastbackward_args:
  loss_mask_policy: negative_tokens
  batch_size: 1
  gradient_accumulation_steps: 16
  warmup_iters: 32
  learning_rate: 1e-5
  min_lr: 1e-6
  decay_lr: True
  max_epochs: 1
  eval_interval: 50
  block_size: 16384
  use_activation_checkpointing: True

  train_data_path: /mnt/efs/spark-data/shared/agentqa/v0/i0-vanguard0_since_20240101_bin_v2/vanguard_train
  eval_data_path: vanguard_valid@/mnt/efs/spark-data/shared/agentqa/v0/i0-vanguard0_since_20240101_bin_v2/vanguard_valid;dogfood@/mnt/efs/spark-data/shared/agentqa/v0/i0-vanguard0_since_20240101_bin_v2/dogfood

  checkpoint_optimizer_state: False
  hf_checkpoint_dir: /mnt/efs/augment/checkpoints/qwen25-coder/Qwen2.5-Coder-32B-Instruct

  tokenizer_name: qwen25coder
  use_research_tokenizer: false
  visualize_logits_samples: 32
  model_parallel_size: 8
  use_sequence_parallel: True

  run_name: pleasehold_qwen32b_v2_bsz128
  wandb_project: pleasehold

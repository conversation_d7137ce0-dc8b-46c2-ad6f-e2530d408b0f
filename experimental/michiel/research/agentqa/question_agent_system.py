"""System for anthropic agent."""

import dataclasses
import logging
import typing
from pathlib import Path

from experimental.michiel.research.agentqa.agent import (
    AgentFunction,
    AgentLog,
    InformationAgent,
    JudgeAnswerAgent,
    ProduceAnswerAgent,
    TopLevelQuestionAnsweringAgent,
)
from experimental.michiel.research.agentqa.fast_agent import (
    FastInformationAgent,
    FastQuestionAnsweringAgent,
    InitialInformationAgent,
)
from experimental.michiel.research.agentqa.tools import (
    CodebaseRetrievalTool,
    DirectorySubTreeTool,
    JudgeAnswerTool,
    QueryRouterTool,
    ToolSelectionTool,
)
from research.core.chat_prompt_input import ResearchChatPromptInput
from research.eval.harness import factories
from research.eval.harness.systems.abs_system import (
    ChatResult,
    ChatSystem,
    register_system,
)
from research.llm_apis.llm_client import (
    get_client,
)
from research.models.all_models import get_model
from research.models.meta_model import ExtraGenerationOutputs, GenerativeLanguageModel
from research.retrieval.types import Document, DocumentId, DocumentIndex

logger = logging.getLogger(__name__)


@register_system("question_agent")
class QuestionAgentSystem(ChatSystem):
    """Agent system."""

    def __init__(
        self,
        agent: AgentFunction,
        retriever: DocumentIndex,
        directory_tree: DirectorySubTreeTool,
        local_models: list[GenerativeLanguageModel],
        verbose: bool = False,
    ):
        self.agent = agent
        self.retriever = retriever
        self.directory_tree = directory_tree
        self.local_models = local_models
        self.verbose = verbose
        self.__loaded = False

        # We provide a dummy model to satisfy the AbstractSystem interface.
        self.model = self.get_model()

    def load(self):
        if not self.__loaded:
            self.retriever.load()
            for model in self.local_models:
                model.load()
        self.__loaded = True

    def unload(self):
        if self.__loaded:
            self.retriever.unload()
            for model in self.local_models:
                model.unload()
            self.__loaded = False

    def add_docs(self, src_files: typing.Collection[Document]):
        """Ingest a copy of the source code repository.

        Args:
          src_files: list of Documents to add
        """
        logger.info(f"Adding {len(list(src_files))} documents.")
        self.retriever.add_docs(src_files)  # type: ignore
        doc_ids = self.retriever.get_doc_ids()
        docs = self.retriever.get_docs(doc_ids)
        self.directory_tree.set_files(docs)
        total_docs = len(doc_ids)
        logger.info(f"There are now {total_docs} total docs.")

    def remove_docs(self, doc_ids: typing.Collection[DocumentId]):
        """Remove documents from the retriever."""
        logger.info(f"Removing {len(list(doc_ids))} documents.")
        self.retriever.remove_docs(doc_ids)
        all_doc_ids = self.retriever.get_doc_ids()
        docs = self.retriever.get_docs(all_doc_ids)
        self.directory_tree.set_files(docs)
        total_docs = len(all_doc_ids)
        logger.info(f"There are now {total_docs} total docs.")

    def get_doc_ids(self) -> typing.AbstractSet[DocumentId]:
        return self.retriever.get_doc_ids()

    def get_docs(self, doc_ids: typing.Iterable[DocumentId]) -> list[Document]:
        return self.retriever.get_docs(doc_ids)

    def clear_retriever(self):
        """Clear any stored documents from the retriever."""
        self.retriever.remove_all_docs()
        self.directory_tree.set_files([])

    def generate(
        self,
        model_input: ResearchChatPromptInput,
    ) -> ChatResult:
        """Generate a completion."""
        null_logs = AgentLog()
        answer = self.agent(null_logs, model_input).answer
        agent_logs = null_logs.child_logs[0]
        extra_output = ExtraGenerationOutputs(
            additional_info={"output_state": agent_logs.to_dict()},
        )
        chat_result = ChatResult(
            generated_text=answer or "",
            prompt_tokens=[],
            retrieved_chunks=[],
            extra_output=extra_output,
        )

        return chat_result

    def get_model(self):
        return get_model("null")

    @dataclasses.dataclass
    class _AgentSystemBuildConfig:
        """Schema for constructing a System."""

        retriever_config: dict
        model_config: dict
        agent_config: dict
        tool_config: dict = dataclasses.field(default_factory=dict)
        verbose: bool = False

    @classmethod
    def from_yaml_config(cls, config: dict) -> "QuestionAgentSystem":
        """Returns a System object constructed using a config dictionary."""
        # prompt configuration happens as part of creating the model
        xconfig = cls._AgentSystemBuildConfig(**config)
        chat_history_len = xconfig.agent_config.pop("chat_history_len", 0)
        retriever = factories.create_retriever(xconfig.retriever_config)

        default_client_config = xconfig.model_config.get("client", {})
        default_client = get_client(
            default_client_config.pop("client_name", "anthropic-direct"),
            **default_client_config,
        )

        answer_client_config = xconfig.model_config.get("answer_client", None)
        if answer_client_config is not None:
            answer_client = get_client(
                answer_client_config.pop("client_name"), **answer_client_config
            )
        else:
            answer_client = default_client
        produce_answer_agent = ProduceAnswerAgent(
            model_client=answer_client, chat_history_len=chat_history_len
        )
        judge_answer_tool = JudgeAnswerTool()
        judge_answer_agent = JudgeAnswerAgent(
            model_client=default_client, judge_answer_tool=judge_answer_tool
        )
        max_codebase_tool_chars = xconfig.tool_config.pop(
            "max_codebase_tool_chars", 20000
        )
        max_num_chunks = xconfig.tool_config.pop("max_num_chunks", 20)
        codebase_retrieval_tool = CodebaseRetrievalTool(
            retriever=retriever,
            max_tool_chars=max_codebase_tool_chars,
            max_num_chunks=max_num_chunks,
        )
        doc_ids = retriever.get_doc_ids()
        docs = retriever.get_docs(doc_ids)

        max_directory_chars = xconfig.tool_config.pop("max_directory_chars", 10000)
        max_file_chars = xconfig.tool_config.pop("max_file_chars", 10000)
        directory_subtree_tool = DirectorySubTreeTool(
            files=docs,
            max_directory_chars=max_directory_chars,
            max_file_chars=max_file_chars,
        )
        tool_selection_tool = ToolSelectionTool()
        information_agent = InformationAgent(
            model_client=default_client,
            codebase_retrieval_tool=codebase_retrieval_tool,
            directory_subtree_tool=directory_subtree_tool,
            tool_selection_tool=tool_selection_tool,
            chat_history_len=chat_history_len,
        )

        agent = TopLevelQuestionAnsweringAgent(
            produce_answer_agent=produce_answer_agent,
            judge_answer_agent=judge_answer_agent,
            information_agent=information_agent,
            **xconfig.agent_config,
        )
        return QuestionAgentSystem(
            agent=agent,
            retriever=retriever,
            directory_tree=directory_subtree_tool,
            local_models=[],
            verbose=xconfig.verbose,
        )


@register_system("fast_question_agent")
class FastQuestionAgentSystem(QuestionAgentSystem):
    """Low latency agent system."""

    @classmethod
    def from_yaml_config(cls, config: dict) -> "FastQuestionAgentSystem":
        """Returns a System object constructed using a config dictionary."""
        xconfig = cls._AgentSystemBuildConfig(**config)
        retriever = factories.create_retriever(xconfig.retriever_config)

        slow_client = get_client("anthropic-direct", **xconfig.model_config["slow"])

        fastconfig = xconfig.model_config["fast"]
        if "checkpoint_path" in fastconfig:
            fastconfig["checkpoint_path"] = Path(fastconfig["checkpoint_path"])
        fast_model = get_model(**fastconfig)
        fast_client = get_client("qwen-fastforward", model=fast_model)

        codebase_retrieval_client = fast_client
        directory_subtree_client = fast_client
        produce_answer_config = xconfig.agent_config["produce_answer_agent_config"]
        produce_answer_agent = ProduceAnswerAgent(
            model_client=slow_client, **produce_answer_config
        )
        codebase_retrieval_tool = CodebaseRetrievalTool(
            retriever=retriever, max_tool_chars=20000
        )
        doc_ids = retriever.get_doc_ids()
        docs = retriever.get_docs(doc_ids)
        directory_subtree_tool = DirectorySubTreeTool(
            files=docs, max_directory_chars=10000, max_file_chars=10000
        )
        info_config = xconfig.agent_config["information_agent_config"]
        information_agent = FastInformationAgent(
            codebase_retrieval_client=codebase_retrieval_client,
            directory_subtree_client=directory_subtree_client,
            codebase_retrieval_tool=codebase_retrieval_tool,
            directory_subtree_tool=directory_subtree_tool,
            **info_config,
        )
        router_tool = QueryRouterTool()

        initial_information_agent = InitialInformationAgent(
            codebase_retrieval_tool=codebase_retrieval_tool,
            directory_subtree_tool=directory_subtree_tool,
        )
        fast_config = xconfig.agent_config["fast_agent_config"]
        agent = FastQuestionAnsweringAgent(
            router_tool=router_tool,
            router_client=fast_client,
            produce_answer_agent=produce_answer_agent,
            information_agent=information_agent,
            initial_information_agent=initial_information_agent,
            **fast_config,
        )
        return FastQuestionAgentSystem(
            agent=agent,
            retriever=retriever,
            directory_tree=directory_subtree_tool,
            local_models=[fast_model],
            verbose=xconfig.verbose,
        )

"""Tests for agentic_retrieval_agent.py."""

from dataclasses import dataclass
from typing import Any, <PERSON><PERSON>

import pytest

from base.prompt_format_chat.prompt_formatter import ChatPromptInput
from experimental.michiel.research.agentqa.agentic_retrieval_agent import (
    AgenticRetrievalAgent,
    AgenticRetrievalResult,
    ProduceAnswerTool,
)
from research.agents.tools import (
    DialogMessages,
    LLMTool,
    ToolCallLogger,
    ToolImplOutput,
)
from research.llm_apis.llm_client import (
    AssistantContentBlock,
    LLMClient,
    LLMMessages,
    ToolCall,
    ToolParam,
)


class MockRetrievalTool(LLMTool):
    name = "request_codebase_information"
    description = "Use this tool to request information from the codebase."
    input_schema = {
        "type": "object",
        "properties": {
            "information_request": {
                "type": "string",
                "description": "The information to request.",
            }
        },
        "required": ["information_request"],
    }

    def run_impl(self, tool_input, dialog_messages=None):
        return ToolImplOutput(
            tool_output="Found information: The codebase uses Python 3.11",
            tool_result_message="Retrieved information from codebase",
            auxiliary_data={},
        )


class MockLLMClient(LLMClient):
    """Mock LLM client that returns predefined responses for testing."""

    def __init__(self, mock_responses: list[ToolCall]):
        """Initialize the mock client with a list of responses to return in order.

        Args:
            mock_responses: List of ToolCall objects to return in sequence
        """
        self.mock_responses = mock_responses
        self.current_response_index = 0
        self.call_count = 0

    def generate(
        self,
        messages: LLMMessages,
        max_tokens: int,
        system_prompt: str | None = None,
        temperature: float = 0.0,
        tools: list[ToolParam] = [],
        tool_choice: dict[str, str] | None = None,
    ) -> Tuple[list[AssistantContentBlock], dict[str, Any]]:
        """Generate responses following LLMClient API.

        Args:
            messages: A list of messages
            max_tokens: The maximum number of tokens to generate
            system_prompt: A system prompt
            temperature: The temperature
            tools: A list of tools
            tool_choice: A tool choice

        Returns:
            Tuple of (list of AssistantContentBlock, metadata dict)

        Raises:
            IndexError: If no more mock responses are available
        """
        if self.current_response_index >= len(self.mock_responses):
            raise IndexError("No more mock responses available")

        response = self.mock_responses[self.current_response_index]
        self.current_response_index += 1
        self.call_count += 1

        return [response], {"mock": True}


def test_agentic_retrieval_tool_can_be_created():
    """Test that AgenticRetrievalTool can be instantiated."""
    llm_client = LLMClient()
    tool_call_logger = ToolCallLogger()
    answer_tool = ProduceAnswerTool(tool_call_logger)
    retrieval_tool = MockRetrievalTool(tool_call_logger)

    tool = AgenticRetrievalAgent(
        llm_client=llm_client,
        answer_tool=answer_tool,
        retrieval_tool=retrieval_tool,
        max_iterations=2,
    )

    assert isinstance(tool, AgenticRetrievalAgent)


def test_agentic_retrieval_tool_single_turn():
    """Test that AgenticRetrievalTool can run a single turn and produce an answer."""
    # Mock LLM to first use retrieval tool, then answer tool
    mock_responses = [
        ToolCall(
            tool_call_id="1",
            tool_name="request_codebase_information",
            tool_input={"information_request": "What Python version is used?"},
        ),
        ToolCall(
            tool_call_id="2",
            tool_name="produce_answer",
            tool_input={"answer": "The codebase uses Python version 3.11"},
        ),
    ]
    llm_client = MockLLMClient(mock_responses)
    tool_call_logger = ToolCallLogger()
    answer_tool = ProduceAnswerTool(tool_call_logger)
    retrieval_tool = MockRetrievalTool(tool_call_logger)

    agent = AgenticRetrievalAgent(
        llm_client=llm_client,
        answer_tool=answer_tool,
        retrieval_tool=retrieval_tool,
        max_iterations=2,
    )

    chat_input = ChatPromptInput(
        message="What Python version is used?",
        path="",
        prefix="",
        selected_code="",
        suffix="",
        chat_history=[],
        prefix_begin=0,
        suffix_end=0,
        retrieved_chunks=[],
    )

    result = agent.run(chat_input)
    assert isinstance(result, AgenticRetrievalResult)
    assert result.answer_text == "The codebase uses Python version 3.11"

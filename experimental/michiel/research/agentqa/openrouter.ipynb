{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.llm_apis.chat_utils import OpenRouterClient\n", "\n", "import os\n", "\n", "# Get your API key env variable\n", "openrouter_api_key = os.environ[\"OPENROUTER_API_KEY\"]\n", "\n", "# # Create an instance of the OpenRouterClient class\n", "openrouter_client = OpenRouterClient(\n", "    api_key=openrouter_api_key,\n", "    provider_name=\"Anthropic\",\n", "    model_name=\"anthropic/claude-3-haiku\",\n", ")\n", "\n", "openrouter_client.generate(\n", "    messages=[\"What model are you?\"],\n", "    max_tokens=256,\n", "    system_prompt=\"You are a helpful assistant.\",\n", "    contains_sensitive_information=False,\n", ")"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 2}
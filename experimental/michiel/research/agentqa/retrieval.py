import base64

from google.cloud import storage

from base.datasets.gcp_creds import get_gcp_creds
from base.datasets.gcs_blob_cache import GCSBlobCache, GCSCheckpointCache
from base.datasets.gcs_client import GCSRequestInsightFetcher
from base.datasets.tenants import DatasetTenant
from base.prompt_format.common import Exchange
from base.prompt_format_chat.prompt_formatter import ChatPromptInput
from research.core.types import Document
from research.retrieval.retrieval_database import RetrievalDatabase
from services.chat_host import chat_pb2


def get_caches(tenant):
    gcp_creds, _ = get_gcp_creds(None)
    storage_client = storage.Client(project=tenant.project_id, credentials=gcp_creds)
    blob_bucket = storage_client.bucket(tenant.blob_bucket_name)
    blob_cache_size_bytes = 2**30
    blob_cache_num_threads = 32
    blob_cache = GCSBlobCache(
        blob_bucket,
        tenant.blob_bucket_prefix,
        max_size_bytes=blob_cache_size_bytes,
        num_threads=blob_cache_num_threads,
    )
    checkpoint_bucket = storage_client.bucket(tenant.checkpoint_bucket_name)
    checkpoint_cache = GCSCheckpointCache(
        checkpoint_bucket,
        tenant.checkpoint_bucket_prefix,
        blob_cache_size_bytes,
        num_threads=blob_cache_num_threads,
    )
    return blob_cache, checkpoint_cache


class BlobGetter:
    def __init__(self, tenant: DatasetTenant):
        self.tenant = tenant
        self.blob_cache, self.checkpoint_cache = get_caches(tenant)
        self.request_fetcher = GCSRequestInsightFetcher.from_tenant(tenant)

    def get_raw_chat_request(self, request_id):
        request_info = self.request_fetcher.get_request(
            request_id=request_id,
        )
        chat_request = [
            event.chat_host_request
            for event in request_info.events
            if event.HasField("chat_host_request")
        ]
        if len(chat_request) == 0:
            return None
        return chat_request[0].request

    def get_chat_request_without_retrievals(self, request_id) -> ChatPromptInput:
        """Get the chat request (without the retrieved chunks)."""
        raw_request = self.get_raw_chat_request(request_id)
        prefix = raw_request.prefix
        selected_code = raw_request.selected_code
        suffix = raw_request.suffix
        chat_history = []
        for exchange in raw_request.chat_history:
            chat_history.append(
                Exchange(
                    request_message=exchange.request_message,
                    response_text=exchange.response_text,
                    request_id=exchange.request_id,
                )
            )
        request = ChatPromptInput(
            message=raw_request.message,
            path=raw_request.path,
            prefix=prefix,
            selected_code=selected_code,
            suffix=suffix,
            chat_history=chat_history,
            prefix_begin=0,
            suffix_end=len(prefix + selected_code + suffix),
            retrieved_chunks=[],
            context_code_exchange_request_id=raw_request.context_code_exchange_request_id
            or None,
        )
        return request

    def get_blob_names_from_chat_request(self, request: chat_pb2.ChatRequest):
        blob_object_list = request.blobs
        if len(blob_object_list) == 0:
            return []
        assert len(blob_object_list) == 1, len(blob_object_list)
        blob_object_dict = blob_object_list[0]
        checkpoint_id = blob_object_dict.baseline_checkpoint_id
        if checkpoint_id:
            checkpoint_content = self.checkpoint_cache.get([checkpoint_id])[0]
            if checkpoint_content:
                checkpoint_blob_names = set(checkpoint_content.blob_names)
            else:
                checkpoint_blob_names = set()
        else:
            checkpoint_blob_names = set()
        added_blob_names = set([blob.hex() for blob in blob_object_dict.added])
        deleted_blob_names = set([blob.hex() for blob in blob_object_dict.deleted])
        blob_names = sorted(
            list((added_blob_names | checkpoint_blob_names) - deleted_blob_names)
        )
        return blob_names

    def get_blobs_from_chat_request(self, request: chat_pb2.ChatRequest):
        blob_names = self.get_blob_names_from_chat_request(request)
        print(f"Getting {len(blob_names)} blobs")
        blobs = self.blob_cache.get(blob_names)
        documents = [
            Document(id=blob_name, text=blob.content, path=str(blob.path))
            for blob_name, blob in zip(blob_names, blobs)
            if blob is not None
        ]
        return documents

    def get_blobs_from_request_id(self, request_id):
        request = self.get_raw_chat_request(request_id)
        return self.get_blobs_from_chat_request(request)


def create_retriever(config) -> RetrievalDatabase:
    from research.eval.harness.factories import create_retriever

    retrieval_database = create_retriever(config)
    assert isinstance(retrieval_database, RetrievalDatabase)
    return retrieval_database

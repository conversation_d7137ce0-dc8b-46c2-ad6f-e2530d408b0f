"""An agent that uses an agentic approach to retrieval."""

from copy import deepcopy
from dataclasses import dataclass
from textwrap import dedent
from typing import Optional

from experimental.michiel.research.agentqa.agent import (
    ChatPromptInput,
    format_chat_input,
)
from experimental.michiel.research.agentqa.tools_new import (
    ProduceAnswerTool,
)
from research.agents.tools import (
    DialogMessages,
    LLMTool,
    LoggingLLMClient,
)


@dataclass
class ChatAgentResult:
    """A result from the chat agent."""

    output: str
    """The chat output."""


class ChatAgent:
    """A chat agent interface."""

    def run(self, chat_input: ChatPromptInput) -> ChatAgentResult:
        """Run the agent.

        Args:
            chat_input: The chat input containing the question.

        Returns:
            The result of the agent.
        """
        raise NotImplementedError


class AgenticRetrievalChatAgent(ChatAgent):
    """An agent that uses an agentic approach to retrieval.

    This agent takes a chat input and repeatedly calls an LLM with access to two tools:
    1. An answer tool that produces a final answer
    2. A high level retrieval agent that gets more information

    After K iterations, it can only call the answer tool.

    The tool enforces tool usage by setting tool_choice="required" in LLM calls,
    ensuring the model must use one of the provided tools rather than generating
    free-form responses.
    """

    def __init__(
        self,
        llm_client: LoggingLLMClient,
        answer_tool: ProduceAnswerTool,
        retrieval_tool: LLMTool,
        max_iterations: int,
        initial_turn_tool: Optional[LLMTool] = None,
    ):
        """Initialize the tool.

        Args:
            llm_client: The LLM client to use.
            answer_tool: Tool that produces a final answer.
            retrieval_tool: Tool that retrieves more information.
            max_iterations: Maximum number of iterations.
        """
        self.llm_client = llm_client
        self.answer_tool = answer_tool
        self.retrieval_tool = retrieval_tool
        self.initial_turn_tool = initial_turn_tool
        self.max_iterations = max_iterations
        assert (
            self.max_iterations > 1
        ), "Need at least 2 iterations for this agent to make sense - one to retrieve and one to answer"

    def run(self, chat_input: ChatPromptInput) -> ChatAgentResult:
        """Run the tool.

        Args:
            chat_input: The chat input containing the question.

        Returns:
            ChatAgentResult containing the final answer.
        """

        answer = ""

        dialog_history = DialogMessages()
        for iteration in range(self.max_iterations):
            print(f"Running iteration {iteration} of {self.max_iterations}")
            if iteration == 0:
                if self.initial_turn_tool is not None:
                    tools = [
                        self.answer_tool.get_tool_param(),
                        self.initial_turn_tool.get_tool_param(),
                    ]
                else:
                    tools = [
                        self.answer_tool.get_tool_param(),
                        self.retrieval_tool.get_tool_param(),
                    ]
                dialog_history = self.get_initial_prompt(chat_input, dialog_history)
            elif iteration == self.max_iterations - 1:
                # Last iteration, only allow answer tool
                tools = [self.answer_tool.get_tool_param()]
                dialog_history = self.get_last_iteration_prompt(
                    chat_input, dialog_history
                )
            else:
                # Allow both answer and retrieval tool
                tools = [
                    self.answer_tool.get_tool_param(),
                    self.retrieval_tool.get_tool_param(),
                ]
                dialog_history = self.get_iteration_prompt(
                    chat_input, dialog_history, iteration
                )

            response, _ = self.llm_client.generate(
                messages=dialog_history.get_messages_for_llm_client(),
                max_tokens=2048,
                tools=tools,
                tool_choice={"type": "any"},  # Force tool usage
            )
            dialog_history.add_model_response(response)
            pending_tool_calls = dialog_history.get_pending_tool_calls()
            assert len(pending_tool_calls) == 1, "Expected exactly one tool call"

            tool_call = pending_tool_calls[0]
            assert (
                tool_call.tool_name
                in [
                    self.answer_tool.name,
                    self.retrieval_tool.name,
                ]
            ), f"Expected tool name to be one of {self.answer_tool.name} or {self.retrieval_tool.name}, got {tool_call.tool_name}"

            tool = (
                self.answer_tool
                if tool_call.tool_name == self.answer_tool.name
                else self.retrieval_tool
            )
            print(f"Calling tool {tool.name} with input: {tool_call.tool_input}")
            tool_result = tool.run(
                tool_input=tool_call.tool_input,
                dialog_messages=deepcopy(dialog_history),
            )
            dialog_history.add_tool_call_result(tool_call, tool_result)

            if tool_call.tool_name == self.answer_tool.name:
                answer = tool_result
                break

        return ChatAgentResult(answer)

    def get_initial_prompt(
        self,
        chat_input: ChatPromptInput,
        dialog_history: DialogMessages,
    ) -> DialogMessages:
        """Get the initial prompt.

        Args:
            chat_input: The chat input containing the question.

        Returns:
            DialogMessages containing the prompt.
        """

        system_prompt = dedent(
            """\
            You are an agent that attempts to answer a user query.
            You have access to two tools:
            1. An answer tool that produces a final answer.
            2. A high level retrieval agent that gets more information.
            After K iterations, you can only call the answer tool.
            """
        )

        initial_message_str = ""
        initial_message_str += system_prompt
        initial_message_str += format_chat_input(chat_input, chat_history_len=2000)

        dialog_history.add_user_prompt(initial_message_str)
        return dialog_history

    def get_iteration_prompt(
        self,
        chat_input: ChatPromptInput,
        dialog_history: DialogMessages,
        iteration: int,
    ) -> DialogMessages:
        """Get the prompt for a single iteration.

        Args:
            chat_input: The chat input containing the question.
            dialog_history: Dialog history for context.

        Returns:
            DialogMessages containing the prompt.
        """

        current_message_str = dedent(
            f"""\
            You are an agent that attempts to answer a user query.
            You have access to two tools:
            1. An answer tool that produces a final answer.
            2. A high level retrieval agent that gets more information.
            After {self.max_iterations} iterations, you can only call the answer tool.
            You are currently in iteration {iteration} of {self.max_iterations}.
            """
        )
        current_message_str += "As a reminder, the user query is:\n"
        current_message_str += format_chat_input(chat_input, chat_history_len=2000)

        dialog_history.add_user_prompt(
            current_message_str, allow_append_to_tool_call_results=True
        )
        return dialog_history

    def get_last_iteration_prompt(
        self,
        chat_input: ChatPromptInput,
        dialog_history: DialogMessages,
    ) -> DialogMessages:
        """Get the prompt for the last iteration.

        Args:
            chat_input: The chat input containing the question.
            dialog_history: Dialog history for context.

        Returns:
            DialogMessages containing the prompt.
        """

        current_message_str = dedent(
            """\
            You are an agent that attempts to answer a user query.
            Given the information you have gathered, you must now produce a final answer by using the produce_answer tool.
            """
        )

        current_message_str += "As a reminder, the user query is:\n"
        current_message_str += format_chat_input(chat_input, chat_history_len=2000)

        dialog_history.add_user_prompt(
            current_message_str, allow_append_to_tool_call_results=True
        )
        return dialog_history

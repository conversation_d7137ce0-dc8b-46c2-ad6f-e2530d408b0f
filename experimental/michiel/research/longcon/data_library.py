"""Prompt generation for rogue long context experiments."""

import random
from types import SimpleNamespace
from typing import Union

from megatron.tokenizer.tokenizer import DeepSeekCoderBaseTokenizer, StarCoderTokenizer

from experimental.michiel.research.longcon.prompt_formatters import (
    RogueLongConFormatter,
)
from research.core.model_input import ModelInput
from research.data.rag.retrieval_utils import deserialize_retrieved_chunks
from research.retrieval import utils as rutils


def generate_prompt(
    prefix: str,
    middle: str,
    suffix: str,
    suffix_offset: int,
    middle_char_start: int,
    middle_char_end: int,
    file_path: str,
    retrieved_chunk_str: str,
    tokenizer: Union[DeepSeekCoderBaseTokenizer, StarCoderTokenizer],
    config: SimpleNamespace,
) -> list[int]:
    """Construct a token prompt."""

    seed = (
        config.random_seed
        + int.from_bytes((file_path).encode(), "little")
        + middle_char_start
    )
    random.seed(seed)

    prompt_formatter = RogueLongConFormatter(
        max_prefix_tokens=config.max_prefix_tokens,
        max_suffix_tokens=config.max_suffix_tokens,
        max_total_near_tokens=config.max_total_near_tokens,
        max_far_tokens=config.max_far_tokens,
        max_noisy_far_tokens=config.max_noisy_far_tokens,
        component_order=config.component_order,
    )
    # Make it so we don't load new tokenizer for every row even though we build prompt formatter
    prompt_formatter.tokenizer = tokenizer
    prompt_formatter.max_prompt_tokens = config.max_prompt_tokens

    retrieved_chunks = deserialize_retrieved_chunks(retrieved_chunk_str)

    # Remove chunks that overlap with middle
    filtered_chunks = rutils.filter_overlap_chunks(
        file_path,
        rutils.Span(middle_char_start, middle_char_end),
        retrieved_chunks,
    )

    model_input = ModelInput(
        prefix=prefix,
        suffix=suffix,
        retrieved_chunks=filtered_chunks,
        path=file_path,
    )

    # TODO(michiel) Add option for sampling different prompt styles
    _, metadata = prompt_formatter.prepare_prompt(model_input)
    # Remove chunks that overlap with prefix or suffix
    new_filtered_chunks = rutils.filter_overlap_chunks(
        file_path,
        rutils.Span(
            middle_char_start - metadata["num_prefix_chars_post_truncation"],
            middle_char_end,
        ),
        filtered_chunks,
    )
    if metadata["num_suffix_chars_post_truncation"] > 0:
        new_filtered_chunks = rutils.filter_overlap_chunks(
            file_path,
            rutils.Span(
                middle_char_end,
                middle_char_end
                + max(metadata["num_suffix_chars_post_truncation"] - suffix_offset, 0),
            ),
            new_filtered_chunks,
        )

    model_input.retrieved_chunks = new_filtered_chunks
    prompt_tokens, _ = prompt_formatter.prepare_prompt(model_input)

    # In the data generation pipeline, the 'middle' section is preprocessed separately.
    # To save time, instead of redoing the entire step with a custom tokenizer,
    # we allow re-using data processed by the StarCoder tokenizer.
    # In this case, we only need to replace the Starcoder special tokens.
    middle = middle.replace("<|pause|>", prompt_formatter.tokenizer.pause_token)
    middle = middle.replace("<|skip|>", prompt_formatter.tokenizer.skip_token)
    middle = middle.replace("<|endoftext|>", prompt_formatter.tokenizer.eod_token)

    target_tokens = prompt_formatter.tokenizer.tokenize(middle)

    target_tokens = target_tokens[: config.max_target_tokens]
    prompt_tokens += target_tokens

    return prompt_tokens

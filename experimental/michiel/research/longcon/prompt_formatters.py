"""Prompt formatter for long context caching experiments."""

import random
from typing import Annotated, Sequence

from megatron.tokenizer.tokenizer import (
    AbstractTokenizer,
    DeepSeekCoderBaseTokenizer,
    StarCoderTokenizer,
)

from base.prompt_format.util import trailing_n
from research.core.abstract_prompt_formatter import <PERSON>bs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from research.core.all_prompt_formatters import register_prompt_formatter
from research.core.model_input import ModelInput
from research.core.prompt_formatters import (
    filter_retrieved_chunks_that_overlap_with_prompt,
)


@register_prompt_formatter("rogue_longcon")
class RogueLongConFormatter(AbstractPromptFormatter):
    """Prompter for Rogue model with stateless caching."""

    # The config used to build the prompt.

    # Budgets of different components
    max_prefix_tokens: int = -1
    "The maximum number of tokens for prefix."

    max_suffix_tokens: int = -1
    "The maximum number of tokens for suffix."

    max_total_near_tokens: int = -1
    "The maximum number of tokens in the nearby, hot context, that we assume is encoded on the fly without any approximations."

    max_far_tokens: int = -1
    "The maximum number of tokens in the far context, which may be cached or approximated in various ways."

    max_noisy_far_tokens: int = 0

    max_filename_tokens: int = 50
    "Truncate filename to this many tokens from left"

    # Order of components
    component_order: Sequence[str] = (
        "far_retrieval",
        "path",
        "near_retrieval",
        "prefix",
        "suffix",
    )
    """Order of components."""

    prepend_bos_token: Annotated[
        bool, "If true, prepend BOS token to the beginning of the prompt."
    ] = False

    def create_default_tokenizer(self) -> AbstractTokenizer:
        return StarCoderTokenizer()

    def prepare_prompt(
        self,
        model_input: ModelInput,
    ) -> tuple[list[int], dict]:
        """Create a simple prompt for the Star Coder model.

        Args:
            input: an instance of ModelInput class, containing all raw input.
            tokenizer: the self.tokenizer.

        Returns:
            A prompt of length at most self.max_prompt_tokens, in tokens.
            A metadata dict containing:
                num_prefix_chars_post_truncation: number of prefix chars post-truncation
                num_suffix_chars_post_truncation: number of suffix chars post-truncation
        """

        metadata = {}

        # Assert tokenizer is StarCoderTokenizer or DeepSeekCoderBaseTokenizerfor typing
        assert isinstance(self.tokenizer, StarCoderTokenizer) or isinstance(
            self.tokenizer, DeepSeekCoderBaseTokenizer
        )

        if self.max_prefix_tokens < 0:
            raise ValueError(
                "max_prefix_tokens must be >= 0, infinite prefix tokens not supported"
            )
        if self.max_suffix_tokens < 0:
            raise ValueError(
                "max_suffix_tokens must be >= 0, infinite suffix tokens not supported"
            )
        if self.max_total_near_tokens < 0:
            raise ValueError(
                "max_total_near_tokens must be >= 0, infinite tokens not supported"
            )

        # Current path
        path_tokens: list[int] = [self.tokenizer.filename_id]
        raw_path_tokens = self.tokenizer.tokenize(model_input.path)
        path_tokens += trailing_n(
            raw_path_tokens, self.max_filename_tokens
        ) + self.tokenizer.tokenize("\n")

        # Construct prefix
        raw_prefix_tokens = self.tokenizer.tokenize(model_input.prefix)
        prefix_start = max(
            0,
            len(raw_prefix_tokens) - self.max_prefix_tokens + 1,  # +1 for fim_prefix
        )
        prefix_tokens: list[int] = [self.tokenizer.fim_prefix_id] + raw_prefix_tokens[
            prefix_start:
        ]

        # Construct suffix
        raw_suffix_tokens = self.tokenizer.tokenize(model_input.suffix)
        suffix_end = min(
            len(raw_suffix_tokens),
            self.max_suffix_tokens - 1,  # -1 for fim_suffix
        )

        suffix_tokens: list[int] = [self.tokenizer.fim_suffix_id] + raw_suffix_tokens[
            :suffix_end
        ]

        num_prefix_chars_post_truncation = len(
            self.tokenizer.detokenize(raw_prefix_tokens[prefix_start:])
        )
        num_suffix_chars_post_truncation = len(
            self.tokenizer.detokenize(raw_suffix_tokens[:suffix_end])
        )
        metadata["num_prefix_chars_post_truncation"] = num_prefix_chars_post_truncation
        metadata["num_suffix_chars_post_truncation"] = num_suffix_chars_post_truncation

        # Clip the budget.
        tokens_used = (
            len(path_tokens)
            + len(prefix_tokens)
            + len(suffix_tokens)
            + 1  # fim_middle token
            + int(self.prepend_bos_token)
        )

        near_retrieval_budget = self.max_total_near_tokens - tokens_used
        assert near_retrieval_budget >= 0

        retrieval_start_tokens = [self.tokenizer.retrieval_section_id]
        near_retrieval_budget -= len(retrieval_start_tokens)

        tokenized_blocks: list[list[int]] = []
        accum = 0
        # We convert the chunks to list here since sometimes it's actually a tuple.
        retrieved_chunks = list(model_input.retrieved_chunks)

        # Filter out retrieved chunks that overlap with ground truth or prefix/suffix.
        retrieved_chunks = filter_retrieved_chunks_that_overlap_with_prompt(
            model_input=model_input,
            num_prefix_chars=num_prefix_chars_post_truncation,
            num_suffix_chars=num_suffix_chars_post_truncation,
        )

        for chunk in retrieved_chunks:
            tokenized_block: list[int] = [self.tokenizer.retrieval_start_id]
            chunk_path = chunk.parent_doc.path
            if chunk_path is not None:
                tokenized_block += [self.tokenizer.filename_id]
                tokenized_block += trailing_n(
                    self.tokenizer.tokenize(chunk_path), self.max_filename_tokens
                )

            tokenized_block += [self.tokenizer.retrieval_body_id]
            tokenized_block += self.tokenizer.tokenize(chunk.text)

            if accum + len(tokenized_block) > near_retrieval_budget:
                break

            tokenized_blocks.append(tokenized_block)
            accum += len(tokenized_block)

        num_nearby_chunks = len(tokenized_blocks)

        # Order chunks such that the most important chunk is at the end of the block,
        # to be closest to the context.
        tokenized_blocks.reverse()

        near_retrieval_tokens: list[int] = []
        near_retrieval_tokens.extend(retrieval_start_tokens)
        for block in tokenized_blocks:
            near_retrieval_tokens.extend(block)

        # Construct far retrieval
        remaining_chunks = retrieved_chunks[num_nearby_chunks:]
        tokenized_blocks: list[list[int]] = []
        accum = 0
        for chunk in remaining_chunks:
            tokenized_block: list[int] = [self.tokenizer.retrieval_start_id]
            chunk_path = chunk.parent_doc.path
            if chunk_path is not None:
                tokenized_block += [self.tokenizer.filename_id]
                tokenized_block += trailing_n(
                    self.tokenizer.tokenize(chunk_path), self.max_filename_tokens
                )

            tokenized_block += [self.tokenizer.retrieval_body_id]
            tokenized_block += self.tokenizer.tokenize(chunk.text)

            if accum + len(tokenized_block) > self.max_far_tokens:
                break

            tokenized_blocks.append(tokenized_block)
            accum += len(tokenized_block)

        num_far_chunks = len(tokenized_blocks)
        num_near_and_far_chunks = num_nearby_chunks + num_far_chunks

        # Add noisy chunks to far retrieval
        remaining_chunks = retrieved_chunks[num_near_and_far_chunks:]

        # Add chunks to far retrieval in reverse order of quality to approximate 'noisy' chunks
        remaining_chunks.reverse()

        accum = 0
        for chunk in remaining_chunks:
            # Negative token to indicate noisy chunk
            tokenized_block: list[int] = [-self.tokenizer.retrieval_start_id]
            chunk_path = chunk.parent_doc.path
            if chunk_path is not None:
                tokenized_block += [self.tokenizer.filename_id]
                tokenized_block += trailing_n(
                    self.tokenizer.tokenize(chunk_path), self.max_filename_tokens
                )

            tokenized_block += [self.tokenizer.retrieval_body_id]
            tokenized_block += self.tokenizer.tokenize(chunk.text)

            if accum + len(tokenized_block) > self.max_noisy_far_tokens:
                break

            tokenized_blocks.append(tokenized_block)
            accum += len(tokenized_block)

        # Randomize far chunk order
        random.seed(
            int.from_bytes(
                (model_input.path + model_input.prefix[-10:]).encode(), "little"
            )
        )
        random.shuffle(tokenized_blocks)

        far_retrieval_tokens: list[int] = []
        for block in tokenized_blocks:
            far_retrieval_tokens.extend(block)

        component_tokens = {
            "path": path_tokens,
            "prefix": prefix_tokens,
            "suffix": suffix_tokens,
            "near_retrieval": near_retrieval_tokens,
            "far_retrieval": far_retrieval_tokens,
        }

        prompt_tokens: list[int] = []
        if self.prepend_bos_token:
            assert self.tokenizer.bos_id is not None
            prompt_tokens.append(self.tokenizer.bos_id)
        for component_name in self.component_order:
            prompt_tokens += component_tokens[component_name]

        # Middle token always at end
        prompt_tokens += [self.tokenizer.fim_middle_id]

        assert (
            len(prompt_tokens) <= self.max_prompt_tokens
        ), f"Returned {len(prompt_tokens)}, max {self.max_prompt_tokens}"

        return prompt_tokens, metadata

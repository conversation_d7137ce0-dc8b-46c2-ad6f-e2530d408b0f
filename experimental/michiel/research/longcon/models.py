"""Models for long context caching experiments."""

from megatron.tokenizer import get_tokenizer

from experimental.michiel.research.longcon.prompt_formatters import (
    RogueLongConFormatter,
)
from research.core.abstract_prompt_formatter import AbstractPromptFormatter
from research.models.llama2_models import LLAMA2Model
from research.models.meta_model import register_model


@register_model("rogue_longcontext")
class RogueLongContext(LLAMA2Model):  # pylint: disable=invalid-name
    """Rogue model with stateless caching."""

    @classmethod
    def create_default_formatter(cls) -> AbstractPromptFormatter:
        return RogueLongConFormatter()

    def load_tokenizer(self):
        """Load tokenizer."""
        tokenizer = get_tokenizer("StarCoderTokenizer")
        # Reset the tokenizer to be the same as the loaded model.
        self.prompt_formatter.rebind(tokenizer=tokenizer)  # type: ignore
        return tokenizer

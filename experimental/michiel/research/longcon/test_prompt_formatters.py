"""Test prompt formatters for long context caching experiments."""

import unittest
from textwrap import dedent

from megatron.tokenizer.tokenizer import StarCoderTokenizer

from experimental.michiel.research.longcon.prompt_formatters import (
    RogueLongConFormatter,
)
from research.core.model_input import Chunk, Document, ModelInput


def get_example_01():
    doc = Document(
        id="doc1", text="This is a fake doc.", path="example_doc.py", meta={}
    )
    return ModelInput(
        path="src/example.py",
        prefix="def aggregate(a,b):\n",
        suffix="\nreturn aggregated_output\n",
        retrieved_chunks=[
            Chunk(
                id="chunk1",
                text="# You can aggregate\n# with a pooling function.\n",
                parent_doc=doc,
                char_offset=93,
                length=49,
                line_offset=1,
                length_in_lines=2,
                meta={},
            ),
            Chunk(
                id="chunk2",
                text="# You can aggregate\n# with a maxing\n# function.\n",
                parent_doc=doc,
                char_offset=54,
                length=51,
                line_offset=5,
                length_in_lines=3,
                meta={},
            ),
            Chunk(
                id="chunk3",
                text="# This is a garbage chunk.\n",
                parent_doc=doc,
                char_offset=54,
                length=51,
                line_offset=5,
                length_in_lines=3,
                meta={},
            ),
        ],
    )


class TestRogueLongConFormatter(unittest.TestCase):
    """Test the Rogue formatter for long context experiments."""

    def setUp(self):
        self.prompter = RogueLongConFormatter(
            max_prefix_tokens=20,
            max_suffix_tokens=30,
            max_total_near_tokens=60,
            max_far_tokens=20,
        )
        self.inputs = get_example_01()

    def test_prompt_formatter_defaults(self):
        prompt, _ = self.prompter.prepare_prompt_text(self.inputs)
        expected = dedent(
            """\
            <|ret-start|><filename>example_doc.py<|ret-body|># You can aggregate
            # with a maxing
            # function.
            <filename>src/example.py
            <|retrieval_section|><|ret-start|><filename>example_doc.py<|ret-body|># You can aggregate
            # with a pooling function.
            <fim_prefix>def aggregate(a,b):
            <fim_suffix>
            return aggregated_output
            <fim_middle>"""
        )
        self.assertEqual(prompt, expected)

    def test_noisy_chunks(self):
        self.prompter.rebind(max_noisy_far_tokens=100)
        prompt_tokens, _ = self.prompter.prepare_prompt(self.inputs)

        assert isinstance(self.prompter.tokenizer, StarCoderTokenizer)
        self.assertEqual(prompt_tokens[0], -self.prompter.tokenizer.retrieval_start_id)
        positive_prompt_tokens = [abs(tok) for tok in prompt_tokens]
        prompt = self.prompter.tokenizer.detokenize(positive_prompt_tokens)
        expected = dedent(
            """\
            <|ret-start|><filename>example_doc.py<|ret-body|># This is a garbage chunk.
            <|ret-start|><filename>example_doc.py<|ret-body|># You can aggregate
            # with a maxing
            # function.
            <filename>src/example.py
            <|retrieval_section|><|ret-start|><filename>example_doc.py<|ret-body|># You can aggregate
            # with a pooling function.
            <fim_prefix>def aggregate(a,b):
            <fim_suffix>
            return aggregated_output
            <fim_middle>"""
        )
        self.assertEqual(prompt, expected)

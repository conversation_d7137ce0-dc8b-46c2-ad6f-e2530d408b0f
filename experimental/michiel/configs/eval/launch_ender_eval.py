"""Launch evals for the ender system."""

import argparse

from experimental.michiel.configs.eval.execute_eval import execute_eval
from experimental.michiel.configs.eval.model_configs import model_config_dict
from experimental.michiel.configs.eval.retriever_configs import retriever_config_dict


def main(args):
    system_config = model_config_dict[args.model]
    model_config = system_config["model"]
    retriever_config = retriever_config_dict[args.retriever]

    system_config = {
        "name": "ender_sys",
        "model": system_config["model"],
        "generation_options": {"max_generated_tokens": 280},
        "dense_retriever": retriever_config,
        "signature_index": system_config["signature_index"],
        "sig_prompt_formatter": system_config["sig_prompt_formatter"],
        "fim_mode": "evaluation",
        "verbose": False,
    }

    tasks = {
        "multilang": {
            "name": "hydra",
            "dataset": "all_languages_2-3lines_medium_to_hard.v1.0",
            "hydra_block_resource_internet_access": True,
        },
        "cceval": {"name": "cceval"},
        "functions": {"name": "hydra", "dataset": "repoeval_functions"},
        "api": {"name": "api", "dataset": "finegrained-python.large"},
        "23lines": {"name": "hydra", "dataset": "repoeval_2-3lines"},
    }
    for key, task_config in tasks.items():
        if args.tasks and key not in args.tasks:
            continue
        if task_config["name"] == "cceval":
            pod_spec = "1xA100.yaml"
        else:
            pod_spec = "A40.yaml"

        if task_config["name"] == "functions":
            system_config["experimental"]["trim_on_dedent"] = True

        task_name = (
            task_config["dataset"] if "dataset" in task_config else task_config["name"]
        )
        checkpoint_path = model_config["model_path"]
        retriever_name = args.retriever
        exp_name = args.exp_name

        determined_name = (
            f"{task_name}, {checkpoint_path}, {retriever_name}, {exp_name}"
        )

        execute_eval(
            determined_name=determined_name,
            system_config=system_config,
            task_config=task_config,
            pod_spec=pod_spec,
        )


if __name__ == "__main__":
    # Parse args
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--model",
        "-m",
        type=str,
        help="The key in the model dict.",
    )
    parser.add_argument(
        "--retriever",
        "-r",
        type=str,
        help="The key in the retriever dict.",
    )
    parser.add_argument(
        "--signature",
        "-s",
        type=str,
        help="The key in the signature dict.",
    )
    parser.add_argument(
        "--exp_name",
        "-e",
        type=str,
        default="",
        help="The experiment name.",
    )
    parser.add_argument(
        "--tasks",
        "-t",
        nargs="+",
        help="List of specific task names to run.",
    )
    parsed_args = parser.parse_args()

    main(parsed_args)

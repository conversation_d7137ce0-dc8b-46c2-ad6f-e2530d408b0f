config = {
    "scorer": {
        "name": "dense_scorer_v2_fbwd",
        "checkpoint_path": "/mnt/efs/augment/user/tongfei/hm/chatanol/consolidate_mp_model/InitCkpt=ethanol-qwen25coder-1b5&Tokenizer=qwen25coder/out",
    },
    "chunker": {
        "name": "smart_line_level",
        "max_chunk_chars": 768,
        "max_headers": 3,
    },
    "query_formatter": {
        "name": "base:chatanol6-singleturnisspecial",
        "tokenizer_name": "qwen25coder",
        "max_tokens": 1024,
    },
    "document_formatter": {
        "name": "base:ethanol6-embedding-with-path-key",
        "tokenizer_name": "qwen25coder",
    },
}

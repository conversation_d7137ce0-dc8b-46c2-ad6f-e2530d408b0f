"""Configuration for the Ethanol6 version 16 retriever."""

config = {
    "scorer": {
        "name": "dense_scorer_v2_fbwd",
        "checkpoint_path": "/mnt/efs/augment/checkpoints/michiel/retriever/stareth_repro",
    },
    "chunker": {
        "name": "line_level",
        "max_lines_per_chunk": 30,
    },
    "query_formatter": {
        "name": "base:ethanol6.16.1-query-embedding",
        "max_tokens": 1023,
        "tokenizer": "starcoder",
    },
    "document_formatter": {
        "name": "base:ethanol6-embedding-with-path-key",
        "max_tokens": 999,
        "tokenizer": "starcoder",
    },
}

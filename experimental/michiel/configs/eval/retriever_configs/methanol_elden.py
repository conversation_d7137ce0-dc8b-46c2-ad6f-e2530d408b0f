"""Configuration for the Methanol retriever."""

config = {
    "scorer": {
        "name": "dense_scorer_v2_fbwd",
        "checkpoint_path": "/mnt/efs/augment/checkpoints/michiel/retriever/methanol/starmeth_500s_128docactual_fix",
    },
    "chunker": {
        "name": "signature",
    },
    "query_formatter": {
        "name": "base:ethanol6.16.1-query-embedding",
        "max_tokens": 1023,
        "tokenizer": "starcoder",
    },
    "document_formatter": {
        "name": "base:ethanol6-embedding-with-path-key",
        "max_tokens": 999,
        "tokenizer": "starcoder",
    },
}

"""Configuration for the Methanol retriever."""

config = {
    "chunker": {"name": "signature"},
    "document_formatter": {
        "add_path": False,
        "name": "simple_document",
        "tokenizer_name": "StarCoderTokenizer",
        "max_tokens": 999,
    },
    "query_formatter": {
        "add_path": True,
        "add_suffix": True,
        "max_lines": -1,
        "max_tokens": 1023,
        "name": "ethanol6_query",
        "tokenizer_name": "StarCoderTokenizer",
    },
    "scorer": {
        "checkpoint_path": "/mnt/efs/augment/checkpoints/menthol/methanol_0416.4_1250/global_step1250/",
        "name": "dense_scorer_v2_fbwd_neox",
    },
}

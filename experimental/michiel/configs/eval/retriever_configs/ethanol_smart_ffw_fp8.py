"""Configuration for the Ethanol6 version 16 retriever in fp8."""

config = {
    "scorer": {
        "name": "dense_scorer_ffwd_starcoder_fp8",
        "checkpoint_path": "/mnt/efs/augment/checkpoints/ethanol/smart/stareth_smart_128doc_2000s/global_step2000-ckpt_v2-fp8",
        "checkpoint_sha256": "ea80339a0fa868f1e474c9c92b919360e0c0dfbd3e6566d72258ce3b06e7a019",
        "output_projection_dim": 512,
    },
    "chunker": {
        "name": "smart_line_level",
        "max_chunk_chars": 768,
        "max_headers": 3,
    },
    "query_formatter": {
        "name": "base:ethanol6.16.1-query-embedding",
        "max_tokens": 1023,
        "tokenizer": "starcoder",
    },
    "document_formatter": {
        "name": "base:ethanol6-embedding-with-path-key",
        "max_tokens": 999,
        "tokenizer": "starcoder",
    },
}

from experimental.michiel.configs.eval.retriever_configs import (
    chatanol,
    chatanol_smart,
    chatanol_smart_path,
    chatanol_qwen_smart,
    ethanol604,
    ethanol616,
    ethanol616_fb,
    ethanol616_ffw,
    ethanol_qwen,
    ethanol_smart_ffw_fp8,
    ethanol_smart_ffw,
    ethanolfb_smart,
    methanol0416,
    methanol0416_ffw,
    methanol_0416_ffw_fp8,
)

retriever_config_dict = {
    "chatanol": chatanol.config,
    "chatanol_smart": chatanol_smart.config,
    "chatanol_smart_path": chatanol_smart_path.config,
    "chatanol_qwen_smart": chatanol_qwen_smart.config,
    "ethanol604": ethanol604.config,
    "ethanol616": ethanol616.config,
    "ethanol616_fb": ethanol616_fb.config,
    "ethanol616_ffw": ethanol616_ffw.config,
    "ethanolfb_smart": ethanolfb_smart.config,
    "ethanol_qwen": ethanol_qwen.config,
    "ethanol_smart_ffw_fp8": ethanol_smart_ffw_fp8.config,
    "ethanol_smart_ffw": ethanol_smart_ffw.config,
    "methanol0416": methanol0416.config,
    "methanol0416_ffw": methanol0416_ffw.config,
    "methanol_0416_ffw_fp8": methanol_0416_ffw_fp8.config,
}

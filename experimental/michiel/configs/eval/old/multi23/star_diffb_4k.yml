#
# This file contains an example of an evaluation config
#

# Sections:
#   Systems - specify the system configuration to evaluate
#   Tasks - specify the evaluation tasks for each system
#   Podspec - overide the default podspec, if necessary
#   Determined - name, workspace, project in the determined UI.

systems:
  - name: basic_rag
    model:
      name: starcoderbase
      checkpoint_path: arun/starcoderfima-16b-mp1
      prompt:
        max_prefix_tokens: 1280
        max_suffix_tokens: 768
        max_retrieved_chunk_tokens: 0
        max_prompt_tokens: 3816
        retrieval_layout_style: comment2
    generation_options:
      temperature: 0
      top_k: 0
      top_p: 0
      max_generated_tokens: 280
    retriever:
      name: diff_boykin
      chunker: line_level
      max_chunk: 40
      max_query_lines: 20
    experimental:
      retriever_top_k: 25
      trim_on_dedent: False
      trim_on_max_lines: null
      remove_suffix: False



# Tasks
#   specify the evaluation tasks for each checkpoint
#
task:
  dataset: all_languages_2-3lines_medium_to_hard.v1.0
  name: hydra
  hydra_block_resource_internet_access: True

# Podspec - set the default podspec for all checkpoints
# See gpt-neox/jobs/templates/podspecs/ for additional options
# Use the following for small models (<=2B)
# podspec: gpu-small.yaml
# Use the following for larger models (>=2B)
podspec: A40.yaml

# Determined
# name, workspace, project control location and display in the determined UI.
#
# IF YOU DO NOT WANT TO EXECUTE CODE:
# To disable execution through hydra, choose the batch-eval.yaml below

determined:
  name: multi23, starcoderfima-16b-mp1 noret
  workspace: Dev
  project: Eval
  # relative to research/gpt-neox
  # metaconfig: jobs/templates/batch-eval.yaml
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml

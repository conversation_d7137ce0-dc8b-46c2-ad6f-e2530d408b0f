#
# This file contains an example of an evaluation config
#

# Sections:
#   Systems - specify the system configuration to evaluate
#   Tasks - specify the evaluation tasks for each system
#   Podspec - overide the default podspec, if necessary
#   Determined - name, workspace, project in the determined UI.

system:
  name: basic_rag
  model:
    name: rogue
    checkpoint_path: rogue/16b_rogue_ethanol51m_addcpp_fix
    prompt:
      max_prefix_tokens: 1280
      max_suffix_tokens: 768
      max_retrieved_chunk_tokens: -1
      max_prompt_tokens: 3816
      always_use_suffix_token: True
      only_truncate_true_prefix: True
  generation_options:
    temperature: 0
    top_k: 0
    top_p: 0
    max_generated_tokens: 280
  retriever:
    scorer:
      name: ethanol
      checkpoint_path: ethanol/ethanol6-04.1
    chunker:
      name: line_level
      max_lines_per_chunk: 30
    query_formatter:
      name: ethanol6_query
      max_tokens: 1023
      add_path: true
    document_formatter:
      name: ethanol6_document
      max_tokens: 999
      add_path: true
  experimental:
    retriever_top_k: 25
    trim_on_dedent: False
    trim_on_max_lines: null
    remove_suffix: False



# Tasks
#   specify the evaluation tasks for each checkpoint
#
task:
  dataset: all_languages_2-3lines_medium_to_hard.v1.0
  name: hydra
  hydra_block_resource_internet_access: True
  # limit: 10

# Podspec - set the default podspec for all checkpoints
# See gpt-neox/jobs/templates/podspecs/ for additional options
# Use the following for small models (<=2B)
# podspec: gpu-small.yaml
# Use the following for larger models (>=2B)
podspec: A40.yaml

# Determined
# name, workspace, project control location and display in the determined UI.
#
# IF YOU DO NOT WANT TO EXECUTE CODE:
# To disable execution through hydra, choose the batch-eval.yaml below

determined:
  name: multi23, 16b_rogue_ethanol51m_addcpp_fix
  workspace: Dev
  project: Eval
  # relative to research/gpt-neox
  # metaconfig: jobs/templates/batch-eval.yaml
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml
import_modules:
  experimental.igor.systems.ethanol

#
# This file contains an example of an evaluation config
#

# Sections:
#   Systems - specify the system configuration to evaluate
#   Tasks - specify the evaluation tasks for each system
#   Podspec - overide the default podspec, if necessary
#   Determined - name, workspace, project in the determined UI.

system:
  name: ender_sys
  model:
    name: ender_fastforward
    model_path: ender/16b_multieth61m_notodo_noinline_5knosig
    prompt:
      max_prefix_tokens: 1280
      max_suffix_tokens: 768
      max_signature_tokens: 0
      max_prompt_tokens: 5120
      max_retrieved_chunk_tokens: -1
  generation_options:
    temperature: 0
    top_k: 0
    top_p: 0
    max_generated_tokens: 280
  dense_retriever:
    scorer:
      name: ethanol
      checkpoint_path: ethanol/ethanol6-04.1
    chunker:
      name: line_level
      max_lines_per_chunk: 30
    query_formatter:
      name: ethanol6_query
      max_tokens: 1023
      add_path: true
    document_formatter:
      name: ethanol6_document
      max_tokens: 999
      add_path: true
  signature_index:
      est_prefix_chars: 3840
      est_suffix_chars: 2304
      max_ctx_signature_chars: 3000
  sig_prompt_formatter:
      max_middle_tks: 1024
  fim_mode: evaluation
  verbose: False



# Tasks
#   specify the evaluation tasks for each checkpoint
#
task:
  dataset: all_languages_2-3lines_medium_to_hard.v1.0
  name: hydra
  hydra_block_resource_internet_access: True
  # limit: 10

# Podspec - set the default podspec for all checkpoints
# See gpt-neox/jobs/templates/podspecs/ for additional options
# Use the following for small models (<=2B)
# podspec: gpu-small.yaml
# Use the following for larger models (>=2B)
podspec: A40.yaml

# Determined
# name, workspace, project control location and display in the determined UI.
#
# IF YOU DO NOT WANT TO EXECUTE CODE:
# To disable execution through hydra, choose the batch-eval.yaml below

determined:
  name: multi23, 16b_multieth61m_notodo_noinline_5knosig
  workspace: Dev
  project: Eval
  # relative to research/gpt-neox
  # metaconfig: jobs/templates/batch-eval.yaml
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml
import_modules:
  experimental.igor.systems.ethanol

"""<PERSON><PERSON><PERSON> (really <PERSON><PERSON><PERSON>'s) utils for submiting eval configs."""

import os
from pathlib import Path
from typing import Optional

import yaml

from research.eval.eval import __file__ as eval_file
from research.utils.inspect_indexed_dataset import print_green


def execute_eval(
    determined_name: str,
    system_config: dict,
    task_config: dict,
    pod_spec: str,
    determined_overrides: Optional[dict] = None,
    additional_overrides: Optional[dict] = None,
    determined_checkpoint_id: Optional[str] = None,
    local: bool = False,
    cluster: str = "GCP-US1",
):
    all_config = {
        "system": system_config,
        "task": task_config,
        "podspec": pod_spec,
        "determined": {
            "name": determined_name,
            "workspace": "Dev",
            "project": "michiel-eval",
            "metaconfig": "jobs/templates/eval-exec-v2-metaconfig.yaml",
        },
    }
    all_config["determined"].update(determined_overrides or {})
    all_config.update(additional_overrides or {})

    # convert the config to yaml string
    config_str = yaml.dump(all_config, indent=2)
    print_green("Launching eval with the following config:")
    print(config_str)
    config_path = Path("/tmp/current_eval_config.yml")
    config_path.write_text(config_str)
    cmd = f"python3 {eval_file} --skip_bazel {config_path} --cluster {cluster}"
    if determined_checkpoint_id is not None:
        cmd += f" --checkpoint={determined_checkpoint_id}"
    if local:
        cmd += " --local"
    print_green(f"Executing command: {cmd}")
    os.system(cmd)

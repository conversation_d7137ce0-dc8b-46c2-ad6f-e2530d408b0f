"""Launch evals for the basic rag system."""

import argparse

from experimental.michiel.configs.eval.execute_eval import execute_eval
from experimental.michiel.configs.eval.model_configs import model_config_dict
from experimental.michiel.configs.eval.retriever_configs import retriever_config_dict


def main(args):
    model_config = model_config_dict[args.model]
    retriever_config = retriever_config_dict[args.retriever]

    system_config = {
        "name": "basic_rag",
        "model": model_config,
        "generation_options": {"max_generated_tokens": 280},
        "retriever": retriever_config,
        "experimental": {
            "remove_suffix": False,
            "trim_on_dedent": False,
            "retriever_top_k": 25,
            "fim_gen_mode": "evaluation",
        },
    }

    num_gpus = 1
    if "model_parallel_size" in model_config:
        num_gpus = model_config["model_parallel_size"]

    tasks = {
        "multilang": {
            "name": "hydra",
            "dataset": "all_languages_2-3lines_medium_to_hard.v1.0",
            "hydra_block_resource_internet_access": True,
        },
        "cceval": {"name": "cceval"},
        "functions": {"name": "hydra", "dataset": "repoeval_functions"},
        "23lines": {"name": "hydra", "dataset": "repoeval_2-3lines"},
        "api": {"name": "api", "dataset": "finegrained-python.large"},
        "hindsight": {
            "name": "hindsight",
            "dataset": "2024-05-01-v1.0",
            "tenant_name": "dogfood",
            "service_account_file": "/mnt/augment/secrets/cw-ri-importer/cw-ri-importer.json",
            "blob_limit": 9000,
            # "limit": 10,
        },
    }

    for key, task_config in tasks.items():
        if args.tasks and key not in args.tasks:
            continue

        if num_gpus > 1:
            pod_spec = f"{num_gpus}xA100.yaml"
        elif task_config["name"] == "cceval":
            pod_spec = "1xA100.yaml"
        else:
            pod_spec = "A40.yaml"

        if task_config["name"] == "functions":
            system_config["experimental"]["trim_on_dedent"] = True

        task_name = (
            task_config["dataset"] if "dataset" in task_config else task_config["name"]
        )
        determined_checkpoint_id = None
        if "checkpoint" in model_config:
            checkpoint_name = model_config["checkpoint"]
            determined_checkpoint_id = model_config["checkpoint"]
        elif "checkpoint_path" in model_config:
            checkpoint_name = model_config["checkpoint_path"]
        elif "model_path" in model_config:
            checkpoint_name = model_config["model_path"]
        else:
            raise ValueError("No checkpoint path found.")
        retriever_name = args.retriever
        exp_name = args.exp_name

        determined_name = (
            f"{task_name}, {checkpoint_name}, {retriever_name}, {exp_name}"
        )

        augment_args = {
            "gpu_count": num_gpus,
            "dai_gcp_service_accounts": [
                {
                    "secret": "aug-prod-cw-ri-importer",  # pragma: allowlist secret
                    "mountpoint": "/mnt/augment/secrets/cw-ri-importer",
                }
            ],
        }
        execute_eval(
            determined_name=determined_name,
            system_config=system_config,
            task_config=task_config,
            pod_spec=pod_spec,
            determined_checkpoint_id=determined_checkpoint_id,
            additional_overrides={"augment": augment_args},
        )


if __name__ == "__main__":
    # Parse args
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--model",
        "-m",
        type=str,
        help="The key in the model dict.",
    )
    parser.add_argument(
        "--retriever",
        "-r",
        type=str,
        help="The key in the retriever dict.",
    )
    parser.add_argument(
        "--exp_name",
        "-e",
        type=str,
        default="",
        help="The experiment name.",
    )
    parser.add_argument(
        "--tasks",
        "-t",
        nargs="+",
        default=["multilang", "cceval", "functions", "23lines", "api"],
        help="List of specific task names to run.",
    )
    parsed_args = parser.parse_args()

    main(parsed_args)

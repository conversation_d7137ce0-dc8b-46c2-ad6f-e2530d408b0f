"""Configuration for roguesl system with 2k context, using nearby prefix."""

config = {
    "name": "fastbackward_starcoder",
    "checkpoint_path": "roguesl/roguesl_eth6_4m_morelang3_fprefretnpref250suf_aug05prompt3k8krdrop03_fb",
    "model_parallel_size": 2,
    "seq_length": 6400,
    "override_prompt_formatter": {
        "name": "rogue_statelesscache",
        "max_prefix_tokens": 1030,
        "max_suffix_tokens": 768,
        "max_retrieved_chunk_tokens": -1,
        # "max_prompt_tokens": 3816,
        # "max_prompt_tokens": 4456,
        "max_prompt_tokens": 6144,
        # "component_order": [
        #     "prefix",
        #     "suffix",
        #     "retrieval",
        #     "nearby_prefix",
        # ],
        "component_order": [
            "prefix",
            "retrieval",
            "nearby_prefix",
            "suffix",
        ],
        "context_quant_token_len": 50,
        "nearby_prefix_token_len": 250,
        "nearby_prefix_token_overlap": 0,
        "nearby_suffix_token_len": 0,
        "nearby_suffix_token_overlap": 0,
        "use_far_prefix_token": True,
    },
}

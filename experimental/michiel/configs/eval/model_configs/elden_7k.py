"""Configuration for roguesl system with 2k context, using nearby prefix."""

config = {
    "name": "elden_fb",
    "checkpoint_path": "star2/elden_0619_90k_0619_sc2_pfretsignpfsuf_rdrop030_1k5b6kstep",
    "model_parallel_size": 2,
    "seq_length": 7600,
    "prompt": {
        "max_prefix_tokens": 1024,
        "max_suffix_tokens": 512,
        "max_signature_tokens": 2024,
        "max_retrieved_chunk_tokens": -1,
        "max_prompt_tokens": 7144,
        "component_order": [
            "prefix",
            "retrieval",
            "signature",
            "nearby_prefix",
            "suffix",
        ],
        "context_quant_token_len": 64,
        "nearby_prefix_token_len": 512,
        "nearby_prefix_token_overlap": 0,
        "nearby_suffix_token_len": 0,
        "nearby_suffix_token_overlap": 0,
    },
}

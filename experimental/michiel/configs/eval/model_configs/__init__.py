from experimental.michiel.configs.eval.model_configs import (
    deepseekfar_6k,
    elden_6k,
    elden_6k_ffw,
    ender_2kcon1ksigrdense,
    ender_2kconrdense,
    endersl_2kcon1ksigrdense,
    llama3_4k,
    qwen_7b_elden_6k,
    qwen_14b_elden,
    qwen_32b_elden,
    roguesl_4k,
    roguesl_4k7_nsuf,
    roguesl_4k_fastforward,
    roguesl_plain_4k,
    star2far_4k,
    star2far_6k,
    star2ffw_6k,
    star2plain_4k,
    star2plain_6k,
    star2sl_4k,
    star2sl_6k,
    starplain_4k,
    starplain_6k,
    starsl_4k,
    starsl_6k,
)

model_config_dict = {
    "deepseekfar_6k": deepseekfar_6k.config,
    "elden_6k": elden_6k.config,
    "elden_6k_ffw": elden_6k_ffw.config,
    "roguesl_4k": roguesl_4k.config,
    "roguesl_4k_fastforward": roguesl_4k_fastforward.config,
    "roguesl_plain_4k": roguesl_plain_4k.config,
    "roguesl_4k7_nsuf": roguesl_4k7_nsuf.config,
    "ender_2kcon1ksigrdense": ender_2kcon1ksigrdense.config,
    "ender_2kconrdense": ender_2kconrdense.config,
    "starplain_4k": starplain_4k.config,
    "starplain_6k": starplain_6k.config,
    "endersl_2kcon1ksigrdense": endersl_2kcon1ksigrdense.config,
    "llama3_4k": llama3_4k.config,
    "starsl_4k": starsl_4k.config,
    "starsl_6k": starsl_6k.config,
    "star2far_4k": star2far_4k.config,
    "star2far_6k": star2far_6k.config,
    "star2sl_4k": star2sl_4k.config,
    "star2sl_6k": star2sl_6k.config,
    "star2plain_4k": star2plain_4k.config,
    "star2plain_6k": star2plain_6k.config,
    "star2ffw_6k": star2ffw_6k.config,
    "qwen_7b_elden_6k": qwen_7b_elden_6k.config,
    "qwen_14b_elden": qwen_14b_elden.config,
    "qwen_32b_elden": qwen_32b_elden.config,
}

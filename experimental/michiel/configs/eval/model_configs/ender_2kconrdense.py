"""Configuration for ender system with 2k context and rest dense."""

config = {
    "model": {
        "name": "ender_fastforward",
        "model_path": "ender/16b_ender_multieth64m_fullsig_nosig_b1ks4k",
        "prompt": {
            "max_prefix_tokens": 1280,
            "max_suffix_tokens": 768,
            "max_signature_tokens": 0,
            "max_prompt_tokens": 5120,
            "max_retrieved_chunk_tokens": -1,
            "component_order": ["signature", "retrieval", "prefix", "suffix"],
        },
    },
    "signature_index": {
        "est_prefix_chars": 3840,
        "est_suffix_chars": 2304,
        "max_ctx_signature_chars": 3000,
    },
    "sig_prompt_formatter": {
        "max_middle_tks": 1024,
    },
}

"""Launch evals for the Elden system."""

import argparse

from experimental.michiel.configs.eval.agent_configs import agent_config_dict
from experimental.michiel.configs.eval.execute_eval import execute_eval
from experimental.michiel.configs.eval.retriever_configs import retriever_config_dict


def main(args):
    system_config = agent_config_dict[args.agent_config]
    dense_retriever_config = retriever_config_dict[args.dense_retriever]

    system_config["retriever_config"] = dense_retriever_config

    tasks = {
        "augmentqa_v3": {
            "name": "augment_qa",
            "dataset_path": "/mnt/efs/augment/data/processed/augment_qa/v3",
            "html_report_output_dir": "/mnt/efs/augment/public_html/michiel/augmentqa_v3",
        },
        "augmentqa_v3_1": {
            "name": "augment_qa",
            "dataset_path": "/mnt/efs/augment/data/processed/augment_qa/v3_1",
            "html_report_output_dir": "/mnt/efs/augment/public_html/michiel/augmentqa_v3_1",
        },
        "modal_v0": {
            "name": "augment_qa",
            "dataset_path": "/mnt/efs/augment/data/processed/modal_labs_v0",
            "html_report_output_dir": "/mnt/efs/augment/public_html/michiel/modal_v0",
        },
    }

    for key, task_config in tasks.items():
        if args.tasks and key not in args.tasks:
            continue

        if args.num_gpus > 1:
            if args.num_gpus > 8:
                raise ValueError("Too many GPUs requested")
            pod_spec = f"{args.num_gpus}xH100.yaml"
        else:
            pod_spec = "1xH100.yaml"
        pod_spec = f"{args.num_gpus}xH100.yaml"

        task_name = (
            task_config["dataset"] if "dataset" in task_config else task_config["name"]
        )
        determined_checkpoint_id = None

        retriever_name = args.dense_retriever
        exp_name = args.exp_name

        determined_name = (
            f"{task_name}, {exp_name}, {args.agent_config}, {retriever_name}"
        )

        augment_args = {
            "gpu_count": args.num_gpus,
        }
        execute_eval(
            determined_name=determined_name,
            system_config=system_config,
            task_config=task_config,
            pod_spec=pod_spec,
            determined_checkpoint_id=determined_checkpoint_id,
            additional_overrides={
                "augment": augment_args,
                "import_modules": [
                    "experimental.michiel.research.agentqa.question_agent_system",
                    "experimental.michiel.research.agentqa.tool_agent_system",
                ],
            },
            local=args.local,
            cluster=args.cluster,
        )


if __name__ == "__main__":
    # Parse args
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--agent-config",
        "-a",
        type=str,
        help="The key in the agent config dict.",
    )
    parser.add_argument(
        "--dense-retriever",
        "-d",
        type=str,
        help="Dense key in the retriever dict.",
    )
    parser.add_argument(
        "--exp_name",
        "-e",
        type=str,
        default="",
        help="The experiment name.",
    )
    parser.add_argument(
        "--tasks",
        "-t",
        nargs="+",
        default=["augmentqa_v3"],
        help="List of specific task names to run.",
    )
    parser.add_argument(
        "--local",
        "-l",
        action="store_true",
        default=False,
        help="Run evaluations outside determined.ai cluster",
    )
    parser.add_argument(
        "--cluster",
        "-c",
        type=str,
        default="GCP-US1",
        help="The cluster to run the experiment on.",
    )
    parser.add_argument(
        "--num_gpus",
        "-ng",
        type=int,
        default=1,
        help="The number of GPUs to use.",
    )
    parsed_args = parser.parse_args()

    main(parsed_args)

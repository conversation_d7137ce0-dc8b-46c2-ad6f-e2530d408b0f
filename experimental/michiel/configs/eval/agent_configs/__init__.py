from experimental.michiel.configs.eval.agent_configs.agent_function import (
    baseline_single,
    haikuv2,
    o1preview,
    o1preview_single,
    sonnetv2,
)
from experimental.michiel.configs.eval.agent_configs.llmtool import (
    o3_mini_single,
    sonnet37_iters,
    sonnet37_single,
    sonnet37_qwen_qr,
    sonnetv2_iters,
    sonnetv2_single,
)

agent_config_dict = {
    "baseline_single": baseline_single.config,
    "sonnetv2": sonnetv2.config,
    "haikuv2": haikuv2.config,
    "o1preview": o1preview.config,
    "o1preview_single": o1preview_single.config,
    "sonnetv2_single": sonnetv2_single.config,
    "sonnetv2_iters": sonnetv2_iters.config,
    "o3_mini_single": o3_mini_single.config,
    "sonnet37_single": sonnet37_single.config,
    "sonnet37_iters": sonnet37_iters.config,
    "sonnet37_qwen_qr": sonnet37_qwen_qr.config,
}

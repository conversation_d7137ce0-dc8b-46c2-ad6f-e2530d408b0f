name: ender_sys
model:
  name: ender_fastforward
  model_path: ender/16b_pydiffb1m_inline_fix_drop_2kcon_1ksig_rdense
  prompt:
    max_prefix_tokens: 1280
    max_suffix_tokens: 768
    max_signature_tokens: 1024
    max_prompt_tokens: 4096
    max_retrieved_chunk_tokens: -1
generation_options:
  temperature: 0
  top_k: 0
  top_p: 0
  max_generated_tokens: 50
dense_retriever:
# Note: must be run with --import_modules experimental.igor.systems.ethanol
  chunker:
    max_lines_per_chunk: 30
    name: line_level
  document_formatter:
    add_path: true
    name: simple_document
  query_formatter:
    add_path: true
    max_tokens: 1023
    name: ethanol3_query
    retokenize: true
  scorer:
    checkpoint_path: ethanol/ethanol5-01
    name: ethanol
signature_index:
    use_middle_signatures: true
fim_mode: interactive
verbose: True

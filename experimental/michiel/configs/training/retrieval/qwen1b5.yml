determined:
  description: null
  workspace: Dev
  project: michiel

augment:
  podspec_path: "8xH100.yaml"
  gpu_count: 32
  project_group: finetuning
  keep_last_n_checkpoints: 1

fastbackward_args:
  run_name: qeth1b_2000s_128doc_smart_logit0005
  # Uncomment these if you want to log to wandb.
  wandb_project: ethanol

  components:
    model:
      component_name: create_dual_encoder_with_tokenizer
      query_model: query_model
      doc_model: query_model
      tokenizer: tokenizer
    query_model:
      component_name: create_embedder_with_language_model
      language_model: query_model_lm
      output_projection_dim: 512
      with_output_bias: True
    query_model_lm:
      component_name: checkpointing.load_transformer_checkpoint
      checkpoint_path: /mnt/efs/augment/checkpoints/qwen25-coder/1b5-fb/
      skip_output: True
    loss_fn:
      component_name: PerplexityLoss
      config:
        # from /home/<USER>/augment/experimental/igor/experiments/2023-09-13_ppl_distill/training/configs/temp_gold_score_0.01.yml
        gold_temperature: 0.01
        # from /home/<USER>/augment/experimental/igor/experiments/2023-09-13_ppl_distill/training/configs/temp_score_10.yml
        pred_temperature: 10.0
        # from /home/<USER>/augment/experimental/igor/experiments/2023-09-13_ppl_distill/training/configs/init_scale_-4.yml
        logits_scale: 0.005
        learnable_logits_scale: False
        reduction: mean

  eval_interval: 100
  checkpoint_interval: 100

  max_epochs: -1
  train_options:
    # Original configuration had batch size x grad acc. x gpus of 1 x 16 x 32.
    batch_size: 1
    gradient_accumulation_steps: 16
    max_iters: 2000
    log_interval: 50
    grad_clip: 1.0

    # from: /home/<USER>/augment/experimental/igor/experiments/2023-09-13_ppl_distill/training/configs/2e-5.yml
    optimizer:
      warmup_iters: 0
      learning_rate: 2.0e-5
      min_lr: 2.0e-6
      # These are the default betas, but `launch.py` doesn't handle lists yet.
      # betas: [0.9, 0.95]
      eps: 1.0e-8
      weight_decay: 0.1

  # Turns out we can fit a large eval batch, so why not.
  eval_batch_size: 1

  train_data:
    # path: /mnt/efs/augment/user/arun/data/starethanol6/ethanol6_16.1_mean_doc/dataset
    path: /mnt/efs/spark-data/shared/ethanol/qwen/indexed_dataset/dataset
    tokenizer_name: qwen25coder
    documents_per_batch: 128
    max_query_tokens: 1024
    max_document_tokens: 1024
  eval_data:
    path: /mnt/efs/spark-data/shared/ethanol/qwen/indexed_dataset/validation_dataset
    tokenizer_name: qwen25coder
    limit: 1024
    documents_per_batch: 128
    max_query_tokens: 1024
    max_document_tokens: 1024

includes:
- augment_configs/starcoder/model/starcoder.yml
- augment_configs/starcoder/model/starcoder-16b.yml

determined:
  name: &xname 16b_roguesl_prefsufret_nearsuf100olap20_nearpref100olap20_quant20
  description: null
  workspace: Dev
  project: michiel
  labels: ["starcoder", "rag"]
  max_restarts: 0
  perform_initial_validation: True

augment:
  podspec_path: "8xA100.yaml"
  gpu_count: 32
  save_trial_best: 0

  checkpoint_handling:
    action: "gc"
  entrypoint: "/run/determined/workdir/research/gpt-neox/jobs/determined.sh"

overrides:
  wandb_name: *xname
  wandb_group: *xname
  # 16k tokens per GPU
  train_micro_batch_size_per_gpu: 2
  gradient_accumulation_steps: 32
  train_batch_size: 1024

  train_iters: 1000
  lr_decay_iters: null  # If not set, defaults to train_iters
  warmup_iters: 100
  lr_decay_style: cosine

  # FIM context loss mask
  loss_mask_mode: fim-context
  extra_loss_masks:
    - fim-context
    - pad

  attention_precision: bfloat16

  seq-length: 4096

  min_lr: 1.0e-6
  optimizer:
    params:
      betas:
      - 0.9
      - 0.95
      eps: 1.0e-08
      lr: 1.0e-5
    type: Adam

  # Eval/save frequency
  eval_interval: 50
  save_interval: 100
  log-interval: 10

  # Data
  train_data_paths: ["/mnt/efs/augment/data/processed/rag/dataset/roguesl_prefsufret_nearsuf100olap20_nearpref100olap20_quant20/dataset"]
  valid_data_paths: ["/mnt/efs/augment/data/processed/rag/dataset/roguesl_prefsufret_nearsuf100olap20_nearpref100olap20_quant20/validation_dataset"]
  test_data_paths: ["/mnt/efs/augment/data/processed/rag/dataset/roguesl_prefsufret_nearsuf100olap20_nearpref100olap20_quant20/validation_dataset"]
  max_valid_data_size: 8192
  data_impl: mmap
  dataset_type: direct

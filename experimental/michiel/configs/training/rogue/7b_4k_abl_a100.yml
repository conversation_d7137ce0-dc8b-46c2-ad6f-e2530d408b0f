includes:
- augment_configs/starcoder/model/starcoder.yml
- augment_configs/starcoder/model/starcoder-7b.yml

determined:
  name: &xname rogue_7b_b512s512_ethanol31m_min500kmax50m_fp32
  description: null
  workspace: Dev
  project: michiel
  labels: ["starcoder", "rag"]
  max_restarts: 0
  perform_initial_validation: True

augment:
  podspec_path: "8xA100.yaml"
  gpu_count: 8
  save_trial_best: 0

  checkpoint_handling:
    action: "gc"
  entrypoint: "/run/determined/workdir/research/gpt-neox/jobs/determined.sh"

overrides:
  wandb_name: *xname
  wandb_group: *xname
  # 16k tokens per GPU
  train_micro_batch_size_per_gpu: 2
  gradient_accumulation_steps: 32
  train_batch_size: 512

  train_iters: 500
  lr_decay_iters: null  # If not set, defaults to train_iters
  warmup_iters: 50
  lr_decay_style: cosine

  # FIM context loss mask
  loss_mask_mode: fim-context
  extra_loss_masks:
    - fim-context
    - pad

  # attention_precision: bfloat16
  flash_attention: false
  fp16:
    enabled: false

  seq-length: 4096

  min_lr: 1.0e-6
  optimizer:
    params:
      betas:
      - 0.9
      - 0.95
      eps: 1.0e-08
      lr: 1.0e-5
    type: Adam

  # Eval/save frequency
  eval_interval: 50
  save_interval: 100
  log-interval: 10

  # Data
  train_data_paths: ["/mnt/efs/augment/data/processed/rag/dataset/ethanol31m_min500kmax50m/dataset"]
  valid_data_paths: ["/mnt/efs/augment/data/processed/rag/dataset/ethanol31m_min500kmax50m/validation_dataset"]
  test_data_paths: ["/mnt/efs/augment/data/processed/rag/dataset/ethanol31m_min500kmax50m/validation_dataset"]
  max_valid_data_size: 8192
  data_impl: mmap
  dataset_type: direct

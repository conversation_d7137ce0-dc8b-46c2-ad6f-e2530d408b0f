includes:
- augment_configs/starcoder/model/starcoder.yml
- augment_configs/starcoder/model/starcoder-16b.yml

determined:
  name: &xname ender16B_b512s512_pydiffb1m_noinline_drop_2kcon_nosig_nodense
  description: null
  workspace: Dev
  project: michiel
  labels: ["starcoder", "rag"]
  max_restarts: 0
  perform_initial_validation: True

augment:
  podspec_path: "8xA100.yaml"
  gpu_count: 8
  save_trial_best: 0

  checkpoint_handling:
    action: "gc"
  entrypoint: "/run/determined/workdir/research/gpt-neox/jobs/determined.sh"

overrides:
  wandb_name: *xname
  wandb_group: *xname
  # 16k tokens per GPU
  train_micro_batch_size_per_gpu: 2
  gradient_accumulation_steps: 64
  train_batch_size: 512

  train_iters: 500
  lr_decay_iters: null  # If not set, defaults to train_iters
  warmup_iters: 50
  lr_decay_style: cosine

  # FIM context loss mask
  loss_mask_mode: signature-fim
  extra_loss_masks:
    - signature-fim
    - pad

  attention_precision: bfloat16

  seq-length: 5120

  min_lr: 1.0e-6
  optimizer:
    params:
      betas:
      - 0.9
      - 0.95
      eps: 1.0e-08
      lr: 1.0e-5
    type: Adam

  # Eval/save frequency
  eval_interval: 50
  save_interval: 100
  log-interval: 10
  extra_valid_metrics: ["fim_exact_match"]

  # Data
  train_data_paths: ["/mnt/efs/augment/data/processed/rag/dataset/ender_pydiffb1m_noinline_drop_2kcon_nosig_nodense/dataset"]
  valid_data_paths: ["/mnt/efs/augment/data/processed/rag/dataset/ender_pydiffb1m_noinline_drop_2kcon_nosig_nodense/validation_dataset"]
  test_data_paths: ["/mnt/efs/augment/data/processed/rag/dataset/ender_pydiffb1m_noinline_drop_2kcon_nosig_nodense/validation_dataset"]
  max_valid_data_size: 8192
  data_impl: mmap
  dataset_type: direct

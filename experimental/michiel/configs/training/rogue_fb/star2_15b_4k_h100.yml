determined:
  name: &xname roguesl_star2_roguesl_eth6_4m_morelang3_fprefsufret_npref250_quant50_rdrop015
  description: null
  workspace: Dev
  project: michiel

augment:
  podspec_path: "8xH100.yaml"
  gpu_count: 32

fastbackward_configs:
 - configs/starcoder2_15b.py

fastbackward_args:
  loss_mask_policy: fim
  fim_middle_token_id: 2
  eot_token_id: 0
  pad_token_id: 49152
  gradient_accumulation_steps: 32
  batch_size: 2
  max_iters: 4000
  warmup_iters: 100
  lr_decay_iters: 4000
  block_size: 4096
  min_lr: 1.0e-6
  learning_rate: 1.0e-5
  decay_lr: True
  eval_interval: 250
  checkpoint: /mnt/efs/augment/checkpoints/starcoder2-15b-fb-mp2
  train_data_path: /mnt/efs/augment/data/processed/rag/dataset/star2_roguesl_eth6_4m_morelang3_fprefsufret_npref250_quant50_rdrop015/dataset
  eval_data_path: /mnt/efs/augment/data/processed/rag/dataset/star2_roguesl_eth6_4m_morelang3_fprefsufret_npref250_quant50_rdrop015/validation_dataset
  model_vocab_size: 49176
  checkpoint_optimizer_state: False

  tokenizer_name: StarCoder2Tokenizer
  use_research_tokenizer: True
  visualize_logits_samples: 8

  run_name: *xname
  wandb_project: rogue

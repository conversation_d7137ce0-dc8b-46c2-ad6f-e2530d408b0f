determined:
  description: null
  workspace: Dev
  project: michiel

augment:
  podspec_path: "8xH100.yaml"
  gpu_count: 64

fastbackward_configs:
 - configs/deepseek_base_33b.py

fastbackward_args:
  loss_mask_policy: fim
  fim_middle_token_id: 32017
  eot_token_id: 32014
  pad_token_id: 32014
  gradient_accumulation_steps: 32
  batch_size: 2
  max_iters: 4000
  warmup_iters: 100
  lr_decay_iters: 4000
  block_size: 7936
  min_lr: 1.0e-6
  learning_rate: 1.0e-5
  decay_lr: True
  eval_interval: 250
  hf_checkpoint_dir: /mnt/efs/augment/checkpoints/deepseek/deepseek-coder-33b-base
  train_data_path: /mnt/efs/augment/data/processed/rag/dataset/ds_eth6_4m_morelang3_fpref1kretnpref0k5suf0k5_aug05rdrop03/dataset
  eval_data_path: /mnt/efs/augment/data/processed/rag/dataset/ds_eth6_4m_morelang3_fpref1kretnpref0k5suf0k5_aug05rdrop03/validation_dataset
  checkpoint_optimizer_state: True

  tokenizer_name: DeepSeekCoderBaseTokenizer
  use_research_tokenizer: True
  visualize_logits_samples: 8

  run_name: ds_eth6_4m_morelang3_fpref1kretnpref0k5suf0k5_aug05rdrop03
  wandb_project: rogue

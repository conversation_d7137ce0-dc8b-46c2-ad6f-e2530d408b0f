determined:
  description: null
  workspace: Dev
  project: michiel

augment:
  podspec_path: "8xH100.yaml"
  gpu_count: 64

fastbackward_configs:
 - configs/qwen25coder_7b.py

fastbackward_args:
  loss_mask_policy: fim
  gradient_accumulation_steps: 24
  batch_size: 1
  max_iters: 5000
  lr_decay_iters: 5000
  warmup_iters: 100
  block_size: 7936
  min_lr: 1.0e-6
  learning_rate: 1.0e-5
  decay_lr: True
  eval_interval: 500
  hf_checkpoint_dir: /mnt/efs/augment/checkpoints/qwen25-coder/Qwen2.5-Coder-7B/
  train_data_path: /mnt/efs/spark-data/user/michiel/elden/qwen/dataset/qwen25coder/dataset
  eval_data_path: /mnt/efs/spark-data/user/michiel/elden/qwen/dataset/qwen25coder/validation_dataset
  checkpoint_optimizer_state: False
  loss_function: sequence_chunked_cross_entropy

  tokenizer_name: qwen25coder
  use_research_tokenizer: false
  visualize_logits_samples: 32

  run_name: qwen_7b_elden_smart_scce
  wandb_project: rogue

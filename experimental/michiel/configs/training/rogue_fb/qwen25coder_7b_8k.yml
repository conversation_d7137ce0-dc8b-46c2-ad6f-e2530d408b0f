determined:
  description: null
  workspace: Dev
  project: michiel

augment:
  podspec_path: "8xH100.yaml"
  gpu_count: 72

fastbackward_configs:
 - configs/qwen25coder_7b.py

fastbackward_args:
  loss_mask_policy: fim
  fim_middle_token_id: 2
  eot_token_id: 0
  pad_token_id: 49152
  gradient_accumulation_steps: 20
  batch_size: 1
  max_iters: 5400
  lr_decay_iters: 5400
  warmup_iters: 100
  block_size: 7936
  min_lr: 1.0e-6
  learning_rate: 1.0e-5
  decay_lr: True
  eval_interval: 500
  # checkpoint: /mnt/efs/augment/checkpoints/qwen25-coder/Qwen2.5-Coder-7B/
  hf_checkpoint_dir: /mnt/efs/augment/checkpoints/qwen25-coder/Qwen2.5-Coder-7B/
  train_data_path: /mnt/efs/spark-data/user/michiel/elden/qwen/dataset/qwen25coder/dataset
  eval_data_path: /mnt/efs/spark-data/user/michiel/elden/qwen/dataset/qwen25coder/validation_dataset
  model_vocab_size: 152064
  checkpoint_optimizer_state: False

  tokenizer_name: qwen25coder
  use_research_tokenizer: False
  visualize_logits_samples: 32

  run_name: qwen7b_elden_fprefretsignpfsuf
  wandb_project: rogue

determined:
  name: &xname RogueSL-Llama-8B-4k-4ksteps
  description: null
  workspace: Dev
  project: michiel

augment:
  podspec_path: "8xA100.yaml"
  gpu_count: 64

fastbackward_configs:
 - configs/llama3_8b.py

fastbackward_args:
  loss_mask_policy: fim
  fim_middle_token_id: 128014
  eot_token_id: 128001
  pad_token_id: 128001
  gradient_accumulation_steps: 8
  batch_size: 2
  max_iters: 4000
  warmup_iters: 100
  lr_decay_iters: 4000
  block_size: 4096
  min_lr: 1.0e-6
  learning_rate: 1.0e-5
  decay_lr: True
  eval_interval: 100
  checkpoint: /mnt/efs/augment/checkpoints/llama3/Meta-Llama-3-8B
  train_data_path: /mnt/efs/augment/data/processed/rag/dataset/llama3_roguesl_eth6_4m_morelang_fprefsufret_npref250_quant50_rdrop015/dataset
  eval_data_path: /mnt/efs/augment/data/processed/rag/dataset/llama3_roguesl_eth6_4m_morelang_fprefsufret_npref250_quant50_rdrop015/validation_dataset
  model_vocab_size: 128256
  checkpoint_optimizer_state: True

  tokenizer_name: LLama3BaseTokenizer
  use_research_tokenizer: True
  visualize_logits_samples: 8

  run_name: *xname
  wandb_project: rogue

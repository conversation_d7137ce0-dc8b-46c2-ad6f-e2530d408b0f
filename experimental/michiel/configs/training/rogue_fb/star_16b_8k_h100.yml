determined:
  name: &xname roguesl_cw_test
  description: null
  workspace: Dev
  project: michiel

augment:
  podspec_path: "8xH100.yaml"
  gpu_count: 64

fastbackward_configs:
 - configs/starcoder_16b.py

fastbackward_args:
  loss_mask_policy: fim
  fim_middle_token_id: 2
  eot_token_id: 0
  pad_token_id: 49152
  gradient_accumulation_steps: 16
  batch_size: 2
  max_iters: 4000
  warmup_iters: 100
  lr_decay_iters: 4000
  block_size: 7936
  min_lr: 1.0e-6
  learning_rate: 1.0e-5
  decay_lr: True
  eval_interval: 250
  hf_checkpoint_dir: /mnt/efs/augment/checkpoints/starcoderbase
  train_data_path: /mnt/efs/augment/data/processed/rag/dataset/roguesl_eth6_4m_morelang3_retprefsuf_aug05rdrop03/dataset
  eval_data_path: /mnt/efs/augment/data/processed/rag/dataset/roguesl_eth6_4m_morelang3_retprefsuf_aug05rdrop03/validation_dataset
  model_vocab_size: 51200
  checkpoint_optimizer_state: False

  tokenizer_name: StarCoderTokenizer
  use_research_tokenizer: True
  visualize_logits_samples: 8

  run_name: *xname
  wandb_project: rogue

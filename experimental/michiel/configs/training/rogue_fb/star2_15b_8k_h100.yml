determined:
  name: &xname sc2_pfretnpfsuf_rdrop030_0608_55k_1_fixspecial
  description: null
  workspace: Dev
  project: michiel

augment:
  podspec_path: "8xH100.yaml"
  gpu_count: 96

fastbackward_configs:
 - configs/starcoder2_15b.py

fastbackward_args:
  loss_mask_policy: fim
  fim_middle_token_id: 2
  eot_token_id: 0
  pad_token_id: 49152
  gradient_accumulation_steps: 16
  batch_size: 2
  max_iters: 6000
  lr_decay_iters: 6000
  warmup_iters: 100
  block_size: 7936
  min_lr: 1.0e-6
  learning_rate: 1.0e-5
  decay_lr: True
  eval_interval: 250
  checkpoint: /mnt/efs/augment/checkpoints/starcoder2-15b-fb-mp2
  train_data_path: /mnt/efs/augment/data/processed/rag/dataset/sc2_pfretnpfsuf_rdrop030_0608_55k_1_fixspecial/dataset
  eval_data_path: /mnt/efs/augment/data/processed/rag/dataset/sc2_pfretnpfsuf_rdrop030_0608_55k_1_fixspecial/validation_dataset
  model_vocab_size: 49176
  checkpoint_optimizer_state: True

  tokenizer_name: StarCoder2Tokenizer
  use_research_tokenizer: True
  visualize_logits_samples: 32

  run_name: *xname
  wandb_project: rogue

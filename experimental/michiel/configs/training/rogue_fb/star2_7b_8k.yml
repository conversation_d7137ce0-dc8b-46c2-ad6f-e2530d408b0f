determined:
  description: null
  workspace: Dev
  project: michiel

augment:
  podspec_path: "8xH100.yaml"
  gpu_count: 192

fastbackward_configs:
 - configs/starcoder2_7b.py

fastbackward_args:
  loss_mask_policy: fim
  fim_middle_token_id: 2
  eot_token_id: 0
  pad_token_id: 49152
  gradient_accumulation_steps: 2
  batch_size: 4
  max_iters: 5000
  warmup_iters: 100
  lr_decay_iters: 5000
  block_size: 7936
  min_lr: 1.0e-6
  learning_rate: 1.0e-5
  decay_lr: True
  eval_interval: 500
  checkpoint: /mnt/efs/augment/checkpoints/starcoder2-7b-fb
  train_data_path: /mnt/efs/spark-data/user/michiel/elden/smart/dataset/sc2/dataset
  eval_data_path: /mnt/efs/spark-data/user/michiel/elden/smart/dataset/sc2/validation_dataset
  model_vocab_size: 49176
  checkpoint_optimizer_state: False

  tokenizer_name: StarCoder2Tokenizer
  use_research_tokenizer: True
  visualize_logits_samples: 32

  run_name: elden_7b_fprefretsignpfsuf_smartnohead
  wandb_project: rogue

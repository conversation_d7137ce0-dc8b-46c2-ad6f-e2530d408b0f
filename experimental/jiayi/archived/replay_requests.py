"""Utilities for replaying requests using research models."""

from __future__ import annotations

import logging
from collections.abc import Mapping
from pathlib import Path
from typing import Iterable, Optional, Sequence

import ipywidgets as widgets
from IPython.display import display

from base.datasets.completion import CompletionDatum
from base.datasets.completion_dataset import CompletionDataset
from base.datasets.gcs_blob_cache import PathAndContent
from base.static_analysis.common import shorten_str
from research.core.model_input import ModelInput
from research.core.types import <PERSON><PERSON><PERSON><PERSON><PERSON>, Document
from research.eval.harness.systems import AbstractSystem
from research.eval.harness.systems.abs_system import CompletionResult
from research.eval.harness.systems.static_analysis_systems import (
    EnderSystem,
    SignatureSystem,
)
from research.utils.notebook_uis.notebook_uis import code_html, show_header


def index_all_docs(
    system: AbstractSystem,
    dataset: CompletionDataset,
    requests: Iterable[CompletionDatum],
):
    # get all the required documents
    all_docs = {
        blob_name: Document(
            id=blob_name,
            text=maybe.content,
            path=str(maybe.path),
            meta=None,
        )
        for datum in requests
        for blob_name, maybe in zip(
            datum.request.blob_names,
            dataset.get_blobs(datum.request.blob_names),
        )
        if maybe is not None
    }
    logging.info("Adding %d docs to the index.", len(all_docs))
    system.add_docs(list(all_docs.values()))


def update_signature_index(
    system: EnderSystem | SignatureSystem,
    indexed_sig_blobs: set[str],
    new_blobs: Mapping[str, Optional[PathAndContent]],
):
    """Incrementally update the signature index to the new blobs."""

    # EnderSystem's signature index doesn't support the doc_ids argument to
    # ignore missing documents, so we are removing them here. We don't remove
    # them from the dense retriever, because it is very slow to recompute the
    # embeddings.
    if isinstance(system, EnderSystem):
        sig_index = system.signature_index
    else:
        sig_index = system.signature_index

    to_remove = indexed_sig_blobs - set(new_blobs)
    for path in to_remove:
        sig_index.remove_file(Path(path))
        indexed_sig_blobs.remove(path)

    docs_to_add = [
        Document(
            id=blob_name,
            text=blob.content,
            path=str(blob.path),
            meta=None,
        )
        for blob_name in (new_blobs.keys() - indexed_sig_blobs)
        if (blob := new_blobs.get(blob_name)) is not None
    ]
    system.save_additional_info = True
    sig_index.add_docs(docs_to_add)
    indexed_sig_blobs.update(doc.id for doc in docs_to_add if doc.path)
    logging.info("Added %d docs to the index.", len(docs_to_add))


def to_model_input(
    datum: CompletionDatum,
) -> ModelInput:
    """Run the task with the given system and save the results into output_path/output_prefix_xxx."""
    return ModelInput(
        prefix=datum.request.prefix,
        suffix=datum.request.suffix,
        target=datum.response.text,
        path=datum.request.path,
        doc_ids=datum.request.blob_names,
        extra={
            "ground_truth_span": CharRange(
                start=len(datum.request.prefix), stop=len(datum.request.prefix)
            ),
            # NOTE(arun): Why do we have this if we have `target`?
            "ground_truth": datum.response.text,
        },
    )


def render_completion(
    datum: CompletionDatum,
    result: CompletionResult,
    prefix_len: int = 500,
    suffix_len: int = 500,
):
    """Print the given completion."""
    # add prefix
    prefix_text = shorten_str(datum.request.prefix, prefix_len, omit_mode="left")
    # add suffix
    suffix_text = shorten_str(datum.request.suffix, suffix_len, omit_mode="right")
    output_sections: list[str] = [
        f"Request id: {datum.request_id}",
        f"File path: {datum.request.path}",
        show_header("Prefix"),
        prefix_text,
        show_header(
            f"Original prediction ({datum.response.model})",
            width=10,
        ),
        datum.response.text,
        show_header("Replayed prediction", width=10),
        result.generated_text,
        show_header("Suffix"),
        suffix_text,
    ]
    output_text = "\n".join(output_sections)
    output_ui = widgets.HTML(code_html(output_text))

    titles = ["Output"]
    children: list[widgets.Widget] = [output_ui]

    add_info = result.extra_output.additional_info
    add_keys = sorted(add_info.keys())
    # make a tab entry for each member
    titles.extend(add_keys)
    children.extend(widgets.HTML(code_html(str(add_info[k]))) for k in add_keys)
    tab = widgets.Tab(children=children, titles=titles)
    return tab


def display_replay_viewer(
    completions: Sequence[CompletionDatum],
    replayed_responses: Sequence[CompletionResult],
):
    """Build the ipywidgets UI for viewing the results."""
    example_selector = widgets.IntText(
        value=0,
        min=0,
        max=len(completions) - 1,
        description="Example:",
    )

    def inc_example(delta: int):
        ex_id = example_selector.value + delta
        ex_id = ex_id % len(completions)
        example_selector.value = ex_id

    next_button = widgets.Button(description="Next")
    prev_button = widgets.Button(description="Prev")
    next_button.on_click(lambda _: inc_example(1))
    prev_button.on_click(lambda _: inc_example(-1))

    prefix_len_selector = widgets.IntText(value=1000, description="Prefix len:")
    suffix_len_selector = widgets.IntText(value=500, description="Suffix len:")

    main_output = widgets.VBox()

    def display_example(_):
        ex_id = example_selector.value
        ex_id = min(max(ex_id, 0), len(completions) - 1)
        prefix_len = prefix_len_selector.value
        suffix_len = suffix_len_selector.value
        r = render_completion(
            completions[ex_id],
            replayed_responses[ex_id],
            prefix_len=prefix_len,
            suffix_len=suffix_len,
        )
        main_output.children = [r]

    for selector in [example_selector, prefix_len_selector, suffix_len_selector]:
        selector.observe(display_example, names="value")

    display_example(None)

    ui = widgets.VBox(
        [
            widgets.HBox(
                [prefix_len_selector, suffix_len_selector],
                layout=widgets.Layout(width="100%"),
            ),
            widgets.HBox([example_selector, prev_button, next_button]),
            main_output,
        ]
    )
    display(ui)

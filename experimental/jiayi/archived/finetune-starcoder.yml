includes:
  - augment_configs/starcoder/model/starcoder.yml
determined:
  name: new-FIM-starcoder-python-559K-opt
  description: null
  workspace: Dev
  project: jiayi
  labels: ["starcoder"]
  max_restarts: 0
  perform_initial_validation: True

augment:
  podspec_path: "8xA100.yaml"
  gpu_count: 8
  save_trial_best: 0

  checkpoint_handling:
    action: "gc"
  entrypoint: "/run/determined/workdir/research/gpt-neox/jobs/determined.sh"

overrides:
  wandb_name: starcoder-python-559K-opt
  wandb_group: jiayi # change this to your group
  wandb_project: new-FIM-sampler

  # data
  train_data_paths:
    [
      "/mnt/efs/augment/data/processed/new-fim/starcoder-python-559K-splits/train",
    ]
  valid_data_paths:
    [
      "/mnt/efs/augment/data/processed/new-fim/starcoder-python-559K-splits/valid",
    ]
  test_data_paths:
    [
      "/mnt/efs/augment/data/processed/new-fim/starcoder-python-559K-splits/test",
    ]
  data_impl: mmap
  dataset_type: direct

  # 8k tokens per GPU
  train_micro_batch_size_per_gpu: 2
  gradient_accumulation_steps: 2
  train_batch_size: 16

  train_iters: 2000
  warmup: 0.01
  lr_decay_style: constant

  optimizer:
    params:
      betas:
        - 0.9
        - 0.95
      eps: 1.08
      lr: 1.0e-05
    type: Adam

  weight-decay: 0.1

  # Eval/save frequency
  eval_interval: 500
  save_interval: 1000
  log-interval: 100

  # FIM context loss mask
  loss_mask_mode: none
  extra_loss_masks:
    - pad
    - fim-context
    - eod-only

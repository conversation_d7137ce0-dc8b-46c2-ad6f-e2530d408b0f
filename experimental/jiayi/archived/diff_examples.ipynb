{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## An indentation example\n", "\n", "The `line_diff` algorithm handles big modifications such as indentation changes poorly. In the example below, we indented 5 lines and slightly modified the 2nd line. Instead of giving us a diff that matches each changed line pair, `line_diff`\n", "only gives us a single big modification span."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.core.str_diff import line_diff, print_diff\n", "\n", "before = \"\"\"\\\n", "This\n", "is a motivating example\n", "to test how the diff\n", "algorithm handles\n", "indentation change.\n", "\"\"\"\n", "\n", "after = \"\"\"\\\n", "    This\n", "    is a motivating testcase\n", "    to test how the diff\n", "    algorithm handles\n", "    indentation change.\n", "\"\"\"\n", "\n", "diff = line_diff(before, after)\n", "\n", "print_diff(diff)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["This is bad for our edit problem sampling algorithm since we rely on the diff alignment to figure out the replacement code for a given edit region. When having a big modification span like this, whenever the edit region happens to overlap with any of these lines in the before version, our algorithm will treat all these indented lines as the output.\n", "\n", "This is also not ideal for prompt formatting. Since we format the model output based on the same line diff algorithm, as a result, we are teaching the model to delete all 5 lines before inserting the 5 new lines, making the diff less local and less streamable."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.next_edits.edit_gen_formatters import encode_model_diff\n", "\n", "\n", "_, diff_str = encode_model_diff(before, after, algorithm=\"linediff\")\n", "print(diff_str)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The good news is that diff<PERSON><PERSON> actually implements a more precise line diff algorithm called `ndiff`, which is based on the `Differ` class and gives more precise line alignments, as shown below. Unfortunately, the algorithm is not directly usable for our application since its tied to a specific textual format and also has a cubic time worst-case complexity "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from difflib import ndiff\n", "\n", "ndiff_str = \"\\n\".join(ndiff(before.splitlines(), after.splitlines()))\n", "print(ndiff_str)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["To use this algorithm for our application, I adapted `ndi<PERSON><PERSON>'s original algorithm to directly produce a `StrDiff` object, and I also bounded its time complexity (at the cost of giving up some quality in some cases). "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.core.str_diff import line_dmp_diff, precise_line_diff\n", "\n", "\n", "diff = precise_line_diff(before, after)\n", "\n", "print_diff(diff)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.next_edits.edit_gen_formatters import encode_model_diff\n", "\n", "\n", "_, diff_str = encode_model_diff(before, after, algorithm=\"precise_linediff\")\n", "print(diff_str)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
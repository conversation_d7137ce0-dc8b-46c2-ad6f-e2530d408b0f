"""Benchmark tree-sitter parsing memory usage over time."""

import gc
import tracemalloc
from typing import Callable

from tqdm.auto import tqdm

from base.static_analysis.parsing import GlobalParser
from research.core.constants import AUGMENT_ROOT
from research.utils.data_utils import Src<PERSON><PERSON>o, read_src_repo


def get_memory_usage_over_time(task: Callable[[], None], trials: int):
    mem_usages = list[int]()
    tracemalloc.start()

    current, _ = tracemalloc.get_traced_memory()
    mem_usages.append(current)
    print(f"Initial memory usage: {current/2**20:.3f}MB")

    try:
        for i in range(trials):
            task()
            gc.collect()
            current, peak = tracemalloc.get_traced_memory()
            mem_usages.append(current)
            print(
                f"[{i}] Memory usage: {current/2**20:.3f}MB, Peak: {peak/2**20:.3f}MB"
            )
    finally:
        tracemalloc.stop()
    return mem_usages


def parsing_task(repo: SrcRepo):
    """Parse a large amount of code using tree sitter."""
    parser = GlobalParser
    for file in tqdm(list(repo.files) * 5):
        parser.parse_ts_tree(file.code, file.lang)


global_storage = []


def test_task():
    """A test task that leaks memory."""
    global_storage.append(list(range(10_000_000)))


if __name__ == "__main__":
    print("Reading repo from:", AUGMENT_ROOT / "../test_repos/chromium")
    repo = read_src_repo(
        langs=("python", "javascript", "typescript", "cpp", "rust", "go"),
        repo_root=AUGMENT_ROOT / "../test_repos/chromium",
    )
    print(f"Repo size: {len(repo.files)}")
    mem_usages = get_memory_usage_over_time(lambda: parsing_task(repo), 10)
    # mem_usages = get_memory_usage_over_time(test_task, 10)
    print([f"{x/2**20}MB" for x in mem_usages])

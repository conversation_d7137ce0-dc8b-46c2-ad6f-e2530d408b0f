{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Replay requests against research models.\n", "\n", "This notebook includes an example of how to replay requests in dogfood (or aitutor-*) \n", "against a research system. For an automated version of this, please see `pinocchio.py`.\n", "\n", "# Replay requests in dev tenants.\n", "\n", "This notebook includes an example of how to replay requests to dogfood (or aitutor-*) \n", "against dev tenants. This can be useful to test models on real requests without\n", "having to deploy the model to staging.\n", "\n", "## Setup (should only need to be done once)\n", "\n", "1. Install the required Python libraries:\n", "```bash\n", "pip3 install ipywidgets\n", "pip3 install -U google-cloud-bigquery google-cloud-storage lru-dict pympler\n", "```\n", "2. Authenticate with Google:\n", "```bash\n", "gcloud auth login\n", "gcloud auth application-default login\n", "```\n", "3. Generate the proto library files (do periodically):\n", "```bash\n", "bazel run //tools/generate_proto_typestubs\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.datasets.completion_dataset import CompletionDataset\n", "\n", "# == Configure your variables here ==\n", "# Which tenant to grab data from.\n", "TENANT_NAME = \"dogfood\"\n", "\n", "\n", "# All examples from the signature spreadsheet\n", "ids_and_solvable = [\n", "    (\"e0a30509-89b0-4d1c-ae83-83077ff1eb24\", False),  # ex 1\n", "    (\"38315ad8-b8f3-4c6a-84e5-afa5933f38ba\", True),\n", "    (\"dd5515fc-3f1c-4eb7-b8aa-16f755886d0e\", True),\n", "    (\"d46dc24a-f904-41b8-a2bb-d52545513e96\", True),\n", "    (\"341aefb7-45b7-4b72-b000-b9f166d3f95f\", True),\n", "    (\"ce3d5d6c-030f-4b3f-975d-1c6637dc9fd6\", True),\n", "    (\"200e17a6-d2b2-4128-8856-a7a034fd135f\", True),\n", "    (\"4813df91-f7b9-4e69-9a07-0547b84ccb43\", True),\n", "    (\"669c8097-7324-4af9-afde-8098b6de1fdc\", True),\n", "    (\"57d60d7d-c2ce-4755-8a7b-4a3d01214859\", True),\n", "    (\"73e95698-12d1-49cc-b5a0-6179dca2acde\", True),\n", "    (\"64a5482a-5706-4fbb-8b0f-e44ea5a0f9ab\", True),\n", "    (\"86dff485-1174-4bc3-a9d4-5dec6d8be5d2\", False),  # ex 13\n", "    (\"f59e877b-df59-4812-8ce1-b5c4e4686b25\", False),  # ex 14\n", "    (\"438a4cb2-65af-4cee-a0ce-91f5e9f4dea0\", False),  # ex 15\n", "    (\"9f6469a2-65f3-486c-a58e-d34c598087b6\", False),  # ex 16\n", "    (\"3456d0ed-1abc-42a0-837d-4cf4be2db30b\", True),\n", "    (\"0272b5ff-69e5-4034-a31d-b430756f3f4f\", True),\n", "    (\"19d3d1b0-0509-4074-bab0-140e12356350\", True),\n", "    (\"5bb9f026-9c2a-4681-b1df-34ba6e7b6407\", True),\n", "    (\"5f9abc1e-bbdb-465f-8ee4-91c5ed178958\", True),\n", "    (\"ad4660bf-22d1-4eb7-a07c-ddf9b33ce357\", True),\n", "    (\"c2f63804-efed-4711-970b-8b9032476758\", True),\n", "    (\"2c9f54cb-fd41-4adc-8fc5-59074916c06d\", True),\n", "    (\"37b49998-03f4-441f-b584-982aae11fcfb\", True),\n", "    (\"c2660355-5bf2-4cf7-a9ac-a4c4435a74b7\", True),\n", "    (\"58512ff9-45ca-4f45-8446-be352e2c3bdb\", True),\n", "    (\"8ad2dbeb-edea-425e-bbaa-3e54fd78a1f0\", True),\n", "    (\"73ad3121-7aec-4dea-8542-1e2f3d2e1b59\", True),\n", "    (\"7e6d997c-3d6e-4493-a3a2-13983fbb571f\", True),\n", "    (\"d454140e-dd4b-4cf8-bd19-3a61d85e9163\", True),\n", "    (\"126fbf6e-a739-4ad1-9942-af8da6af17f0\", False),  # ex 32\n", "    (\"b251b2bf-eed4-475b-b969-cf6807aab4df\", True),\n", "    (\"b17c46dd-d07e-4eac-b3ee-6feddb83cb3f\", True),\n", "]\n", "\n", "\n", "# The filter criteria for the dataset you want to replay. This is configured to replay\n", "# specific set of request ids, but there are plenty of options you can play with.\n", "\n", "# ids_to_replay = [req_id for req_id, sovable in ids_and_solvable if sovable]\n", "ids_to_replay = [req_id for req_id, _ in ids_and_solvable]\n", "DATASET_FILTERS = CompletionDataset.Filters(request_ids=ids_to_replay)\n", "\n", "# An alternate choice of dataset filter to find a random set of accepted requests.\n", "# DATASET_FILTERS = CompletionDataset.Filters(\n", "#     accepted_only=True,\n", "# )\n", "\n", "# Ordering by request id will effectively give you data in a random order.\n", "DATASET_ORDER_BY: CompletionDataset.OrderBy = \"request_id\"\n", "\n", "# The maximum number of requests to replay. Here to make sure you don't accidentally\n", "# replay thousands of requests.\n", "DATASET_LIMIT = 100"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Actually get the dataset.\n", "from base.datasets import tenants\n", "from base.datasets.completion import CompletionDatum\n", "from research.utils.inspect_indexed_dataset import print_green, print_yellow\n", "\n", "dataset = CompletionDataset.create(\n", "    tenants.get_tenant(TENANT_NAME),\n", "    filters=DATASET_FILTERS,\n", "    order_by=DATASET_ORDER_BY,\n", "    limit=DATASET_LIMIT,\n", ")\n", "id_to_completion = {c.request_id: c for c in dataset.get_completions()}\n", "\n", "# sort the completions following the order of ids_to_show\n", "completions = list[CompletionDatum]()\n", "for req_id in ids_to_replay:\n", "    if req_id in id_to_completion:\n", "        completions.append(id_to_completion[req_id])\n", "    else:\n", "        print_yellow(f\"Request not found: {req_id}\")\n", "print_green(f\"Retrieved {len(completions)} completions.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load a system.\n", "import yaml\n", "\n", "from research.eval.harness.factories import create_system\n", "\n", "# config of the ender system\n", "ender_config = yaml.safe_load(\n", "    \"\"\"\n", "dense_retriever:\n", "  chunker:\n", "    max_lines_per_chunk: 30\n", "    name: line_level\n", "  document_formatter:\n", "    add_path: true\n", "    max_tokens: 999\n", "    name: ethanol6_document\n", "  query_formatter:\n", "    add_path: true\n", "    add_suffix: true\n", "    max_tokens: 1023\n", "    name: ethanol6_query\n", "    prefix_ratio: 0.9\n", "  scorer:\n", "    checkpoint_path: ethanol/ethanol6-16.1\n", "    name: ethanol\n", "fim_mode: evaluation\n", "generation_options:\n", "  max_generated_tokens: 280\n", "model:\n", "  model_path: ender/16b_multieth61m_fullsig_b256s4k\n", "  name: ender_fastforward\n", "  prompt:\n", "    component_order: [signature, retrieval, prefix, suffix]\n", "    max_prefix_tokens: 1280\n", "    max_prompt_tokens: 5120\n", "    max_retrieved_chunk_tokens: -1\n", "    max_signature_tokens: 1024\n", "    max_suffix_tokens: 768\n", "name: ender_sys\n", "sig_prompt_formatter:\n", "  max_middle_tks: 1024\n", "signature_index:\n", "  est_prefix_chars: 3840\n", "  est_suffix_chars: 2304\n", "  max_ctx_signature_chars: 3000\n", "  sig_printer:\n", "    show_full_method_signatures: true\n", "  usage_distance_metric: var_occurrence\n", "  verbose: false\n", "\"\"\"\n", ")\n", "\n", "system = create_system(ender_config)\n", "system.load()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Replay dataset\n", "from tqdm.auto import tqdm\n", "\n", "from research.eval.harness.systems.abs_system import CompletionResult\n", "from research.eval.harness.systems.static_analysis_systems import (\n", "    EnderSys<PERSON>,\n", "    SignatureSystem,\n", ")\n", "from research.tools.replay_requests import (\n", "    index_all_docs,\n", "    to_model_input,\n", "    update_signature_index,\n", ")\n", "\n", "\n", "if not isinstance(system, SignatureSystem):\n", "    # index all docs beforehand to save dense index compute\n", "    print_green(\"Indexing all dependent documents into the system.\")\n", "    index_all_docs(system, dataset, completions)\n", "\n", "if isinstance(system, (SignatureSystem, EnderSystem)):\n", "    system.signature_index.remove_all()\n", "    indexed_sig_docs = set[str]()\n", "\n", "replayed_responses = list[CompletionResult]()\n", "for datum in tqdm(completions, desc=\"Replaying\"):\n", "    print_green(f\"Evaluating on request {datum.request_id}\")\n", "\n", "    if isinstance(system, (EnderSystem, SignatureSystem)):\n", "        new_blobs = {\n", "            blob_name: maybe\n", "            for blob_name, maybe in zip(\n", "                datum.request.blob_names,\n", "                dataset.get_blobs(datum.request.blob_names),\n", "            )\n", "        }\n", "        update_signature_index(system, indexed_sig_docs, new_blobs)\n", "    replayed_responses.append(system.generate(to_model_input(datum)))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.tools.replay_requests import display_replay_viewer\n", "\n", "display_replay_viewer(completions, replayed_responses)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}
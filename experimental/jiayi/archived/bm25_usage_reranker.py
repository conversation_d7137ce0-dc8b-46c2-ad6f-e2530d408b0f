"""[WIP] A BM25-based usage reranker implementation.

This uses a minimal BM25 implementation adapted
from: research/retrieval/legacy_retrieval_implementations/bm25/reference_bm25.py.

NOTE(Jiayi): Compared to the version above, this version doesn't create a process pool
in its `__init__`, making it more suitable for different types of use cases.
TODO: Add methods to support incremental updates to the index.
"""

import math
from collections import defaultdict
from dataclasses import dataclass
from typing import Collection, Sequence

import numpy as np

from base.static_analysis.usage_analysis import (
    FileSummary,
    SymbolDefinition,
    UsageIndex,
)


@dataclass
class BM25UsageReranker:
    """Helper class to rerank the definition usages based on API co-occurrence."""

    current_file: FileSummary
    index: UsageIndex

    def __post_init__(self):
        all_files = self.index.file2summaries.keys()
        summaries = list(map(_file2words, self.index.file2summaries.values()))
        file_bm25 = BM25L(summaries)
        self._file_bonuses = dict(
            zip(all_files, file_bm25.get_scores(_file2words(self.current_file)))
        )
        self._def_bonuses = defaultdict[SymbolDefinition, float](lambda: 0.0)

    def score_def(self, d: SymbolDefinition) -> float:
        """Return how relevant a def is to the current context."""
        # distance_penalty = path_distance(d.file, self.current_file)
        distance_penalty = 0
        bonus = self._file_bonuses[d.file] + self._def_bonuses[d]
        if pd := d.parent_def:
            bonus += self._def_bonuses[pd]
        return bonus - distance_penalty


def _file2words(file: FileSummary) -> Collection[str]:
    defs = {d.name for d in file.definitions}
    usages = {name for name, _ in file.local_analysis.name_usages}
    return defs | usages


class BM25:
    """BM25 parent class."""

    def __init__(self, corpus: Sequence[Collection[str]]):
        self.corpus_size = 0
        self.avgdl = 0
        self.doc_freqs = []
        self.idf = {}
        self.doc_len = []

        nd = self._initialize(corpus)
        self._calc_idf(nd)

    def _initialize(self, corpus):
        nd = {}  # word -> number of documents with word
        num_doc = 0
        for document in corpus:
            self.doc_len.append(len(document))
            num_doc += len(document)

            frequencies = {}
            for word in document:
                if word not in frequencies:
                    frequencies[word] = 0
                frequencies[word] += 1
            self.doc_freqs.append(frequencies)

            for word, _ in frequencies.items():
                try:
                    nd[word] += 1
                except KeyError:
                    nd[word] = 1

            self.corpus_size += 1

        self.avgdl = num_doc / self.corpus_size
        return nd

    def _calc_idf(self, nd):
        raise NotImplementedError()

    def get_scores(self, query):
        """Get scores."""
        raise NotImplementedError()

    def get_batch_scores(self, query, doc_ids):
        """Get batch scores."""
        raise NotImplementedError()

    def get_top_n(self, query, documents, n=5):
        """Get top n."""
        assert self.corpus_size == len(
            documents
        ), "The documents given don't match the index corpus!"

        scores = self.get_scores(query)
        top_n = np.argsort(scores)[::-1][:n]
        return [documents[i] for i in top_n]


class BM25L(BM25):
    """Reference implementation of BM25L."""

    def __init__(self, corpus: Sequence[Collection[str]], k1=1.5, b=0.75, delta=0.5):
        # Algorithm specific parameters
        self.k1 = k1
        self.b = b
        self.delta = delta
        super().__init__(corpus)

    def _calc_idf(self, nd):
        for word, freq in nd.items():
            # idf = math.log(self.corpus_size + 1) - math.log(freq + 0.5)
            idf = math.log(self.corpus_size + 1) - math.log(freq + 0.5)
            self.idf[word] = idf

    def get_scores(self, query) -> Sequence:
        """Get the scores."""
        score = np.zeros(self.corpus_size)
        doc_len = np.array(self.doc_len)
        for q in query:
            q_freq = np.array([(doc.get(q) or 0) for doc in self.doc_freqs])
            ctd = q_freq / (1 - self.b + self.b * doc_len / self.avgdl)
            score += (
                (self.idf.get(q) or 0)
                * (self.k1 + 1)
                * (ctd + self.delta)
                / (self.k1 + ctd + self.delta)
            )
        return list(score)

    def get_batch_scores(self, query, doc_ids):
        """Calculate bm25 scores between query and subset of all docs."""
        assert all(di < len(self.doc_freqs) for di in doc_ids)
        score = np.zeros(len(doc_ids))
        doc_len = np.array(self.doc_len)[doc_ids]
        for q in query:
            q_freq = np.array([(self.doc_freqs[di].get(q) or 0) for di in doc_ids])
            ctd = q_freq / (1 - self.b + self.b * doc_len / self.avgdl)
            score += (
                (self.idf.get(q) or 0)
                * (self.k1 + 1)
                * (ctd + self.delta)
                / (self.k1 + ctd + self.delta)
            )
        return score.tolist()

{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "\n", "\n", "from research.static_analysis.parsing import ScopeOrSpan, GlobalParser, SrcScope, SrcSpan"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from glob import glob\n", "from pathlib import Path\n", "import json\n", "from research.core.types import Document\n", "\n", "\n", "# doc id -> doc\n", "retrieval_corpuses = {}\n", "retrieval_corpus_paths = glob('/mnt/efs/augment/data/eval/hydra/datasets/all_languages_2-3lines_medium_to_hard.v1.0/**/*_retrieval_db.jsonl') \n", "\n", "for retrieval_dbs_path in retrieval_corpus_paths:\n", "  repo_name = Path(retrieval_dbs_path).stem.replace(\"_retrieval_db\", \"\")\n", "  org_name = Path(retrieval_dbs_path).parent.stem\n", "\n", "  with Path(retrieval_dbs_path).open() as f:\n", "    for line in f.readlines():\n", "      doc = Document(**json.loads(line))\n", "      if doc.meta is None:\n", "        doc.meta = {}\n", "      doc.meta[\"repo_identifier\"] = (org_name, repo_name)\n", "      \n", "      retrieval_corpuses[doc.id] = doc"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["bad_doc_ids = \"\"\"<bad ids here>\"\"\".split(\" \")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.static_analysis.common import guess_lang_from_fp\n", "from research.core.utils import check_not_none\n", "from tqdm import tqdm\n", "\n", "from research.static_analysis.parsing import ScopeTreeParser\n", "from research.static_analysis.common import LanguageID\n", "\n", "robust_parser = ScopeTreeParser(parse_errored_root=True)\n", "\n", "for doc_id in tqdm(bad_doc_ids):\n", "    bad_doc: Document = retrieval_corpuses[doc_id]\n", "    path = check_not_none(bad_doc.path)\n", "    lang: LanguageID = check_not_none(guess_lang_from_fp(path))\n", "    try:\n", "        GlobalParser.parse(bad_doc.text,  path, lang)\n", "    except:\n", "        print(f\"Error parsing {path} with lang {lang}. Id: {doc_id}\")\n", "        node = GlobalParser.parse_ts_tree(bad_doc.text, lang).root_node\n", "        # print(\"Tree-sitter node:\")\n", "        # GlobalParser.print_ts_node(node)\n", "        print(\"Code:\")\n", "        print(bad_doc.text)\n", "        print(\"Parsed:\")\n", "        robust_parser.parse(bad_doc.text, path, lang).pprint()"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.17"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
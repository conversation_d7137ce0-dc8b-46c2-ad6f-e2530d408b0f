# --------------------- Interview Version ------------------------
from __future__ import annotations

from dataclasses import dataclass


def parse_code(code: str) -> Node:
    ...


@dataclass
class Node:
    type: str  # identifier, argument_list, call, assignment, module, etc...
    children: list[Node]
    range: tuple[int, int]  # [start_char, stop_char)
    code: str


# Question 1.2


def find_smallest_node(node: Node, crange: tuple[int, int]) -> Node | None:
    # if the range is outside of node
    if not (node.range[0] <= crange[0] <= crange[1] <= node.range[1]):
        return None
    for child in node.children:
        # if the range is inside any child
        if n := find_smallest_node(child, crange):
            return n
    # current node is the smallest containing node
    return node


# Question 1.3


def build_parent_map_(node: Node, pmap: dict[tuple[int, int], tuple[int, int]]) -> None:
    parent_range = node.range
    for child in node.children:
        child_range = child.range
        if child_range != parent_range:
            pmap[child_range] = parent_range
        build_parent_map_(child, pmap)


def build_parent_map(node: Node):
    pmap = dict()
    build_parent_map_(node, pmap)
    return pmap


# Question 1.4


def expand_cursor_range(code: str, cursor_range: tuple[int, int]) -> tuple[int, int]:
    node = parse_code(code)
    smallest = find_smallest_node(node, cursor_range)
    if smallest is None:
        # keep the selection the same
        return cursor_range
    if smallest.range != cursor_range:
        # already expanded
        return smallest.range

    pmap = build_parent_map(node)
    # try to expand the range if there is a parent
    return pmap.get(smallest.range, smallest.range)


# Question 2.2: remove all comments in a node


@dataclass
class ANode:
    type: str  # identifier, argument_list, call, assignment, module, etc...
    children: list[ANode]
    code: str  # only identifier node has non-empty text


def remove_comments(node: ANode) -> ANode | None:
    if node.type == "comment":
        return None
    new_children = [remove_comments(child) for child in node.children]
    new_children = [c for c in new_children if c is not None]
    return ANode(node.type, new_children, node.code)


# Question 2.2 followup: add keyword sorting
def normalize_node(node: ANode) -> ANode | None:
    if node.type == "comment":
        return None
    new_children = [normalize_node(child) for child in node.children]
    new_children = [c for c in new_children if c is not None]
    new_children = sort_keywords(new_children)
    return ANode(node.type, new_children, node.code)


def sort_keywords(nodes: list[ANode]) -> list[ANode]:
    keywords = [n for n in nodes if n.type == "keyword_argument"]
    non_keywords = [n for n in nodes if n.type != "keyword_argument"]
    keywords.sort(key=lambda n: n.code)
    return non_keywords + keywords


# Question 2.4: find local variables in each scope


def find_local_vars(node: ANode) -> dict[ANode, set[str]]:
    """Build a local name map for each scope."""
    local_vars = dict[ANode, set[str]]()

    def record_defined_names(node: ANode, scope_names: set[str]) -> None:
        """Record locally defined names in each scope."""
        if node.type == "assignment":
            lhs = node.children[0]
            scope_names.add(lhs.code)
        elif node.type == "parameters":
            # add all function parameters as defined names
            for child in node.children:
                scope_names.add(child.code)
        else:
            if node.type == "function_definition":
                scope_names = set()  # introduce a new scope
            local_vars[node] = scope_names
            for child in node.children:
                record_defined_names(child, scope_names)

    record_defined_names(node, set())
    return local_vars


# Question 2.5: rename local variables


def rename_local_vars(node: ANode, local_vars: dict[ANode, set[str]]) -> ANode:
    name_counter = 0

    def rename(node: ANode, name_map: dict[str, str]):
        nonlocal name_counter

        if node.type == "identifier":
            name = node.children[0].code
            assert name is not None
            new_name = name_map.get(name, name)
            return ANode("identifier", [], new_name)

        # update the name_map if this is a new scope
        if node in local_vars:
            name_map = name_map.copy()
            for v in local_vars[node]:
                name_map[v] = f"${name_counter}"
                name_counter += 1

        new_children = [rename(child, name_map) for child in node.children]
        return ANode(node.type, new_children, node.code)

    return rename(node, dict())

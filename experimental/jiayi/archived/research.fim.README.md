
## How to train a new signature system
1. **Updating the change log**: After you have made changes to signature system, go to `SignatureDataprocessor` in [generate_signature_data.py](../../research/utils/generate_signature_data.py) and append your changes to the change log in the doc string. If any change modifies the default data output, also update the `VERSION` constant (which ensures that older versions of the data will not be reused to train the model).
2. **Configuring the data generator**: Go to `make_signature_dataset` in [make_signature_dataset.py](../../experimental/jiayi/finetuning/make_signature_dataset.py), make any necessary modifications to the code to config the `SignatureDataProcessor` object that will be used to generate the training data.
3. **Configuring the training script**: Go to the bottom of [start_signature_finetuning.py](../../experimental/jiayi/finetuning/start_signature_finetuning.py), change `variant` to a short string that summaries the changes you made to the current default setting (or leave it to be `default`.) Also make sure that the `user` parameter is correct so the Determined project will be created under your name.
4. **Training the model**: Run the script from the above step using Python. You may want to do this inside a `tmux` shell since the data generation process can take a few hours (depending on how many CPU cores your machine has). The script will print out a determined link that you can open in your browser to monitor the training.
5. **Downloading the checkpoint**: When training has finished, go to the bottom of [download_checkpoints.py](../../experimental/jiayi/download_checkpoints.py), set `CHECKPOINT_ID` to the UUID of the checkpoint you just trained (which can be found under the "Checkpoints" tab in the Determined UI). Also change `CHECKPOINT_NAME` to a name that the checkpoint will be saved under (this should ideally match the Determined job name, e.g., "v1.11.1-early_lookup_v2-starcoder16b-1000K_python"). Then run this script.

If you want to launch a local server with your changes so you can point the extension to it:

- **Launching a research model server**: Go to [signature_sys_config.yaml](../../research/model_server/configs/signature_sys_config.yaml), change the `model_path` to point to the checkpoint you just trained. Then you should be able to launch a new signature system locally with `python launch_model_server.py --system_yaml_config configs/signature_sys_config.yaml` (assuming you are running this command under `research/model_server/`.)

If you want to run evals on your changes:

- **Evaluating new model on Hydra**: To evaluate the new system on Hydra, go to [start_signature_eval.py](../../experimental/jiayi/evaluation/start_signature_eval.py), change `model_path` to point to the checkpoint you just trained. If you added an option that is off by default, change `system_config` to enable the option. Then you can launch the eval tasks by running this script.
- Once the evals have finished, compare their results (printed in their logs) to those on [this Notion page](https://www.notion.so/Signature-System-Eval-Results-81647e54bf6c40369fac5bdd2ac059c7).

# Launch the signature system as the model server
# python launch_model_server --system_yaml_config configs/signature_sys_config.yaml

name: signature_sys
model:
  name: starcoder_fastforward
  model_path: sig-fim/v1.15-no_docstr_early_lookup-starcoder16b-5langs_500K
generation_options:
  max_generated_tokens: 128 # max tokens in actual output (excluding query tokens)
sig_prompt_formatter:
  max_context_tks: 5000
  suffix_budget_tks: 768
  prefix_budget_tks: 1280
fim_gen_mode: interactive
verbose: True

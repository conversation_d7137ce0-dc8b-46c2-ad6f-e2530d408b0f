"""Systems for static analysis-augmented code completion."""

from __future__ import annotations

import copy
import logging
from dataclasses import dataclass
from pathlib import Path
from typing import Callable, Collection, Sequence, TypeVar

from typing_extensions import assert_never

from base.ranges.line_map import LineMap
from base.static_analysis.parsing import Src<PERSON><PERSON>
from base.static_analysis.signature_index import (
    FileSummaryWithSignatures,
    SignatureQueryMetrics,
    SignatureQueryState,
    UsageToSignaturesMap,
)
from base.static_analysis.signature_utils import SymbolSignature
from research.core.artifacts import artifacts_enabled, post_artifact
from research.core.model_input import ModelInput
from research.core.prompt_formatters import PromptFormatterEnder
from research.core.types import Char<PERSON><PERSON><PERSON>, Document, DocumentId
from research.core.utils import FileLogger
from research.eval.harness.systems.abs_system import (
    CodeCompleteSystem,
    CompletionResult,
    register_system,
)
from research.eval.harness.systems.research_signature_index import (
    ResearchSignatureIndex,
)
from research.fim.fim_prompt import SignatureFimProm<PERSON><PERSON>ormatter
from research.fim.fim_sampling import Fim<PERSON>roblem, SkipOrOutput
from research.models import GenerationOptions
from research.models.fastforward_models import (
    DirectLogitBoost,
    Ender_FastForward,
    StarCoder_FastForward,
)
from research.models.meta_model import ExtraGenerationOutputs, GenerativeLanguageModel
from research.models.starcoder_models import FimGenMode
from research.retrieval.types import DocumentIndex
from research.utils.skip_token import replace_skip_tokens


def fim_problem_from_model_input(
    model_input: ModelInput, middle_range: CharRange | None = None
) -> FimProblem:
    if middle_range is None:
        middle_range = model_input.extra["ground_truth_span"]
        assert middle_range is not None
    start, stop = middle_range.to_tuple()
    path = Path(model_input.path)
    prefix = SrcSpan(
        CharRange(start - len(model_input.prefix), start), model_input.prefix
    )
    suffix = SrcSpan(
        CharRange(stop, stop + len(model_input.suffix)), model_input.suffix
    )
    middle_spans = list[SkipOrOutput[SrcSpan]]()
    if model_input.target:
        middle_spans.append(
            SkipOrOutput(SrcSpan(middle_range, model_input.target), skipped=False)
        )

    return FimProblem(
        prefix=prefix,
        middle_spans=(),
        suffix=suffix,
        file_path=path,
        middle_node_type="[MISSING]",
        original_middle_code="",
    )


@register_system("signature_sys")
@dataclass
class SignatureSystem(CodeCompleteSystem):
    """Signature-augmented code completion system."""

    model: StarCoder_FastForward
    generation_options: GenerationOptions
    signature_index: ResearchSignatureIndex
    sig_prompt_formatter: SignatureFimPromptFormatter
    fim_gen_mode: FimGenMode = FimGenMode.evaluation
    file_logger: FileLogger | None = None
    save_additional_info: bool = False

    def load(self):
        print(f"Loading the system (fim_gen_mode={self.fim_gen_mode.name})...")
        self.model.load()

    def unload(self):
        self.model.unload()

    def supported_languages(self) -> Collection[str]:
        return self.signature_index.supported_langs

    def add_docs(self, src_files: Collection[Document]) -> None:
        self.signature_index.add_docs(src_files)

    def remove_docs(self, doc_ids: Collection[DocumentId]) -> None:
        self.signature_index.remove_docs(doc_ids)

    def clear_retriever(self):
        self.signature_index.remove_all()

    def generate(self, model_input: ModelInput) -> CompletionResult:
        middle_range: CharRange = model_input.extra["ground_truth_span"]
        code = model_input.prefix + model_input.suffix
        input_doc = Document.new(code, model_input.path)

        context_signatures, query_state, query_metrics = _initial_signature_query(
            self.signature_index, input_doc, middle_range
        )

        prob = fim_problem_from_model_input(model_input, middle_range)
        fmt_prob = self.sig_prompt_formatter.format(
            prob,
            context_signatures,
            middle_signatures=dict(),
            format_middle=False,
        )
        prompt: list[int] = fmt_prob.tokens.tolist()
        tkn = self.sig_prompt_formatter.tokenizer
        if prompt[-1] == tkn.special_tokens.eos:
            del prompt[-1]
        if self.file_logger:
            prompt_text = tkn.detokenize(prompt)
            self.file_logger.log("prompt.txt", prompt_text)
        logging.info(f"Symbol counts: {self.signature_index.symbol_counts()}")

        inline_lookups = list[tuple[str, Sequence[SymbolSignature]]]()

        def signature_lookup(symbol_name: str) -> Sequence[SymbolSignature]:
            """Lookup signatures for the given symbol name and record usage bonus."""
            if query_state is None:
                # this is None when document language is not supported by the index
                return []
            sigs, _ = self.signature_index.inline_signature_query(
                symbol_name, query_state
            )
            inline_lookups.append((symbol_name, sigs))
            return sigs

        sig_gen_output = inline_sig_raw_generate(
            signature_lookup,
            self.model,
            prompt,
            self.generation_options,
            self.fim_gen_mode,
        )

        added_prompt = sig_gen_output.prompt[len(prompt) :]
        prediction = tkn.detokenize(sig_gen_output.output_tokens)

        def compute_logging_dict():
            lmap = LineMap(code)
            added_prompt_text = tkn.detokenize(added_prompt)
            try:
                context_sigs_text = (
                    f"Middle range: {_show_cursor_range(lmap, middle_range)}\n"
                    "*Context signatures (from most to least relevant)*\n"
                    + _show_context_signatures(context_signatures, lmap)
                )
            except Exception as e:  # pylint: disable=broad-except
                context_sigs_text = f"ERROR: {e}"

            return {
                "internal_prompt": added_prompt_text,
                "prediction": prediction,
                "context_signatures": context_sigs_text,
                "inline_lookups": [name for name, _ in inline_lookups],
                "metrics": query_metrics,
            }

        if artifacts_enabled():
            post_artifact(compute_logging_dict())

        if self.file_logger:
            for k, v in compute_logging_dict().items():
                self.file_logger.log(f"{k}.txt", str(v))

        if self.save_additional_info:
            additional_info = compute_logging_dict()
        else:
            additional_info = {}

        replace_result = replace_skip_tokens(
            prediction, model_input.suffix, tkn.detokenize([tkn.special_tokens.skip])
        )
        extra_output = ExtraGenerationOutputs(
            prompt_tokens=sig_gen_output.prompt,
            skipped_suffix=replace_result.skipped_suffix,
            suffix_replacement_text=replace_result.suffix_replacement_text,
            additional_info=additional_info,
        )
        return CompletionResult(
            generated_text=replace_result.completion_text,
            prompt_tokens=prompt,
            retrieved_chunks=[],
            extra_output=extra_output,
        )

    def set_log_dir(self, log_dir: Path):
        self.file_logger = FileLogger(log_dir)

    def get_model(self) -> GenerativeLanguageModel:
        return self.model

    @classmethod
    def from_yaml_config(cls, config: dict):
        """An example config can be found at `configs/signature_sys_config.yaml`."""
        from research.eval.harness import factories

        model = factories.create_model(config["model"])
        assert isinstance(model, StarCoder_FastForward)

        generation_options = GenerationOptions(**config.get("generation_options", {}))
        sig_formatter = SignatureFimPromptFormatter(
            **config.get("sig_prompt_formatter", {})
        )
        index = ResearchSignatureIndex.from_yaml_config(
            config.get("index", {}),
        )
        index.set_for_token_budgets(
            prefix_tokens=sig_formatter.prefix_budget_tks,
            suffix_tokens=sig_formatter.suffix_budget_tks,
            signature_tokens=sig_formatter.signature_budget_tks,
        )
        return SignatureSystem(
            model=model,
            generation_options=generation_options,
            signature_index=index,
            sig_prompt_formatter=sig_formatter,
            fim_gen_mode=FimGenMode[config.get("fim_gen_mode", "evaluation")],
        )


@register_system("ender_sys")
@dataclass
class EnderSystem(CodeCompleteSystem):
    """System with signature and dense retrieval."""

    model: Ender_FastForward
    generation_options: GenerationOptions
    signature_index: ResearchSignatureIndex
    dense_index: DocumentIndex
    fim_gen_mode: FimGenMode = FimGenMode.evaluation
    dense_top_k: int = 25
    file_logger: FileLogger | None = None
    save_additional_info: bool = False

    def load(self):
        print(f"Loading the system (fim_gen_mode={self.fim_gen_mode.name})...")
        self.model.load()
        self.dense_index.load()

    def unload(self):
        self.model.unload()
        self.dense_index.unload()

    def supported_languages(self) -> Collection[str]:
        return self.signature_index.supported_langs

    def add_docs(self, src_files: Collection[Document]):
        self.signature_index.add_docs(src_files)
        self.dense_index.add_docs(src_files)

    def remove_docs(self, doc_ids: Collection[DocumentId]):
        self.signature_index.remove_docs(doc_ids)
        self.dense_index.remove_docs(doc_ids)

    def clear_retriever(self):
        self.signature_index.remove_all()
        self.dense_index.remove_all_docs()

    def _retrieve(
        self,
        model_input: ModelInput,
    ) -> ModelInput:
        if model_input.doc_ids is None:
            logging.warning("No doc_ids provided, using all doc_ids.")
        model_input.retrieved_chunks, _ = self.dense_index.query(
            model_input,
            doc_ids=model_input.doc_ids,
            top_k=self.dense_top_k,
        )
        return model_input

    def generate(self, model_input: ModelInput) -> CompletionResult:
        # WARNING(arun): The signature index doesn't support doc_ids, make sure you
        # remove old doc ids before calling this method.

        middle_range: CharRange = model_input.extra["ground_truth_span"]
        code = model_input.prefix + model_input.suffix
        input_doc = Document.new(code, model_input.path)

        context_signatures, query_state, query_metrics = _initial_signature_query(
            self.signature_index, input_doc, middle_range
        )
        signature_chunks = [
            self.signature_index.signature_to_chunk(sig)
            for sigs in context_signatures.values()
            for sig in sigs
        ]

        model_input.extra["signature_chunks"] = signature_chunks
        model_input = self._retrieve(model_input)

        prompt: list[int]
        prompt, _ = self.model.prompt_formatter.prepare_prompt(model_input)

        tkn = self.model.tokenizer
        if prompt[-1] == tkn.eod_id:
            del prompt[-1]
        if self.file_logger:
            prompt_text = tkn.detokenize(prompt)
            self.file_logger.log("prompt.txt", prompt_text)
        logging.info(f"Symbol counts: {self.signature_index.symbol_counts()}")

        inline_lookups = list[tuple[str, Sequence[SymbolSignature]]]()

        def signature_lookup(symbol_name: str) -> Sequence[SymbolSignature]:
            """Lookup signatures for the given symbol name and record usage bonus."""
            if query_state is None:
                return []
            sigs, _ = self.signature_index.inline_signature_query(
                symbol_name, query_state
            )
            inline_lookups.append((symbol_name, sigs))
            return sigs

        sig_gen_output = inline_sig_raw_generate(
            signature_lookup,
            self.model,
            prompt,
            self.generation_options,
            self.fim_gen_mode,
        )

        added_prompt = sig_gen_output.prompt[len(prompt) :]
        prediction = tkn.detokenize(sig_gen_output.output_tokens)

        def compute_logging_dict() -> dict:
            lmap = LineMap(code)
            added_prompt_text = tkn.detokenize(added_prompt)
            try:
                context_sigs_text = (
                    f"Middle range: {_show_cursor_range(lmap, middle_range)}\n"
                    "*Context signatures (from most to least relevant)*\n"
                    + _show_context_signatures(context_signatures, lmap)
                )
            except Exception as e:  # pylint: disable=broad-except
                context_sigs_text = f"ERROR: {e}"

            dense_chunk_text = ("\n" + "~" * 100 + "\n").join(
                chunk.text for chunk in model_input.retrieved_chunks
            )

            return {
                "internal_prompt": added_prompt_text,
                "prediction": prediction,
                "context_signatures": context_sigs_text,
                "dense_chunks": dense_chunk_text,
                "inline_lookups": [name for name, _ in inline_lookups],
                "metrics": query_metrics,
            }

        if artifacts_enabled():
            post_artifact(compute_logging_dict())

        if self.file_logger:
            for k, v in compute_logging_dict().items():
                self.file_logger.log(f"{k}.txt", str(v))

        if self.save_additional_info:
            additional_info = compute_logging_dict()
        else:
            additional_info = {}

        replace_result = replace_skip_tokens(
            prediction, model_input.suffix, tkn.skip_token
        )
        extra_output = ExtraGenerationOutputs(
            prompt_tokens=sig_gen_output.prompt,
            skipped_suffix=replace_result.skipped_suffix,
            suffix_replacement_text=replace_result.suffix_replacement_text,
            additional_info=additional_info,
        )
        return CompletionResult(
            generated_text=replace_result.completion_text,
            prompt_tokens=prompt,
            retrieved_chunks=[],
            extra_output=extra_output,
        )

    def get_model(self) -> GenerativeLanguageModel:
        return self.model

    def set_log_dir(self, log_dir: Path):
        self.file_logger = FileLogger(log_dir)

    @classmethod
    def from_yaml_config(cls, config: dict):
        """An example config can be found at `configs/signature_sys_config.yaml`."""
        from research.eval.harness import factories

        model = factories.create_model(config["model"])
        assert isinstance(model, Ender_FastForward)

        generation_options = GenerationOptions(**config.get("generation_options", {}))
        sig_index_config = config.get("signature_index", {})
        sig_index = ResearchSignatureIndex.from_yaml_config(sig_index_config)
        formatter = model.prompt_formatter
        assert isinstance(formatter, PromptFormatterEnder)
        # automatically set limits to ensure correctness
        if "est_prefix_chars" not in sig_index_config:
            sig_index.est_prefix_chars = formatter.max_prefix_tokens * 3
        if "est_suffix_chars" not in sig_index_config:
            sig_index.est_suffix_chars = formatter.max_suffix_tokens * 3
        if "max_ctx_signature_chars" not in sig_index_config:
            # The prompt formatter will truncate to the exact token boundary,
            # so it is safe to over-generate signatures.  This lets us avoid
            # worrying about characters vs tokens in SignatureIndex.
            # Larger values here might let us slip in extra signatures at the
            # cost of doing extra tokenization.
            sig_index.max_ctx_signature_chars = formatter.max_signature_tokens * 10

        dense_index = factories.create_retriever(config["dense_retriever"])

        return EnderSystem(
            model=model,
            generation_options=generation_options,
            signature_index=sig_index,
            dense_index=dense_index,
            fim_gen_mode=FimGenMode[config.get("fim_gen_mode", "evaluation")],
            dense_top_k=config.get("dense_top_k", cls.dense_top_k),
        )


def _initial_signature_query(
    signature_index: ResearchSignatureIndex, doc: Document, middle_range: CharRange
) -> tuple[UsageToSignaturesMap, SignatureQueryState | None, SignatureQueryMetrics]:
    """Try to get the signature chunks and query state for the given document.

    Returns (context_signatures, query_state | None)
    """
    if signature_index.is_document_supported(doc):
        pfile = signature_index.parse_doc(doc, robust_parsing=True)
        fss = FileSummaryWithSignatures.from_pfile(
            pfile, signature_index.sig_printer, show_private=True
        )
        signature_index.update_file(fss)
        cursor_location = middle_range.start
        est_prompt_range = CharRange(
            max(0, cursor_location - signature_index.est_prefix_chars),
            cursor_location + signature_index.est_suffix_chars,
        )
        result = signature_index.get_context_signatures(
            fss.summary, cursor_location, est_prompt_range
        )
        # Update the cache to avoid showing private symbols.
        fss = FileSummaryWithSignatures.from_pfile(
            pfile, signature_index.sig_printer, show_private=False
        )
        signature_index.update_file(fss)
        return result.ctx_signatures, result.state, result.metrics
    else:
        logging.warning(f"No signature support for file: {doc.path}")
        return dict(), None, SignatureQueryMetrics.empty()


@dataclass
class InlineSigGenOutput:
    """The output of inline signature generation."""

    output_tokens: list[int]
    """The tokens output to the user."""

    prompt: list[int]
    """The final prompt tokens the model sees."""


def inline_sig_raw_generate(
    signature_lookup: Callable[[str], Sequence[SymbolSignature]],
    model: StarCoder_FastForward,
    prompt: list[int],
    generation_options: GenerationOptions,
    fim_gen_mode: FimGenMode,
    max_lookups_per_pause: int = 4,
) -> InlineSigGenOutput:
    """Generate tokens with inline signature retrieval."""
    assert fim_gen_mode != FimGenMode.raw, "raw mode not implemented."
    prompt = prompt.copy()  # tokens internally seen by the model
    output_tks = list[int]()  # tokens output to the user

    def get_max_output_tks() -> int:
        """Get the max_output_tokens limit for the next raw_generate_tokens call."""
        max_output_tks = model.seq_length - len(prompt)
        if generation_options.max_generated_tokens is not None:
            max_output_tks = min(
                max_output_tks,
                generation_options.max_generated_tokens - len(output_tks),
            )
        return max_output_tks

    tkn = model.tokenizer
    if fim_gen_mode == FimGenMode.interactive:
        fim_stop_tokens = {tkn.eod_id, tkn.pad_id, tkn.pause_id}
    elif fim_gen_mode == FimGenMode.evaluation:
        fim_stop_tokens = {tkn.eod_id, tkn.pad_id, tkn.skip_id}
    else:
        assert_never(fim_gen_mode)

    model.stop_tokens = fim_stop_tokens | {tkn.sig_begin_id}
    inline_lookup_counts = 0
    origin_token_boosters = model.token_boosters

    while (max_output_tks := get_max_output_tks()) > 0:
        gen_opt = copy.copy(generation_options)
        gen_opt.max_generated_tokens = max_output_tks
        tokens = model.raw_generate_tokens(prompt, gen_opt).tokens
        if not tokens:
            logging.warning(
                "No tokens generated by model."
                f"{max_output_tks=}, {len(prompt)=}"
                f"Prompt: {tkn.detokenize(prompt)}"
            )
            break
        # find the last index of tkn.sig_lookup_id in tokens
        query_i = _find_last(tokens, tkn.sig_lookup_id)

        last_tk = tokens[-1]
        # Model issues sig lookup queries as <lookup_id>query tokens<sig_begin_id>.
        # <sig_begin_id> is a stop token, so we can check for a signature lookup query
        # by checking if the last token is <sig_begin_id>.
        if last_tk == tkn.sig_begin_id:
            # if the query is finished, do inline retrieval to add the
            # signatures to the prompt
            if query_i is None:
                logging.warning(
                    f"No signature query found in: {tkn.detokenize(tokens)}"
                )
                return InlineSigGenOutput(output_tokens=output_tks, prompt=prompt)

            tks_before_query = tokens[:query_i]
            output_tks.extend(tks_before_query)
            symbol_name = tkn.detokenize(tokens[query_i + 1 : -1])

            sigs_found = signature_lookup(symbol_name)
            sigs_text = SignatureFimPromptFormatter.show_signatures(sigs_found)
            sig_tks = tkn.tokenize(sigs_text)

            more_prompt = [
                *tokens,
                *sig_tks,
                tkn.sig_end_id,
            ]
            prompt.extend(more_prompt)

            if tkn.pause_id in tks_before_query:
                # this is a new pause span, reset the inline lookup counter
                inline_lookup_counts = 0
                model.token_boosters = origin_token_boosters

            inline_lookup_counts += 1
            if inline_lookup_counts >= max_lookups_per_pause:
                # try to turn off inline lookups
                lookup_boost = DirectLogitBoost(tkn.sig_lookup_id, -100000.0)
                model.token_boosters = (lookup_boost, *origin_token_boosters)
                logging.warning(
                    f"Too many inline lookups: {inline_lookup_counts}. Turning it off."
                )
        else:
            prompt.extend(tokens)
            if last_tk in fim_stop_tokens:
                output_tks.extend(tokens)
            else:
                # If we run out of budget mid-query, drop tokens from unfinished query
                output_tks.extend(tokens[:query_i])
            break

    model.token_boosters = origin_token_boosters

    if (
        fim_gen_mode == FimGenMode.evaluation
        and output_tks
        and output_tks[-1] not in fim_stop_tokens
    ):
        # not finished, so we try to backtrack to the last pause token
        if (last_pause := _find_last(output_tks, tkn.pause_id)) is not None:
            output_tks = output_tks[:last_pause]

    # clean up any stop or pause tokens
    tokens_to_remove = fim_stop_tokens | {tkn.pause_id}
    output_tks = [tk for tk in output_tks if tk not in tokens_to_remove]

    return InlineSigGenOutput(output_tokens=output_tks, prompt=prompt)


def _show_context_signatures(context_signatures: UsageToSignaturesMap, lmap: LineMap):
    """Show the context signatures in a human readable format."""
    lines = list[str]()
    for u, sigs in context_signatures.items():
        lines.append("-" * 100)
        lines.append(
            f"name={repr(u.name)}, kind={u.kind}, "
            f"site={_show_cursor_range(lmap, u.use_site)}"
        )
        for sig in sigs:
            lines.append(f"Defined at: {sig.path}[{sig.crange})]")
            lines.append(sig.text)
            lines.append("")
        lines.append("")
    return "\n".join(lines)


def _show_cursor_range(lmap: LineMap, crange: CharRange) -> str:
    """Show the character range as 1-based (line, column) range."""
    start = lmap.get_line_column(crange.start)
    stop = lmap.get_line_column(crange.stop)
    return f"({start[0]+1},{start[1]+1}):({stop[0]+1},{stop[1]+1})"


AT = TypeVar("AT")


def _find_last(xs: Sequence[AT], element: AT) -> int | None:
    """Find the last index of `element` in `xs`."""
    for i in range(len(xs) - 1, -1, -1):
        if xs[i] == element:
            return i
    return None

{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["This Notebook is used to inspect the sampled change order on a given file change."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.core.constants import AUGMENT_ROOT\n", "from research.utils.repo_change_utils import get_commit_history, iterate_repo_history\n", "\n", "\n", "n_commits = 2\n", "repo_path = AUGMENT_ROOT\n", "commit_history = get_commit_history(repo_path, n_commits)\n", "repo_changes = list(iterate_repo_history(repo_path, commit_history, max_workers=8))\n", "head_repo = repo_changes[-1].after_files"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "\n", "from research.core.str_diff import StrDiff\n", "\n", "# point this path to a changed file\n", "file_name = Path(\"research/next_edits/edit_gen_sampler.py\")\n", "file_current = (repo_path / file_name).read_text()\n", "file_head = head_repo[file_name]\n", "file_diff = StrDiff.build(file_head, file_current)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.ranges.line_map import LineMap\n", "\n", "\n", "def visualize_change_order(diff: StrDiff, change_order: list[int]):\n", "    lmap_before = LineMap(diff.get_before())\n", "    lmap_after = LineMap(diff.get_after())\n", "\n", "    for i, span_idx in enumerate(change_order):\n", "        before_char = diff.span_ranges_in_before[span_idx].start\n", "        before_line = lmap_before.get_line_number(before_char) + 1\n", "        after_char = diff.span_ranges_in_after[span_idx].start\n", "        after_line = lmap_after.get_line_number(after_char) + 1\n", "        print(f\"[{i}] -{before_line:4}, +{after_line:4}:\")\n", "        span = diff.spans[span_idx]\n", "        print(f\"    before={repr(span.before)}\")\n", "        print(f\"    after ={repr(span.after)}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.core.str_diff import print_diff\n", "\n", "\n", "print_diff(file_diff)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from random import Random\n", "from research.next_edits.wip_repo_sampler import sample_change_order\n", "\n", "rng = Random(42)\n", "change_order = sample_change_order(rng, file_diff, \"Python\")\n", "visualize_change_order(file_diff, change_order)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
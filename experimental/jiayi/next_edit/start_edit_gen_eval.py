"""<PERSON><PERSON><PERSON> to run all edit generation evaluation jobs.

This script should be manually edited and run to launch all the evaluation jobs.
"""

from pathlib import Path
from typing import Literal, assert_never

from base.prompt_format_next_edit.gen_prompt_formatter import (
    default_section_budgets,
    section_budgets_13k,
    section_budgets_10k,
)
from experimental.jiayi.finetuning.training_config_utils import (
    NextEditModelArc,
    Qwen25CoderArc,
    StarCoder2Arc,
)
from experimental.jiayi.evaluation.eval_config_tools import launch_eval
from research.data.spark.pipelines.stages.next_edit_gen_pipelines import (
    raven_retriever_config,
)

model_arc: NextEditModelArc = StarCoder2Arc(size="15b")
checkpoint_path = Path(
    "/mnt/efs/augment/checkpoints/"
    "next-edit-gen/S28_wip0.6_small1000-R4_ethanol-P21_star2_seq12k_pause500_out600-pr_grouped_10k-starcoder2_15b-ffw"
)
use_retrieval = True
# the context length that should be used during eval
context_length: Literal["8k", "12k", "16k"] = "12k"


if isinstance(model_arc, StarCoder2Arc):
    model_config = {
        "name": "starcoder2_fastforward",
        "model_path": str(checkpoint_path),
    }
elif isinstance(model_arc, Qwen25CoderArc):
    model_config = {
        "name": f"fastforward_qwen25coder_{model_arc.size}",
        "checkpoint_path": str(checkpoint_path),
    }
else:
    assert_never(model_arc)

if context_length == "16k":
    formatter_config = dict(
        diff_context_lines=9,
        max_prompt_tokens=13_200,
        section_budgets=section_budgets_13k(),
    )
elif context_length == "12k":
    formatter_config = dict(
        diff_context_lines=9,
        max_prompt_tokens=10_200,
        section_budgets=section_budgets_10k(),
    )
elif context_length == "8k":
    formatter_config = dict(
        diff_context_lines=9,
        max_prompt_tokens=6_800,
        section_budgets=default_section_budgets(),
    )

system_config = {
    "name": "next_edit_gen",
    "model": model_config,
    "generation_options": {"max_generated_tokens": 600},
    "retriever": raven_retriever_config,
    "prompt_formatter": formatter_config,
}
if not use_retrieval:
    system_config.pop("retriever", None)


if __name__ == "__main__":
    tasks = [
        {
            "name": "next_edit_gen",
            "dataset_path": dataset_path,
            "must_give_change": True,
            "limit_examples": 1000,
        }
        for dataset_path in [
            "/mnt/efs/augment/data/eval/next_edits/manual.v3.jsonl.zst",
            "/mnt/efs/augment/data/eval/next_edits/augment_v2_pr6835_uniform.pkl",
            "/mnt/efs/augment/data/eval/next_edits/prs.v7.jsonl.zst",
        ]
    ]
    tasks.append(
        {
            "name": "next_edit_classification",
            "dataset_path": "/mnt/efs/augment/data/eval/next_edits/augment_classification_v1.pkl",
            "limit_examples": 1000,
            "use_instruction": False,
        }
    )
    for task_config in tasks:
        model_name = checkpoint_path.name
        dataset_name = Path(task_config["dataset_path"]).name
        determined_name = f"{dataset_name}, {model_name}"

        launch_eval(determined_name, system_config, task_config, "1xH100.yaml")

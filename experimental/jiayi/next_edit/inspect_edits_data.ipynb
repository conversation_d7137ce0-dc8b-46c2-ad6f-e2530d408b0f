{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "import pandas as pd\n", "\n", "from research.fim.fim_prompt import TokenArray\n", "from research.utils.token_array_utils import load_indexed_dataset\n", "\n", "parquets_to_read = 100\n", "dataset_dir = Path(\n", "    \"/mnt/efs/spark-data/shared/next-edit/stage3/prv2-pr_grouped_10k/S25_16000p,R4_ethanol-K128,P18_star2_diff12_seq12k\"\n", ")\n", "parquet_files = [path for path in dataset_dir.glob(\"*.parquet\")]\n", "parquet_files = parquet_files[:parquets_to_read]\n", "\n", "# read these as a pandas dataframe\n", "df = pd.concat([pd.read_parquet(parquet_file) for parquet_file in parquet_files])\n", "df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pickle\n", "from research.next_edits.edit_gen_stages import FormattedEditGenProblem\n", "\n", "# read the \"pickled_results\" column and unpickle the results\n", "\n", "results: list[FormattedEditGenProblem] = []\n", "for row in df[\"pickled_results\"]:\n", "    results.extend(pickle.loads(row))\n", "\n", "print(\"number of examples:\", len(results))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def has_recency_diff(result: FormattedEditGenProblem) -> bool:\n", "    if \"simulate_wip_repo_states_with_order\" not in result.debug_info:\n", "        return False\n", "    info = result.debug_info[\"simulate_wip_repo_states_with_order\"]\n", "    return info.get(\"simulated_granular_diffs\", 0) > 0\n", "\n", "\n", "def has_squashes(result: FormattedEditGenProblem) -> bool:\n", "    if \"simulate_wip_repo_states_with_order\" not in result.debug_info:\n", "        return False\n", "    info = result.debug_info[\"simulate_wip_repo_states_with_order\"]\n", "    return info.get(\"num_squashes\", 0) > 0\n", "\n", "\n", "recency_examples = [result for result in results if has_recency_diff(result)]\n", "print(f\"number of examples with recency diffs: {len(recency_examples)}\")\n", "squash_examples = [result for result in results if has_squashes(result)]\n", "print(f\"number of examples with squashes: {len(squash_examples)}\")\n", "pr_metas = {result.pr_meta for result in results}\n", "print(f\"number of PRs: {len(pr_metas)}\")\n", "repos = {meta.repo_name for meta in pr_metas if meta}\n", "print(f\"number of repos: {len(repos)}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "from base.tokenizers.starcoder2_tokenizer import StarCoder2Tokenizer\n", "\n", "\n", "tokenizer = StarCoder2Tokenizer()\n", "pause_id = tokenizer.special_tokens.pause\n", "fim_mid_id = tokenizer.special_tokens.fim_middle\n", "has_change_id = tokenizer.special_tokens.has_change\n", "\n", "\n", "def output_has_pause(tokens: TokenArray) -> bool:\n", "    tokens = np.abs(tokens)\n", "    fim_mid_idx = int(np.where(tokens == fim_mid_id)[0][0])\n", "    output_tokens = tokens[fim_mid_idx + 1 :]\n", "    return pause_id in output_tokens\n", "\n", "\n", "def output_has_change(result: FormattedEditGenProblem) -> bool:\n", "    tokens = np.abs(result.tokens)\n", "    return has_change_id in tokens\n", "\n", "\n", "def get_output_tokens(result: FormattedEditGenProblem) -> int:\n", "    tokens = np.abs(result.tokens)\n", "    fim_mid_idx = int(np.where(tokens == fim_mid_id)[0][0])\n", "    return len(tokens[fim_mid_idx + 1 :])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["interesting_results = [result for result in results if output_has_change(result)]\n", "# interesting_results = [result for result in results if has_recency_diff(result)]\n", "\n", "interesting_results.sort(key=get_output_tokens, reverse=True)\n", "pause_ratio = len(interesting_results) / len(results)\n", "print(f\"number of interesting examples: {len(interesting_results)} ({pause_ratio:.1%})\")\n", "interesting_results = [\n", "    result\n", "    for result in interesting_results\n", "    if \"format_as_tokens\" not in result.debug_info\n", "    or \"drop_instruction\" not in result.debug_info[\"format_as_tokens\"]\n", "]\n", "print(\"number of examples with instruction:\", len(interesting_results))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.utils.inspect_indexed_dataset import detokenize_with_masked_tokens\n", "from research.utils.notebook_uis.notebook_uis import pprint_dict\n", "\n", "\n", "ex_id = 20\n", "example = interesting_results[ex_id]\n", "\n", "\n", "print(pprint_dict(example.debug_info))\n", "print(\"~=\" * 50)\n", "print(detokenize_with_masked_tokens(example.tokens, tokenizer))"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
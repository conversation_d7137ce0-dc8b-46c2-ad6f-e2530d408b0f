{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "\n", "from research.data.spark.pipelines.stages.next_edit_grouping_pipelines import (\n", "    load_grouped_changes,\n", ")\n", "\n", "data_path = Path(\"/mnt/efs/spark-data/shared/next-edit-grouping/G1.0_hunks.lt30.v2\")\n", "pr_to_grouped_changes = load_grouped_changes(data_path)\n", "all_grouped_changes = list(pr_to_grouped_changes.values())\n", "print(f\"Total number of groupped repo changes: {len(all_grouped_changes)}.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["all_grouped_changes[1].pprint()"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
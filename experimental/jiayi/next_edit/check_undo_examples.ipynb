{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "import pandas as pd\n", "\n", "from research.fim.fim_prompt import TokenArray\n", "from research.utils.token_array_utils import load_indexed_dataset\n", "\n", "parquets_to_read = 30\n", "dataset_dir = Path(\n", "    \"/mnt/efs/spark-data/shared/next-edit/stage1/prv2-pr_grouped_10k/S25_16000p\"\n", ")\n", "parquet_files = [path for path in dataset_dir.glob(\"*.parquet\")]\n", "parquet_files = parquet_files[:parquets_to_read]\n", "\n", "# read these as a pandas dataframe\n", "df = pd.concat([pd.read_parquet(parquet_file) for parquet_file in parquet_files])\n", "df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pickle\n", "from research.next_edits.edit_gen_stages import EditGenProblem\n", "\n", "# read the \"pickled_results\" column and unpickle the results\n", "\n", "results: list[EditGenProblem] = []\n", "for row in df[\"pickled_results\"]:\n", "    results.extend(pickle.loads(row))\n", "\n", "print(\"number of examples:\", len(results))\n", "results = results[:20_000]\n", "print(f\"using only {len(results)} examples.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from tqdm import tqdm\n", "from research.utils.repo_change_utils import squash_file_changes\n", "from services.next_edit_host.server.post_processing import is_exact_undo_recent_changes\n", "\n", "\n", "def label_is_undo(result: EditGenProblem) -> bool:\n", "    # if result.debug_info.get(\"n_squashes\", 0) > 0:\n", "    #     return False  # only check when there's no squash\n", "    changed_files = [\n", "        c.map(lambda x: x.to_file()) for c in result.repo_change.changed_files\n", "    ]\n", "    last_change = changed_files[-1:]\n", "    last_is_undo = is_exact_undo_recent_changes(\n", "        recent_changes=last_change,\n", "        selection_range=result.edit_region,\n", "        existing_code=result.selected_code,\n", "        suggested_code=result.output.replacement,\n", "        current_path=str(result.current_path),\n", "    )\n", "    if last_is_undo:\n", "        return True\n", "    try:\n", "        squashed_change_files = [\n", "            c.map(lambda x: x.to_file())\n", "            for c in squash_file_changes(result.repo_change.changed_files)\n", "        ]\n", "    except AssertionError:\n", "        return False  # couldn't check\n", "    return is_exact_undo_recent_changes(\n", "        recent_changes=squashed_change_files,\n", "        selection_range=result.edit_region,\n", "        existing_code=result.selected_code,\n", "        suggested_code=result.output.replacement,\n", "        current_path=str(result.current_path),\n", "    )\n", "\n", "\n", "undo_examples = [result for result in tqdm(results) if label_is_undo(result)]\n", "print(f\"number of examples with undo label: {len(undo_examples)}\")\n", "print(f\"Ratio of undo examples: {len(undo_examples) / len(results):%}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.diff_utils.diff_formatter import format_file_changes\n", "from base.diff_utils.diff_utils import File, compute_file_diff\n", "from research.utils.notebook_uis.notebook_uis import pprint_dict\n", "\n", "\n", "ex_id = 4\n", "example = undo_examples[ex_id]\n", "\n", "\n", "print(pprint_dict(example.debug_info))\n", "print(\"~=\" * 50)\n", "for change in example.repo_change.changed_files:\n", "    diff = format_file_changes(\n", "        [change.map(lambda x: x.to_file())], diff_context_lines=3\n", "    )\n", "    print(diff)\n", "\n", "print(\"~=\" * 50)\n", "print(\"Current path:\", example.current_path)\n", "label_diff = compute_file_diff(\n", "    before_file=File(str(example.current_path), example.selected_code),\n", "    after_file=File(str(example.current_path), example.output.replacement),\n", ")\n", "print(label_diff)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
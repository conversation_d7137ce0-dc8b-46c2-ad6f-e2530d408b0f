"""<PERSON><PERSON><PERSON> to convert GroupedRepoChanges to OrderedRepoChanges."""

from pathlib import Path
from research.data.spark.pipelines.stages.next_edit_grouping_pipelines import (
    grouped_to_ordered_repo_changes_wrapper,
)
from research.data.spark.pipelines.stages.next_edit_gen_pipelines import get_session
import research.data.spark.utils as spark_utils
from research.model_server.server_logging import configure_server_logging


def run_conversion():
    """Run the conversion."""
    configure_server_logging("INFO")  # for nicer logging
    spark_utils.logger.setLevel("WARNING")

    with get_session(use_gpu=False, max_workers=32) as spark:
        output_path = Path(
            "/mnt/efs/spark-data/shared/next-edit/stage0/ordered-gh_pr_train_repartitioned/G1.0_hunks.lt30.v2"
        )
        grouped_to_ordered_repo_changes_wrapper(
            repo_change_dir="/mnt/efs/spark-data/shared/next-edit/stage0/gh_pr_train_repartitioned/repo_changes",
            grouping_data_dir="/mnt/efs/spark-data/shared/next-edit-grouping/G1.0_hunks.lt30.v2",
            output_path=output_path,
            task_info_location=output_path / "logs",
            spark=spark,
            repos_per_file=20,
        )


if __name__ == "__main__":
    run_conversion()

{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["This Notebook uses in-memory blob caches to help you quickly look up the Hindsight data from recent edit history."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.datasets.hindsight_next_edit_intermediate_dataset import (\n", "    NextEditIntermediateType,\n", "    NextEditRawDataQueryArgs,\n", "    NextEditIntermediateErrorDetails,\n", ")\n", "from experimental.jiayi.next_edit.download_hindsight_data import (\n", "    IntermediateDataDownloader,\n", ")\n", "from datetime import datetime, timedelta\n", "import pytz\n", "\n", "\n", "california_tz = pytz.timezone(\"America/Los_Angeles\")\n", "\n", "\n", "all_data: list[NextEditIntermediateType]\n", "error_collector: NextEditIntermediateErrorDetails\n", "downloader = IntermediateDataDownloader()  # Blobs are cached inside this object"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query_args = NextEditRawDataQueryArgs(\n", "    min_request_interval=timedelta(seconds=10), user_ids=[\"jiayi\"]\n", ")\n", "end_time = california_tz.localize(datetime.fromisoformat(\"2025-01-27T14:40:00.0\"))\n", "total_duration = timed<PERSON>ta(hours=3)\n", "\n", "all_data, error_collector = downloader.download_intermediate_data(\n", "    end_time,\n", "    total_duration,\n", "    future_events_duration=timedelta(hours=1),\n", "    query_args=query_args,\n", ")\n", "print(error_collector.summary())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["error_collector.some_edited_files_not_found"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import collections\n", "from research.next_edits.next_hunk_heuristic import (\n", "    HeuristicStateRecorder,\n", "    NextHunkHeuristic,\n", "    HeuristicFailed,\n", ")\n", "from tqdm import tqdm\n", "\n", "heuristic = NextHunkHeuristic()\n", "successful_examples = list[tuple[int, HeuristicStateRecorder]]()\n", "errors = list[tuple[int, HeuristicFailed]]()\n", "\n", "for i, datum in enumerate(tqdm(all_data, smoothing=0)):\n", "    analyzer = HeuristicStateRecorder(heuristic.args, datum)\n", "    try:\n", "        if heuristic.get_gold_edits(datum, analyzer) is not None:\n", "            successful_examples.append((i, analyzer))\n", "    except HeuristicFailed as e:\n", "        errors.append((i, e))\n", "\n", "print(f\"{len(successful_examples)=}\")\n", "print(f\"A total of {len(errors)} ({len(errors) / len(all_data):.1%}) examples failed.\")\n", "print(\"Top errors:\")\n", "counter = collections.Counter([e.category for _, e in errors])\n", "for category, count in counter.most_common(10):\n", "    print(f\"\\t{category}: {count}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.utils.notebook_uis.edit_gen import render_next_hunk_heuristic_analyzer\n", "from research.utils.notebook_uis.notebook_uis import render_item_browser\n", "\n", "\n", "def inspect_example(i: int):\n", "    _, analyzer = successful_examples[i]\n", "    return render_next_hunk_heuristic_analyzer(analyzer)\n", "\n", "\n", "render_item_browser(len(successful_examples), inspect_example, initial_item=0)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
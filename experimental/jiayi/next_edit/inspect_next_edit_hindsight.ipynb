{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "import pickle\n", "from base.datasets.hindsight_next_edit_intermediate_dataset import (\n", "    NextEditIntermediateType,\n", "    NextEditRawDataQueryArgs,\n", "    NextEditIntermediateErrorDetails,\n", ")\n", "from experimental.jiayi.next_edit.download_hindsight_data import (\n", "    IntermediateDataDownloader,\n", ")\n", "from datetime import datetime, timedelta\n", "import pytz\n", "\n", "\n", "california_tz = pytz.timezone(\"America/Los_Angeles\")\n", "end_time = california_tz.localize(datetime.fromisoformat(\"2025-01-22T23:00:00.0\"))\n", "total_duration = timed<PERSON>ta(hours=5)\n", "dataset_name = \"dogfood_2025_01_22_5hours_10s\"\n", "\n", "\n", "result_tmp_file = Path(f\"~/tmp/{dataset_name}.pkl\").expanduser()\n", "result_tmp_file.parent.mkdir(parents=True, exist_ok=True)\n", "\n", "result_tmp_file = Path(f\"~/tmp/{dataset_name}.pkl\").expanduser()\n", "\n", "all_data: list[NextEditIntermediateType]\n", "error_collector: NextEditIntermediateErrorDetails\n", "\n", "if result_tmp_file.exists():\n", "    print(f\"loading results from file: {result_tmp_file}\")\n", "    all_data, error_collector = pickle.load(open(result_tmp_file, \"rb\"))\n", "else:\n", "    all_data, error_collector = IntermediateDataDownloader().download_intermediate_data(\n", "        end_time,\n", "        total_duration,\n", "        future_events_duration=timedelta(hours=1),\n", "        query_args=NextEditRawDataQueryArgs(min_request_interval=timedelta(seconds=10)),\n", "    )\n", "    print(error_collector.summary())\n", "    print(f\"Saving results to {result_tmp_file}\")\n", "    with open(result_tmp_file, \"wb\") as f:\n", "        pickle.dump((all_data, error_collector), f)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(f\"File size: {result_tmp_file.stat().st_size / 2**30:.2f} GB\")\n", "print(f\"Num examples: {len(all_data)}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.utils.notebook_uis.edit_gen import display_next_edit_intermediate_problems\n", "\n", "\n", "display_next_edit_intermediate_problems(all_data)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Running The Ground Truth Heuristic"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import collections\n", "from research.next_edits.next_hunk_heuristic import (\n", "    HeuristicStateRecorder,\n", "    NextHunkHeuristic,\n", "    HeuristicFailed,\n", ")\n", "from tqdm import tqdm\n", "\n", "heuristic = NextHunkHeuristic()\n", "successful_examples = list[tuple[int, HeuristicStateRecorder]]()\n", "errors = list[tuple[int, HeuristicFailed]]()\n", "\n", "for i, datum in enumerate(tqdm(all_data, smoothing=0)):\n", "    analyzer = HeuristicStateRecorder(heuristic.args, datum)\n", "    try:\n", "        if heuristic.get_gold_edits(datum, analyzer) is not None:\n", "            successful_examples.append((i, analyzer))\n", "    except HeuristicFailed as e:\n", "        errors.append((i, e))\n", "\n", "print(f\"{len(successful_examples)=}\")\n", "print(f\"A total of {len(errors)} ({len(errors) / len(all_data):.1%}) examples failed.\")\n", "print(\"Top errors:\")\n", "counter = collections.Counter([e.category for _, e in errors])\n", "for category, count in counter.most_common(10):\n", "    print(f\"\\t{category}: {count}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from collections import Counter\n", "\n", "user_data_counter = Counter(\n", "    ex.datum.future_events[0].user_id for _, ex in successful_examples\n", ")\n", "for user, count in user_data_counter.most_common():\n", "    print(f\"{user}: {count}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.utils.notebook_uis.edit_gen import render_next_hunk_heuristic_analyzer\n", "from research.utils.notebook_uis.notebook_uis import render_item_browser\n", "\n", "\n", "def inspect_example(i: int):\n", "    _, analyzer = successful_examples[i]\n", "    return render_next_hunk_heuristic_analyzer(analyzer)\n", "\n", "\n", "# the gap between example 104 and 105 is suspicious\n", "# (<EMAIL> at 2025-01-22 09:09:36)\n", "\n", "render_item_browser(len(successful_examples), inspect_example, initial_item=0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# pickle the result\n", "\n", "import pickle\n", "\n", "from base.datasets.tenants import DOGFOOD_SHARD\n", "from base.datasets.hindsight_next_edit_intermediate_dataset import (\n", "    DatasetWithGroundTruths,\n", ")\n", "\n", "result_data = [\n", "    (analyzer.datum, analyzer.ground_truths[0]) for _, analyzer in successful_examples\n", "]\n", "result_dataset = DatasetWithGroundTruths(DOGFOOD_SHARD.name, result_data)\n", "\n", "save_name = result_tmp_file.with_name(\"next_hunk-\" + result_tmp_file.name)\n", "\n", "with open(save_name, \"wb\") as f:\n", "    pickle.dump(result_dataset, f)\n", "\n", "# print out the saved file size\n", "print(f\"File saved to: {save_name}\")\n", "print(f\"File size: {save_name.stat().st_size / 2**30:.2f} GB\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# example to load the data\n", "import pickle\n", "from base.datasets.hindsight_next_edit_intermediate_dataset import (\n", "    DatasetWithGroundTruths,\n", ")\n", "from pathlib import Path\n", "\n", "result_tmp_file = Path(f\"~/tmp/{dataset_name}.pkl\").expanduser()\n", "result_tmp_file.parent.mkdir(parents=True, exist_ok=True)\n", "\n", "data_path = Path(f\"~/tmp/{dataset_name}.pkl\").expanduser()\n", "print(f\"File size: {data_path.stat().st_size / 2**30:.2f} GB\")\n", "with open(data_path, \"rb\") as f:\n", "    result_dataset: DatasetWithGroundTruths = pickle.load(f)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Debug script - ok if fails"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.datasets.hindsight_next_edit import NextEditIntermediateType\n", "from research.next_edits.next_hunk_heuristic import HeuristicArgs\n", "\n", "\n", "def find_datum_by_request_id(request_id: str) -> NextEditIntermediateType:\n", "    for datum in all_data:\n", "        if datum.request.request_id == request_id:\n", "            return datum\n", "    raise ValueError(f\"Request ID {request_id} not found in all_data\")\n", "\n", "\n", "datum = find_datum_by_request_id(\"18dd665d-efab-4922-8648-0495746024dd\")\n", "\n", "heuristic_to_eval = NextHunkHeuristic(HeuristicArgs(max_active_hunks=100))\n", "analyzer = HeuristicStateRecorder(heuristic_to_eval.args, datum)\n", "try:\n", "    heuristic_to_eval.get_gold_edits(datum, analyzer)\n", "except HeuristicFailed:\n", "    # print out the stack trace\n", "    import traceback\n", "\n", "    traceback.print_exc()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["render_next_hunk_heuristic_analyzer(analyzer)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
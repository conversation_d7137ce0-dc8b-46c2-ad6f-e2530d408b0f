from pathlib import Path
import subprocess
from typing import assert_never
import json

from research.core.constants import AUGMENT_CHECKPOINTS_ROOT
from experimental.jiayi.finetuning.training_config_utils import (
    NextEditModelArc,
    Qwen25CoderArc,
    StarCoder2Arc,
)


def run_quantization(
    model_arc: NextEditModelArc, ckpt_path: Path, calibration_data_dir: Path
) -> None:
    calibration_data_prefix = calibration_data_dir / "valid"
    ckpt_sha = json.loads((ckpt_path / "params.json").read_text())["checkpoint_sha256"]
    if isinstance(model_arc, StarCoder2Arc):
        command = (
            "python research/tools/quantization/quantize_starcoder2.py "
            "--calibration-steps 200 "
            "--log-to-stdout "
            f"--ckpt-path {ckpt_path} "
            f"--ckpt-sha256 {ckpt_sha} "
            f"--out-path {ckpt_path}-fp8 "
            f"--calibration-data {calibration_data_prefix}"
        )
    elif isinstance(model_arc, Qwen25CoderArc):
        command = (
            "python research/tools/quantization/quantize_llama.py "
            "--calibration-steps 200 "
            "--log-to-stdout "
            f"--model-size qwen2_5-{model_arc.size} "
            f"--ckpt-path {ckpt_path} "
            f"--ckpt-sha256 {ckpt_sha} "
            f"--out-path {ckpt_path}-fp8 "
            f"--calibration-data {calibration_data_prefix}"
        )
    else:
        assert_never(model_arc)

    subprocess.run(command, shell=True, check=True)


if __name__ == "__main__":
    ckpt_path = (
        AUGMENT_CHECKPOINTS_ROOT
        / "next-edit-gen/S28-R4_ethanol-P21_qwen_seq12k_pause500_out600-pr_grouped_10k-qwen25coder_14b-ffw"
    )
    data_dir = Path(
        "/mnt/efs/augment/data/processed/next-edit/"
        "prv2-pr_grouped_10k/S28_16000p,R4_ethanol-K120,P21_qwen_seq12k_pause500_out600-pos_0.70-pad_10881"
    )
    run_quantization(Qwen25CoderArc(size="14b"), ckpt_path, data_dir)

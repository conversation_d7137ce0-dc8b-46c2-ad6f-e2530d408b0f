{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "\n", "from research.eval.harness.tasks.next_edit_gen_eval_task import NextEditGenEvalInfo\n", "\n", "\n", "def is_correct(info: NextEditGenEvalInfo) -> bool:\n", "    return info.predicted_change == info.gold_change\n", "\n", "\n", "eval1_name = \"S24-original\"\n", "eval1_path = Path(\"/mnt/efs/augment/eval/jobs/A89FFS4G\")\n", "eval2_name = \"S26-original\"\n", "eval2_path = Path(\"/mnt/efs/augment/eval/jobs/3Xcrmtdp\")\n", "eval1_info_list = [\n", "    NextEditGenEvalInfo.load_from_dir(path)\n", "    for path in sorted(eval1_path.glob(\"000__no_instruct_problem_*\"))\n", "]\n", "eval2_info_list = [\n", "    NextEditGenEvalInfo.load_from_dir(path)\n", "    for path in sorted(eval2_path.glob(\"000__no_instruct_problem_*\"))\n", "]\n", "\n", "eval2_gain_examples = [\n", "    (info1, info2)\n", "    for info1, info2 in zip(eval1_info_list, eval2_info_list)\n", "    if not is_correct(info1) and is_correct(info2)\n", "]\n", "eval2_lost_examples = [\n", "    (info1, info2)\n", "    for info1, info2 in zip(eval1_info_list, eval2_info_list)\n", "    if is_correct(info1) and not is_correct(info2)\n", "]\n", "print(f\"Number of gain examples: {len(eval2_gain_examples)}\")\n", "print(f\"Number of lost examples: {len(eval2_lost_examples)}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def item_display_fn(i: int, show_gain: bool):\n", "    if show_gain:\n", "        info1, info2 = eval2_gain_examples[i]\n", "    else:\n", "        info1, info2 = eval2_lost_examples[i]\n", "    file_names = [\"prompt_tokens.txt\", \"output_tokens.txt\"]\n", "    print(f\"{eval1_name} Files:\")\n", "    for rel_path in file_names:\n", "        print(f\"\\t{Path(info1.save_path) / rel_path}\")\n", "    print(f\"{eval2_name} Files:\")\n", "    for rel_path in file_names:\n", "        print(f\"\\t{Path(info2.save_path) / rel_path}\")\n", "    print(\"~=\" * 40)\n", "    print(\"Diff in context:\")\n", "    diff_start_pos = info2.prompt_tokens.index(\"<pr_diff>\")\n", "    diff_end_pos = info2.prompt_tokens.index(\"<pr_base>\")\n", "    print(info2.prompt_tokens[diff_start_pos:diff_end_pos])\n", "    print(\"~=\" * 40)\n", "    print(\"Selected code:\")\n", "    selection_begin = info2.prompt_tokens.index(\"[[<PERSON> Replace]]\")\n", "    selection_end = info2.prompt_tokens.index(\"[[Begin Replacement]]\")\n", "    print(info2.prompt_tokens[selection_begin:selection_end])\n", "    print(\"~=\" * 40)\n", "    print(\n", "        f\"{eval1_name} prediction (correct={info1.predicted_change == info1.gold_change}):\"\n", "    )\n", "    print(info1.predicted_change)\n", "    print(\"~=\" * 40)\n", "    print(\n", "        f\"{eval2_name} prediction (correct={info2.predicted_change == info2.gold_change}):\"\n", "    )\n", "    print(info2.predicted_change)\n", "\n", "\n", "item_display_fn(3, show_gain=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.eval.harness.tasks.next_edit_gen_eval_task import (\n", "    load_edit_problems_from_next_edit_data,\n", ")\n", "\n", "dataset_path = \"/mnt/efs/augment/data/eval/next_edits/prs.v7.jsonl.zst\"\n", "diffs_path = dataset_path.replace(\".jsonl.zst\", \".diffs.jsonl.zst\")\n", "files_path = dataset_path.replace(\".jsonl.zst\", \".files.jsonl.zst\")\n", "problems = load_edit_problems_from_next_edit_data(\n", "    diffs_path, files_path, limit_examples=500\n", ")\n", "print(\"Total number of problems: \", len(problems))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.utils.notebook_uis.edit_gen import display_edit_gen_viewer\n", "\n", "display_edit_gen_viewer(problems)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
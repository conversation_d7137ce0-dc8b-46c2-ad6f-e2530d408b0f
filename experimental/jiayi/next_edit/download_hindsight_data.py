from dataclasses import dataclass

from tqdm import tqdm
from base.datasets.gcs_blob_cache import GCSBlobCache, GCSPathCache
from base.datasets.gcp_creds import get_gcp_creds
from base.datasets.hindsight_next_edit import NextEditIntermediateType
from datetime import timedelta
from google.cloud import storage  # type: ignore

from base.datasets.tenants import DOGFOOD_SHARD, DatasetTenant
from base.datasets.hindsight_next_edit_intermediate_dataset import (
    GCSCaches,
    download_raw_data,
    NextEditRawDataQueryArgs,
    NextEditIntermediateDataLoader,
    NextEditIntermediateErrorDetails,
)
from research.next_edits.next_hunk_heuristic import (
    fix_next_edit_datum_events,
    dedup_next_edit_data,
)

from datetime import datetime

GB = 2**30


@dataclass
class IntermediateDataDownloader:
    """A convenience class for downloading the Next Edit intermediate data.

    Note that blobs are cached inside this object.
    """

    tenant: DatasetTenant = DOGFOOD_SHARD

    def __post_init__(self):
        self.caches = GCSCaches.create(self.tenant)

    def download_intermediate_data(
        self,
        end_time: datetime,
        total_duration: timedelta,
        future_events_duration: timedelta,
        query_args: NextEditRawDataQueryArgs = NextEditRawDataQueryArgs(),
    ) -> tuple[list[NextEditIntermediateType], NextEditIntermediateErrorDetails]:
        session_to_raw_data = download_raw_data(
            self.caches,
            timestamp_begin=end_time - total_duration,
            total_duration=total_duration,
            future_events_duration=future_events_duration,
            query_args=query_args,
        )
        error_collector = NextEditIntermediateErrorDetails()

        intermediate_data = list[NextEditIntermediateType]()
        for raw_data in tqdm(session_to_raw_data.values(), desc="sessions", position=0):
            if not raw_data.text_edit_events:
                # NextEditIntermediateDataLoader requires at least one text edit event
                continue
            user = raw_data.next_edit_data[0].user_id
            loader = NextEditIntermediateDataLoader(
                self.caches.tenant,
                raw_data,
                self.caches.blob_cache,
                self.caches.path_cache,
            )
            all_data = tqdm(
                loader.iterator(future_events_duration, error_collector),
                smoothing=0,
                desc=f"user={user}",
                total=len(raw_data.next_edit_data),
                position=1,
            )
            all_data = list(all_data)

            for datum in all_data:
                fix_next_edit_datum_events(datum)
            unique_data = list(dedup_next_edit_data(all_data))
            removed_data = {x.request.request_id for x in all_data} - {
                x.request.request_id for x in unique_data
            }
            error_collector.duplicated_data.extend(removed_data)
            intermediate_data.extend(unique_data)
        return intermediate_data, error_collector

"""A script to start a model server for next edit generation."""

import copy
from base.prompt_format_next_edit.gen_prompt_formatter import (
    EditGenFormatterConfig,
    section_budgets_10k,
)
from research.core.constants import (
    AUGMENT_CHECKPOINTS_ROOT,
    AUGMENT_EFS_ROOT,
)
from research.model_server.server_logging import configure_server_logging
from research.retrieval.retrieval_database import RetrievalDatabase
from research.eval.harness import factories
from research.eval.harness.systems.next_edit_gen_system import NextEditGenSystem
from research.llm_apis.chat_utils import Llama3ChatClient
from research.model_server.launch_model_server import main
from research.models.fastforward_models import StarCoder2_FastForward
from research.models.meta_model import GenerationOptions
from research.next_edits.edit_gen_formatters import EditGenPromptFormatter
from research.data.spark.pipelines.stages.next_edit_gen_pipelines import (
    raven_retriever_config,
)


def build_system(use_retriever: bool = False):
    model = StarCoder2_FastForward(
        AUGMENT_CHECKPOINTS_ROOT
        / "next-edit-gen/S24-R4_no_retrieval-P18_star2_diff12_seq12k-ordered-gh_pr_train-starcoder2_15b-ffw",
    )

    retriever = None
    if use_retriever:
        retriever_config = copy.deepcopy(raven_retriever_config)
        retriever_config["scorer"]["cache_dir"] = "/tmp/augment/cache"
        retriever = factories.create_retriever(dict(retriever_config))
        assert isinstance(retriever, RetrievalDatabase)

    # 9 diff context lines seem to be a sweet spot
    prompt_formatter = EditGenPromptFormatter(
        model.tokenizer_type()._base_tokenizer,
        config=EditGenFormatterConfig(
            diff_context_lines=9,
            max_prompt_tokens=10_200,
            section_budgets=section_budgets_10k(),
        ),
    )

    llama_server_address = "**************:8000"
    chat_client = Llama3ChatClient(
        server_type="triton", address=llama_server_address, timeout=180
    )

    return NextEditGenSystem(
        model,
        generation_options=GenerationOptions(max_generated_tokens=1000),
        retriever=retriever,
        prompt_formatter=prompt_formatter,
        chat_client=chat_client,
    )


log_dir = (
    AUGMENT_EFS_ROOT / "user/jiayi" / "model_server_logs" / "next_edits_server_logs"
)


def start_server():
    import socket

    logger_dir = AUGMENT_EFS_ROOT / "user/jiayi/model_server_logs"
    # get the ip address of the server
    ip_address = socket.gethostbyname(socket.gethostname())
    server_port = 5000
    log_file_path = logger_dir / f"terminal_log_{ip_address}_{server_port}.log"
    configure_server_logging("INFO", log_output_path=log_file_path)
    print(f"Server log will be saved to: {log_file_path}")

    system = build_system(use_retriever=False)
    system.load()

    args = [
        "--port",
        str(server_port),
        "--log_dir",
        str(logger_dir),
        "--logfile",
        str(log_file_path),
    ]
    # Comment out the line below if you are only using this server locally and are not planning to make it available to others.
    args.extend(["--host", "0.0.0.0"])
    main(
        system,
        input_args=args,
    )


if __name__ == "__main__":
    start_server()

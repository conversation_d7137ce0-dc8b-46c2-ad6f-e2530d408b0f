"""A script to start a model server for next edit generation."""

import yaml

import experimental.jiayi.next_edit.start_next_edit_gen_server as gen_server
from base.prompt_format_next_edit.gen_prompt_formatter import EditGenFormatterConfig
from research.core.constants import (
    AUGMENT_CHECKPOINTS_ROOT,
    AUGMENT_EFS_ROOT,
    AUGMENT_ROOT,
)
from research.eval.harness import factories
from research.eval.harness.systems.next_edit_combined_system import (
    NextEditCombinedSystem,
)
from research.eval.harness.systems.next_edit_gen_system import NextEditGenSystem
from research.eval.harness.systems.next_edit_location_system import (
    BasicNextEditLocationSystem,
    SingleFileNextEditLocationSystem,
)
from research.eval.harness.systems.next_edit_reranker_system import (
    NextEditRerankerSystem,
)
from research.model_server.launch_model_server import main
from research.model_server.server_logging import configure_server_logging
from research.models.fastforward_models import StarCoder2_FastForward
from research.models.meta_model import GenerationOptions
from research.next_edits.edit_gen_formatters import (
    EditGenPromptFormatter,
)
from research.next_edits.smart_chunking import SmartChunker


def build_location_system() -> BasicNextEditLocationSystem:
    config_path = (
        AUGMENT_ROOT / "research/model_server/configs/next_edit_location_raven.yaml"
    )
    with config_path.open(encoding="utf8") as f:
        config = yaml.safe_load(f)
    system = factories.create_system(config)
    assert isinstance(system, BasicNextEditLocationSystem)
    return system


def build_reranking_system():
    edit_model = StarCoder2_FastForward(
        AUGMENT_CHECKPOINTS_ROOT
        / "next-edit-gen/S1.13.1-R1.3_no_retrieval_synth_instruct-P1.13_context12-gh_pr_train_repartitioned-starcoder2_7b"
    )
    prompt_formatter = EditGenPromptFormatter(
        edit_model.tokenizer_type()._base_tokenizer,
        config=EditGenFormatterConfig(diff_context_lines=9),
    )

    edit_model = NextEditGenSystem(
        edit_model,
        generation_options=GenerationOptions(max_generated_tokens=1),
        retriever=None,
        prompt_formatter=prompt_formatter,
        chat_client=None,
    )
    localizer = build_location_system()
    rechunker = SmartChunker(max_chunk_chars=2000)
    reranker = NextEditRerankerSystem(localizer, edit_model, rechunker)
    return reranker


def build_combined_system():
    gen_system = gen_server.build_system()
    loc_system = build_reranking_system()
    single_file_system = SingleFileNextEditLocationSystem(max_chunk_chars=2000)
    return NextEditCombinedSystem(
        loc_system,
        single_file_system,
        gen_system,
        max_changes_to_return=3,
        default_max_changes_to_attempt=24,
        changes_per_yield=1,
        location_score_filter=0.35,
    )


def start_server():
    import socket

    logger_dir = AUGMENT_EFS_ROOT / "user/jiayi/model_server_logs"
    # get the ip address of the server
    ip_address = socket.gethostbyname(socket.gethostname())
    server_port = 5000
    log_file_path = logger_dir / f"terminal_log_{ip_address}_{server_port}.log"
    configure_server_logging("INFO", log_output_path=log_file_path)
    print(f"Server log will be saved to: {log_file_path}")

    system = build_combined_system()
    system.load()

    args = [
        "--port",
        str(server_port),
        "--log_dir",
        str(logger_dir),
        "--logfile",
        str(log_file_path),
    ]
    # Comment out the line below if you are only using this server locally and are not planning to make it available to others.
    args.extend(["--host", "0.0.0.0"])
    main(
        system,
        input_args=args,
    )


if __name__ == "__main__":
    start_server()

{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## Start the model server inside this notebook to enable autoloading"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We load the model and build the system first (since this is the expensive part) and then start the server in a separate cell. This allows us to make some changes, kill the server, let autoreload update the system code, and then restart the server without having to rebuild the system."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from experimental.jiayi.next_edit.start_next_edit_gen_server import (\n", "    build_system,\n", "    log_dir,\n", ")\n", "\n", "system = build_system()\n", "# system.retriever = None  # uncomment to disable retrieval\n", "system.load()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import importlib\n", "\n", "from research.model_server import launch_model_server\n", "\n", "# always reload the model_server module to avoid API endpoint errors.\n", "importlib.reload(launch_model_server)\n", "\n", "launch_model_server.main(\n", "    system,\n", "    input_args=(\"--port\", \"5001\", \"--host\", \"0.0.0.0\", \"--log_dir\", str(log_dir)),\n", ")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
"""<PERSON><PERSON><PERSON> to run all edit generation evaluation jobs.

This script should be manually edited and run to launch all the evaluation jobs.
"""

from pathlib import Path

import yaml

from experimental.jiayi.evaluation.eval_config_tools import launch_eval
from research.core.constants import AUGMENT_ROOT

diff_context_lines = 9

reranker_config = {
    "name": "next_edit_gen",
    "model": {
        "name": "starcoder2_fastforward",
        "model_path": (
            "/mnt/efs/augment/checkpoints/"
            "next-edit-gen/S1.10-R1.0_no_retrieval-P1.10_context12-gh_pr_train_repartitioned-starcoder2_7b"
        ),
    },
    "generation_options": {"max_generated_tokens": 1},
    "prompt_formatter": {
        "use_diff_based_output": True,
        "diff_context_lines": diff_context_lines,
    },
    "retriever_formatter": {
        "use_diff": False,
    },
}

localizer_config_path = (
    AUGMENT_ROOT / "research/model_server/configs/next_edit_location_raven.yaml"
)
with localizer_config_path.open(encoding="utf8") as f:
    localizer_config = yaml.safe_load(f)
    # Don't cache embeddings in evals.
    if "cache_dir" in localizer_config["retriever"]["scorer"]:
        del localizer_config["retriever"]["scorer"]["cache_dir"]

system_config = {
    "name": "next_edit_reranker",
    "localizer": localizer_config,
    "reranker": reranker_config,
    "rechunker": None,
}
# system_config = localizer_config


if __name__ == "__main__":
    tasks = [
        {
            "name": "next_edit_location",
            "dataset_path": dataset_path,
            "limit_examples": 1000,
            "top_ks": [3, 8, 32],
        }
        for dataset_path in [
            "/mnt/efs/augment/data/eval/next_edits/manual.v3.jsonl.zst",
            "/mnt/efs/augment/data/eval/next_edits/prs.v7.jsonl.zst",
        ]
    ]
    for task_config in tasks:
        dataset_name = Path(task_config["dataset_path"]).name.replace(".jsonl.zst", "")
        if system_config == localizer_config:
            model_name = Path(
                localizer_config["retriever"]["scorer"]["checkpoint_path"]
            ).name
            determined_name = f"{dataset_name},{model_name}"
        else:
            localizer_name = Path(
                localizer_config["retriever"]["scorer"]["checkpoint_path"]
            ).name
            model_name = Path(reranker_config["model"]["model_path"]).name
            determined_name = (
                f"{dataset_name},{localizer_name},reranked_by-{model_name}"
            )
        determined_name = f"{determined_name},topK{task_config['top_ks'][-1]}"

        launch_eval(
            determined_name,
            system_config,
            task_config,
            "1xA100.yaml",
            job_root=Path(f"/mnt/efs/augment/eval/next_edits/{determined_name}"),
        )

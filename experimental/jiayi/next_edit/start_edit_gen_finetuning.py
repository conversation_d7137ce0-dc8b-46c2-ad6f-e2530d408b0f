# flake8: noqa
from __future__ import annotations

from pathlib import Path

from megatron.tokenizer.tokenizer import StarCoder2Tokenizer, StarCoderTokenizer
from research.core.constants import AUGMENT_CHECKPOINTS_ROOT, AUGMENT_EFS_ROOT
from research.environments.providers import <PERSON>lusterName
from experimental.jiayi.finetuning.training_config_utils import (
    Qwen25CoderArc,
    StarCoder2Arc,
    get_fastbackward_config,
    sync_gcp,
    wait_for_condition,
    NextEditModelArc,
)


def edit_gen_fb_config(
    run_summary: str,
    model_arc: NextEditModelArc,
    user: str,
    data_splits_path: Path,
    sequence_length: int,
    train_iters: int,
    batch_size: int = 256,
    eval_items: int = 256 * 100,
    eval_interval: int = 500,
    n_nodes: int | None = None,
    is_quick_test: bool = False,
    restore_from_checkpoint: str | None = None,
    checkpoint_path: Path | None = None,
) -> dict:
    """Get the training config dict to be submitted to FastBackward.

    Args:
        run_summary: A short string summary of the training run.
        model_name: The model name to use.
        user: The training job will be submitted under Dev/{user}.
        model_size: The model size to use.
        data_splits_path: The directory containing the indexed dataset files.
        sequence_length: The sequence length to use.
        train_iters: The number of training iterations.
        batch_size: The effective batch size to use.
        n_nodes: The number of GPU nodes to use. Each node has 8 GPUs.
        is_quick_test: Whether to use a quick test config.
        restore_from_checkpoint: The checkpoint ID to restore training from. When this\
            is set, `checkpoint_path` will be ignored.
        checkpoint_path: The path to the checkpoint directory. If None, will use the\
            default checkpoint path for the model.
    """

    wandb_project = "next-edit-gen"
    training_name = f"{run_summary}-{model_arc.name}_{model_arc.size}"
    if is_quick_test:
        training_name += "-quicktest"

    common_overrides: dict = {
        "eval_items": eval_items,
        "eval_interval": eval_interval,
        "log_interval": eval_interval // 5,
        "loss_mask_policy": "fim",
    }
    if restore_from_checkpoint is not None:
        common_overrides["checkpoint"] = restore_from_checkpoint
        common_overrides["restore_training_metadata_from_checkpoint"] = True
        # assuming we don't want this if we are not restoring from the Determined UI
        common_overrides["restore_optimizer_state_from_checkpoint"] = False
    elif checkpoint_path is not None:
        common_overrides["checkpoint"] = str(checkpoint_path)

    if is_quick_test:
        common_overrides["eval_interval"] = 40
        common_overrides["eval_items"] = 128
        train_iters = 50
        wandb_project = "quicktest"

    # effective batch size = 256
    if model_arc.size == "3b":
        n_nodes = n_nodes or 4
        grad_accumulation_steps = batch_size // 32 // n_nodes
        batch_size_per_gpu = 4
    elif model_arc.size == "7b":
        n_nodes = n_nodes or 4
        grad_accumulation_steps = batch_size // 16 // n_nodes
        batch_size_per_gpu = 2
    elif model_arc.size in ("14b", "15b"):
        n_nodes = n_nodes or 8
        grad_accumulation_steps = batch_size // 8 // n_nodes
        batch_size_per_gpu = 2
    elif model_arc.size == "32b":
        n_nodes = n_nodes or 16
        grad_accumulation_steps = batch_size // 4 // n_nodes
        batch_size_per_gpu = 2
    else:
        raise NotImplementedError(f"{model_arc=} not supported")

    assert grad_accumulation_steps > 0
    return get_fastbackward_config(
        model_arc=model_arc,
        training_name=training_name,
        wandb_project=wandb_project,
        user=user,
        data_split_path=data_splits_path,
        sequence_length=sequence_length,
        n_gpu=8,
        n_nodes=n_nodes,
        batch_size_per_gpu=batch_size_per_gpu,
        grad_accumulation_steps=grad_accumulation_steps,
        train_iters=train_iters,
        overrides=common_overrides,
    )


def start_finetuning():
    dataset_path = Path(
        "/mnt/efs/augment/data/processed/next-edit/"
        "prv2-pr_grouped_10k/S28_16000p_wip0.6_small1000,R4_ethanol-K120,P21_star2_seq12k_pause500_out600-pos_0.70-pad_10881"
    )
    wait_for_condition(
        "dataset_path", lambda: (dataset_path / "_SUCCESS").exists(), retry_secs=60
    )
    model_arc = StarCoder2Arc(size="15b")
    run_summary = "S28_wip0.6_small1000-R4_ethanol-P21_star2_seq12k_pause500_out600-pr_grouped_10k"
    sequence_length = 10880  # this should be pad length - 1
    assert sequence_length % 128 == 0, "sequence_length should be a multiple of 128"
    train_iters = 10_000
    eval_interval = 1000
    batch_size = 512
    is_quick_test = False
    cluster: ClusterName = "CW-EAST4"

    from research.fastbackward.determined.launch import launch_fb_job  # type: ignore

    train_config = edit_gen_fb_config(
        model_arc=model_arc,
        run_summary=run_summary,
        user="jiayi",
        data_splits_path=dataset_path,
        sequence_length=sequence_length,
        train_iters=train_iters,
        batch_size=batch_size,
        eval_items=512 * 100,
        eval_interval=eval_interval,
        is_quick_test=is_quick_test,
        restore_from_checkpoint=None,
        checkpoint_path=None,
        n_nodes=16,
    )
    launch_fb_job(
        train_config,
        cluster=cluster,
    )


def start_continued_finetuning():
    """Contine finetuning an EditGen checkpoint on the ordered diff dataset."""

    dataset_path = Path(
        "/mnt/efs/augment/data/processed/next-edit/"
        "ordered-gh_pr_train_repartitioned/S24.1_25000p,R4_ethanol-K128,P18_star2_diff12_seq12k-pos_0.70-pad_12033"
    )
    checkpoint_path = (
        AUGMENT_CHECKPOINTS_ROOT
        / "next-edit-gen/S24-R4_ethanol-P18_star2_diff12_seq12k-pr_grouped_10k-starcoder2_15b-fbwd"
    )

    wait_for_condition(
        "dataset_path", lambda: (dataset_path / "_SUCCESS").exists(), retry_secs=60
    )
    model_arc = StarCoder2Arc(size="15b")
    run_summary = "S24.1-R4_ethanol-P18_star2_diff12_seq12k-ordered-gh_pr_train"
    sequence_length = 12032  # this should be pad_to_length - 1
    assert sequence_length % 128 == 0, "sequence_length should be a multiple of 128"
    train_iters = 2000
    is_quick_test = False
    cluster: ClusterName = "GCP-US1"

    if cluster != "GCP-US1":
        raise NotImplementedError("Only GCP-US1 is supported.")
        if model_name == "starcoder2":
            sync_gcp(AUGMENT_EFS_ROOT / StarCoder2Tokenizer.VocabFileDir)
        else:
            sync_gcp(AUGMENT_EFS_ROOT / StarCoderTokenizer.VocabFileDir)

    from research.fastbackward.determined.launch import launch_fb_job  # type: ignore

    train_config = edit_gen_fb_config(
        model_arc=model_arc,
        run_summary=run_summary,
        user="jiayi",
        data_splits_path=dataset_path,
        sequence_length=sequence_length,
        train_iters=train_iters,
        is_quick_test=is_quick_test,
        restore_from_checkpoint=None,
        checkpoint_path=checkpoint_path,
        eval_items=256 * 60,
        n_nodes=16,
    )
    launch_fb_job(
        train_config,
        cluster=cluster,
    )


if __name__ == "__main__":
    start_finetuning()
    # start_continued_finetuning()

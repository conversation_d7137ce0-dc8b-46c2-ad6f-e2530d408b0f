from __future__ import annotations

import dataclasses
import json
import shutil
import subprocess
from logging import getLogger
from pathlib import Path
from typing import assert_never

from base.fastforward.cached_attention import SplitHeadModes
from base.fastforward.checkpoints.impl.manifest import CkptManifest
from experimental.jiayi.scripts.convert_startcoder2_ff import convert_starcoder2_ff
from experimental.jiayi.finetuning.training_config_utils import (
    Qwen25CoderArc,
    StarCoder2Arc,
    NextEditModelArc,
)
from research.tools.ckp_converter.fbw2ffw_llama import main as convert_llama
from base.fastforward.llama import model_specs as llama_model_specs

logger = getLogger(__name__)

checkpoint_root = Path("/mnt/efs/augment/checkpoints/")


def download_merge_checkpoint(
    checkpoint_id: str,
    save_name: str,
    model_arc: NextEditModelArc,
    from_gcp: bool,
):
    """Download the checkpoint, optionally merge it, and convert to fastforward.

    Args:
        checkpoint_id: the UUID of the checkpoint.
        save_name: the path of the checkpoint relative to $AUGMENT_CHECKPOINTS_ROOT.
        model_arc: the model architecture.
        from_gcp: whether to download from GCP.
    """
    match model_arc:
        case StarCoder2Arc(size="15b") | Qwen25CoderArc(size="14b"):
            model_parallel = 2
        case _:
            model_parallel = 1
    if model_parallel > 1:
        download_name = save_name + f"-fbw-mp{model_parallel}"
    else:
        download_name = save_name + "-fbw"

    download_args = [
        "bash",
        "research/utils/download_checkpoint.sh",
        checkpoint_id,
        download_name,
    ]
    if from_gcp:
        download_args.append("gcp-us1")
    else:
        download_args.append("cw-east4")
    subprocess.run(download_args, check=True)

    if not save_name.endswith("-ffw"):
        save_name += "-ffw"

    if isinstance(model_arc, StarCoder2Arc):
        logger.info("Converting a StarCoder2 checkpoint.")
        convert_starcoder2_ff(
            checkpoint_root / download_name, checkpoint_root / save_name, model_arc.size
        )
    elif isinstance(model_arc, Qwen25CoderArc):
        logger.info("Converting a Qwen25 checkpoint.")
        manifest = convert_llama(
            checkpoint_root / download_name, checkpoint_root / save_name
        )
        save_llama_params(model_arc, manifest, checkpoint_root / save_name)
    else:
        assert_never(model_arc)

    if (checkpoint_root / download_name).exists():
        print(f"Removing {checkpoint_root / download_name}...")
        shutil.rmtree(checkpoint_root / download_name)
    print(f"Checkpoint saved to: {checkpoint_root / save_name}")


def save_llama_params(
    model_arc: Qwen25CoderArc, manifest: CkptManifest, save_path: Path
):
    model_name = f"qwen2_5-{model_arc.size}"
    model_spec = llama_model_specs.get_llama_model_spec(
        model_name=model_name,
        checkpoint_path=save_path,
    )
    model_spec.checkpoint_sha256 = manifest.manifest_sha256.hex()

    class CustomEncoder(json.JSONEncoder):
        def default(self, o):
            if isinstance(o, SplitHeadModes):
                return o.value
            return super().default(o)

    params_dict = json.dumps(
        dataclasses.asdict(model_spec), cls=CustomEncoder, indent=2, sort_keys=True
    )
    (save_path / "params.json").write_text(params_dict, "utf8")
    print(f"Converted checkpoint saved to {save_path}")


if __name__ == "__main__":
    download_merge_checkpoint(
        "[CHECKPOINT_ID]",
        "next-edit-gen/[CHECKPOINT_NAME]",
        model_arc=Qwen25CoderArc(size="14b"),
        from_gcp=True,
    )

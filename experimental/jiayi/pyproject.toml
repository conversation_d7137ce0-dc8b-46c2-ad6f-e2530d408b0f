[tool.poetry]
name = "static_analysis"
version = "0.1.0"
description = "Static Analysis utils for Augment."
authors = ["<PERSON><PERSON><PERSON> <ji<PERSON><PERSON>@augmentcode.com>"]
readme = "README.md"

packages = [
    { include = "static_analysis" },
]

[tool.poetry.dependencies]
python = "3.9.*"
tree-sitter-languages = "~1.5.0"

[tool.poetry.group.dev.dependencies]
pytest = "*"
black = "*"
snakeviz = "*"
line-profiler = "*"
ipykernel = "*"
pylint = "2.17.0"
pre-commit = "^3.2.2"
flake8 = "^6.0.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

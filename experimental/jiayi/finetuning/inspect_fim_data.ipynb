{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "\n", "from pathlib import Path\n", "\n", "from research.utils.generate_fim_data import load_indexed_dataset\n", "from research.utils.inspect_indexed_dataset import highlight_special_tokens\n", "\n", "\n", "def compute_training_steps(tokens: int, batch_size: int, ctx_size: int):\n", "    return tokens / (batch_size * ctx_size)\n", "\n", "\n", "dataset_dir = Path(\n", "    \"/mnt/efs/augment/data/processed/next-edit/100K_repos/S1.7_keep_most_1400p_2000f,R1.0_no_retrieval,P1.8_star2_context12/valid\"\n", "    # \"/mnt/efs/augment/data/processed/rag/dataset/ender_multieth61m_fixfim_noinline_sigretprefsuf_5k/dataset\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["idata = load_indexed_dataset(dataset_dir)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["n_tks = sum(len(idata.get(i)) for i in range(len(idata)))\n", "train_steps = compute_training_steps(n_tks, batch_size=512, ctx_size=8192)\n", "print(f\"Training steps needed: {train_steps}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from dataclasses import dataclass\n", "from typing import Sequence\n", "\n", "import numpy as np\n", "from megatron.tokenizer.tokenizer import StarCoder2Tokenizer, StarCoderTokenizer\n", "\n", "tokenizer = StarCoder2Tokenizer()\n", "special_tokens = tokenizer.all_special_tokens()\n", "\n", "def find_elem(array: np.ndarray, elem: int) -> int:\n", "    return int(np.where(array == elem)[0][0])\n", "\n", "\n", "@dataclass\n", "class FimSequence:\n", "    preamble: np.n<PERSON><PERSON>\n", "    prefix: np.n<PERSON><PERSON>\n", "    middle: np.n<PERSON><PERSON>\n", "    suffix: np.n<PERSON>ray\n", "\n", "    @classmethod\n", "    def parse(cls, tokens: Sequence[int]):\n", "        array = np.abs(np.array(tokens))\n", "        prefix_i = find_elem(array, tokenizer.fim_prefix_id)\n", "        suffix_i = find_elem(array, tokenizer.fim_suffix_id)\n", "        mid_i = find_elem(array, tokenizer.fim_middle_id)\n", "        preamble = array[:prefix_i]\n", "        prefix = array[prefix_i:suffix_i]\n", "        suffix = array[suffix_i:mid_i]\n", "        middle = array[mid_i:]\n", "        if tokenizer.pad_id in middle:\n", "            pad_i = find_elem(middle, tokenizer.pad_id)\n", "            middle = middle[: pad_i]\n", "        return cls(preamble, prefix, middle, suffix)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from typing import cast\n", "\n", "from tqdm import tqdm\n", "\n", "\n", "def sequence_filter(seq: FimSequence):\n", "    return tokenizer.instruction_id in seq.preamble\n", "    # return True\n", "\n", "interesting_seqs = list[FimSequence]()\n", "for ex in tqdm(idata[:10_000], smoothing=0):\n", "    parsed = FimSequence.parse(cast(Sequence[int], ex))\n", "    if sequence_filter(parsed):\n", "        interesting_seqs.append(parsed)\n", "print(f\"{len(interesting_seqs)=}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def print_fim_sequence(seq: FimSequence):\n", "    rearranged = (\n", "        tokenizer.detokenize(seq.preamble)\n", "        + tokenizer.detokenize(seq.prefix)\n", "        + tokenizer.detokenize(seq.middle)\n", "        + tokenizer.detokenize(seq.suffix)\n", "    )\n", "    print(highlight_special_tokens(rearranged, special_tokens))\n", "\n", "def print_data(code: str):\n", "    print(highlight_special_tokens(code, special_tokens))\n", "\n", "ex_id = 21\n", "print_fim_sequence(interesting_seqs[ex_id])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from megatron.tokenizer.tokenizer import StarCoderTokenizer\n", "\n", "from research.utils.inspect_indexed_dataset import find_loss_tokens\n", "\n", "tokenizer = StarCoderTokenizer()\n", "\n", "skip_id = tokenizer.skip_id\n", "skip_examples = [ex for ex in idata if skip_id in ex]\n", "print(len(skip_examples))\n", "print(f\"skip fraction: {len(skip_examples) / len(idata):.2f}\")\n", "\n", "lookup_id = tokenizer.sig_lookup_id\n", "lookup_examples = [ex for ex in idata if lookup_id in ex]\n", "print(len(lookup_examples))\n", "print(f\"lookup fraction: {len(lookup_examples) / len(idata):.2f}\")\n", "\n", "\n", "loss_token_counts = [\n", "    len(find_loss_tokens(\"signature-fim\", tokenizer, ex)) for ex in idata[:500]\n", "]\n", "print(\"average loss token count:\", sum(loss_token_counts) / len(loss_token_counts))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.utils.inspect_indexed_dataset import print_fim_sequence, find_loss_tokens\n", "\n", "ex_id = 3\n", "\n", "loss_tokens = find_loss_tokens(\"signature-fim\", tokenizer, lookup_examples[ex_id])\n", "print(\"Tokens with loss:\")\n", "print(\n", "    highlight_special_tokens(\n", "        tokenizer.detokenize(loss_tokens), tokenizer.all_special_tokens()\n", "    )\n", ")\n", "print(\"==\" * 50)\n", "print_fim_sequence(tokenizer, lookup_examples[ex_id])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from tqdm import tqdm\n", "from research.fim.fim_sampling import find_todo_like\n", "\n", "\n", "mid_id = tokenizer.fim_middle_id\n", "\n", "\n", "def find_todo_in_middle(tokens):\n", "    tokens = list(tokens)\n", "    mid_i = tokens.index(mid_id)\n", "    middle = tokens[mid_i:]\n", "    middle_code = tokenizer.detokenize(middle)\n", "    match = find_todo_like(middle_code)\n", "    if match is not None:\n", "        return (match.group(), middle_code)\n", "    return None\n", "\n", "\n", "sample_size = 50_000\n", "matches = []\n", "for ex in tqdm(idata[:sample_size]):\n", "    match = find_todo_in_middle(ex)\n", "    if match is not None:\n", "        matches.append(match)\n", "        print(match[0])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(f\"All Matches: {len(matches)} ({len(matches) / sample_size:.2%})\")\n", "\n", "note_matches = [\n", "    m for m in matches if m[0].startswith(\"note\") or m[0].startswith(\"Note\")\n", "]\n", "print(f\"Note matches: {len(note_matches)} ({len(note_matches) / sample_size:.2%})\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for m in note_matches[:20]:\n", "    print(\"~=\" * 50)\n", "    print(\"matched:\", m[0])\n", "    print(m[1])\n", "    print(\"~=\" * 50)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Analyzing the middle span distribution"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pickle\n", "\n", "from research.fim.fim_prompt import FormattedFimProblem\n", "from research.utils.generate_signature_data import RepoSamples\n", "\n", "probs_path = dataset_dir.parent.parent.parent / \"sample_from_the_stack(python).pkl\"\n", "\n", "samples: list[RepoSamples] = pickle.load(probs_path.open(\"rb\"))\n", "problems: list[FormattedFimProblem] = [\n", "    p for repo in samples for p in repo.formatted_problems\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "\n", "dataset_name = dataset_dir.parent.name\n", "middle_lens = [len(p.middle_range) for p in problems]\n", "\n", "truncate_rate = sum(1 for p in problems if len(p.middle_range) >= 500) / len(problems)\n", "print(f\"fraction of truncated middles: {truncate_rate:.1%}\")\n", "\n", "plt.hist(middle_lens, bins=50, label=f\"Middle span tokens ({dataset_name})\")\n", "plt.show()"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
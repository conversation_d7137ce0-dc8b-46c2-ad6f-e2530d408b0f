{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Finetuning to align FIM model completions to semantic boundaries\n", "\n", "(copied and modified from `experimental/arun`)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load models. We are only comparing StarCoder, and doing so via model-server.\n", "import logging\n", "from pathlib import Path\n", "\n", "from research.models.starcoder_models import StarCoder\n", "\n", "logging.basicConfig(level=logging.WARNING)\n", "\n", "from research.models.remote_models import RemoteModel\n", "\n", "# All our models currently are loaded on the same machine.\n", "model = RemoteModel(\"http://216.153.48.243:5000\")\n", "# model = StarCoder(checkpoint_path=Path(\"/mnt/efs/augment/checkpoints/starcoderbase_neox\"))\n", "model.load()\n", "\n", "model_custom = RemoteModel(\"http://216.153.49.220:5000\")\n", "model_custom.load()\n", "# model_custom = StarCoder(\n", "#     checkpoint_path=Path(\"/mnt/efs/augment/checkpoints/new-FIM-starcoder-python-559K\")\n", "# )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Testing utils"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Testing utils\n", "import pathlib\n", "from typing import Iterable\n", "from termcolor import colored\n", "from research.core.model_input import ModelInput\n", "from research.models.meta_model import GenerationOptions, GenerativeLanguageModel\n", "from research.models.meta_gpt_neox_model import GPTNeoXModel\n", "from research.eval.vulcan.data import VulcanTest, Patch\n", "from research.eval.vulcan import load_tests\n", "\n", "\n", "def fim_tests(tags: set[str]):\n", "    return [test for test in load_tests() if set(test.meta.get(\"tags\", [])) & tags]\n", "\n", "\n", "def run_test(\n", "    model: GenerativeLanguageModel,\n", "    test: VulcanTest,\n", "    temperature: float = 0,\n", "    max_tokens: \"int | None\" = 1024,\n", ") -> Patch:\n", "    # Make sure we generate sufficient tokens for the expected case. Here we are\n", "    # intentionally using an underestimate #chars:#tokens ratio.\n", "    if \"max_generated_tokens\" in test.meta:\n", "        max_tokens_str = test.meta[\"max_generated_tokens\"]\n", "        if isinstance(max_tokens_str, int):\n", "            max_tokens = max_tokens_str\n", "        elif max_tokens_str.lower() == \"none\":\n", "            max_tokens = None\n", "        else:\n", "            max_tokens = int(max_tokens_str)\n", "    elif test.expected:\n", "        max_tokens = len(test.expected)\n", "    else:\n", "        max_tokens = 200\n", "\n", "    predicted = model.generate(\n", "        ModelInput(prefix=test.prefix, suffix=test.suffix),\n", "        GenerationOptions(\n", "            temperature=temperature,\n", "            max_generated_tokens=max_tokens,\n", "        ),\n", "    )\n", "    return test.as_patch(predicted or \"\")\n", "\n", "\n", "def display_output(test: VulcanTest, model_output: str, extra_title_content: str = \"\"):\n", "    correct = model_output.strip() == test.expected.strip()\n", "\n", "    name = pathlib.Path(test.filename).name\n", "\n", "    print(\n", "        \"========================================================================================\"\n", "    )\n", "    print(f\"EXAMPLE: {name} {'✓' if correct else '✗'} | {extra_title_content}\")\n", "    print(\n", "        \"----------------------------------------------------------------------------------------\"\n", "    )\n", "    print(\n", "        colored(test.prefix, \"blue\")\n", "        + (colored(\"(no output)\", \"white\", \"on_black\") if not model_output else \"\")\n", "        + colored(model_output, \"white\", \"on_black\")\n", "        + colored(test.suffix, \"blue\")\n", "    )\n", "    print(\n", "        \"========================================================================================\"\n", "    )\n", "    print(\"Expected:\", test.expected)\n", "    print()\n", "    print()\n", "\n", "\n", "import datetime\n", "import jsonlines\n", "\n", "VULCAN_EVAL_ROOT = pathlib.Path(\"/mnt/efs/augment/eval/vulcan/\")\n", "\n", "\n", "def run_all_tests(\n", "    model: GenerativeLanguageModel,\n", "    model_name: str,\n", "    tests: Iterable[VulcanTest],\n", "    save_results: bool = False,\n", "    n_samples: int = 1,\n", "    temperature: float = 0,\n", "):\n", "    patches = []\n", "\n", "    print(\n", "        \"========================================================================================\"\n", "    )\n", "    print(f\"MODEL: {model_name}\")\n", "    print(\n", "        \"----------------------------------------------------------------------------------------\"\n", "    )\n", "    for test in tests:\n", "        for sample in range(n_samples):\n", "            model_output = run_test(model, test, temperature=temperature)\n", "            patches.append(model_output)\n", "            display_output(\n", "                test,\n", "                model_output.patch_content,\n", "                extra_title_content=f\"model={model_name}, temp={temperature}, sample={sample}\",\n", "            )\n", "\n", "    if save_results and patches:\n", "        # Save output to a patches file.\n", "        output = VULCAN_EVAL_ROOT / f\"{datetime.date.today():%Y%m%d}_{model_name}.jsonl\"\n", "        with output.open(\"w\") as fp:\n", "            jsonlines.Writer(fp).write_all([patch.to_json() for patch in patches])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Checkpoint starcoderbase_neox (default)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# FIM-behavior\n", "run_all_tests(\n", "    model,\n", "    \"starcoder_16b\",\n", "    fim_tests({\"fim\"}),\n", "    save_results=False,\n", "    n_samples=1,\n", "    temperature=0,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Custom Checkpoint\n", "\n", "* [Determined](https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud/det/experiments/11148/checkpoints)\n", "* [WANDB](https://wandb.ai/augment/starcoder/runs/2yc99y86)\n", "* Trained for 2000 iterations on 8 GPUs.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# FIM behavior\n", "run_all_tests(\n", "    model_custom,\n", "    \"custom model\",\n", "    fim_tests({\"fim-skip\", \"fim-pause\"}),\n", "    n_samples=1,\n", "    temperature=0,\n", ")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## Sample Signature-augmented FIM fintuning data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "%load_ext snakeviz\n", "%load_ext line_profiler"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from random import Random\n", "from research.utils.data_utils import read_src_repo\n", "\n", "from research.utils.generate_fim_data import IntRange\n", "from research.utils.generate_signature_data import (\n", "    SignatureDataProcessor,\n", ")\n", "\n", "seq_length = 1024 * 8\n", "rng = Random(42)\n", "n_examples = 500\n", "\n", "processor = SignatureDataProcessor(seq_len_range=IntRange(2048, seq_length))\n", "processor._initializer()\n", "aug_repo = read_src_repo(langs=(\"python\",))\n", "all_files = aug_repo.files\n", "shuffled_file_ids = list(range(len(all_files)))\n", "rng.shuffle(shuffled_file_ids)\n", "shuffled_file_ids = shuffled_file_ids[:n_examples]\n", "\n", "sampling_files = [all_files[i].path for i in shuffled_file_ids]\n", "probs = processor._repo_to_problems(\n", "    aug_repo, files_to_sample=sampling_files, seed=42, tqdm_args={}\n", ")\n", "print(f\"{len(probs)=}\")\n", "tkn = processor.get_formatter().tokenizer"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.models.starcoder_models import StarCoderTokenizer\n", "interesting_probs = [p for p in probs if StarCoderTokenizer.sig_lookup_id in p.tokens]\n", "print(len(interesting_probs))\n", "print(f\"ratio: {len(interesting_probs) / len(probs)}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["interesting_probs[0].pprint(tkn)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["probs[8].pprint(tkn)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Replay requets against dev deploy.\n", "\n", "This notebook includes an example of how to replay requests in dogfood (or aitutor-*) \n", "against a dev deploy. "]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Replay requests in dev tenants.\n", "\n", "This notebook includes an example of how to replay requests to dogfood (or aitutor-*) \n", "against dev tenants. This can be useful to test models on real requests without\n", "having to deploy the model to staging.\n", "\n", "## Setup (should only need to be done once)\n", "\n", "1. Install the required Python libraries:\n", "```bash\n", "pip3 install -U google-cloud-bigquery google-cloud-storage lru-dict pympler\n", "```\n", "2. Authenticate with Google:\n", "```bash\n", "gcloud auth login\n", "gcloud auth application-default login\n", "```\n", "3. Generate the proto library files (do periodically):\n", "```bash\n", "bazel run //tools/generate_proto_typestubs\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Gets Requests from BigQuery"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Gets Specific Requests"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["TARGET_REQUESTS = {\n", "    \"70a29f00-930d-4f45-875d-0d351e9f52bc\": \"Undoes his most recent change\",\n", "    \"2ef50dc9-b1a1-4e2f-a76b-426e224707be\": \"Undoes its own change (The hunks don’t follow a linear order due to the recent prompt formatter change)\",\n", "    \"bef2569c-319f-41b5-9605-fcf288af3af1\": \"Undo most recent change (The last change’s hunk does not have a file path)\",\n", "    \"767fb484-053a-445b-93fe-f8a1210f449f\": \"Adding code she deleted\",\n", "    \"20c7cbd4-72c3-426f-8e87-8ab5d5c29ea6\": \"Undo\",\n", "    \"b98418a8-bad8-445b-a8d0-6af40dc5c076\": \"suggesting removal of newly added code\",\n", "}\n", "target_request_ids = list(TARGET_REQUESTS)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["QUERY = \"\"\"\n", "SELECT\n", "  request_id,\n", "  raw_json as request_json,\n", "  time\n", "FROM\n", "  `staging_request_insight_full_export_dataset.next_edit_host_request`\n", "WHERE\n", "  tenant = 'dogfood-shard'\n", "  AND request_id IN UNNEST(@request_ids)\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from google.cloud import bigquery\n", "\n", "from base.datasets import replay_utils, tenants\n", "from base.datasets.gcp_creds import get_gcp_creds\n", "\n", "tenant = tenants.DOGFOOD_SHARD\n", "bigquery_client = bigquery.Client(\n", "    project=tenant.project_id, credentials=get_gcp_creds()[0]\n", ")\n", "blob_cache = replay_utils.get_blob_cache(tenant)\n", "checkpoint_cache = replay_utils.get_checkpoint_cache(tenant)\n", "query_parameters = [\n", "    bigquery.ArrayQueryParameter(\"request_ids\", \"STRING\", target_request_ids)\n", "]\n", "job_config = bigquery.QueryJobConfig(query_parameters=query_parameters)\n", "\n", "rows = bigquery_client.query_and_wait(QUERY, job_config=job_config, page_size=128)\n", "data = list(rows)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Replay"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Gets Augment client"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "from base.augment_client.client import AugmentClient\n", "\n", "with open(os.path.expanduser(\"~/.config/augment/api_token\")) as f:\n", "    client = AugmentClient(\n", "        url=\"https://dev-jiayi.us-central.api.augmentcode.com\",\n", "        token=f.read().strip(),\n", "    )\n", "next_edit_host_name = \"raven-edit-v4-15b\"\n", "model_client = client.client_for_model(next_edit_host_name)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Upload blobs"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import tqdm\n", "from base.blob_names import blob_names_pb2\n", "from google.protobuf.json_format import ParseDict\n", "\n", "all_blob_names = set()\n", "\n", "for r in tqdm.tqdm(data):\n", "    blobs_proto = blob_names_pb2.Blobs()\n", "    ParseDict(r[\"request_json\"][\"request\"][\"blobs\"], blobs_proto)\n", "    blob_names = replay_utils.resolve_checkpoint(blobs_proto, checkpoint_cache)\n", "    replay_utils.upload_and_ensure_blobs_exist(\n", "        client, blob_cache, blob_names, next_edit_host_name\n", "    )\n", "    all_blob_names.update(blob_names)\n", "\n", "# Verifies that all the blobs are indexed.\n", "replay_utils.index_status(client, all_blob_names, next_edit_host_name)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Replay requests in dev tenants."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from google.protobuf.json_format import MessageToDict, ParseDict\n", "\n", "from base.blob_names import blob_names_pb2\n", "from services.next_edit_host import next_edit_pb2\n", "from services.request_insight import request_insight_pb2\n", "\n", "all_requests = []\n", "for r in tqdm.tqdm(data):\n", "    request_pb = ParseDict(r[\"request_json\"], request_insight_pb2.RINextEditRequest())\n", "    responses = list(\n", "        model_client.next_edit_stream(\n", "            mode=next_edit_pb2.NextEditMode.Name(request_pb.request.mode),\n", "            scope=next_edit_pb2.NextEditScope.Name(request_pb.request.scope),\n", "            instruction=request_pb.request.instruction,\n", "            path=request_pb.request.path,\n", "            prefix=request_pb.request.prefix,\n", "            suffix=request_pb.request.suffix,\n", "            selected_text=request_pb.request.selected_text,\n", "            edit_events=[\n", "                MessageToDict(\n", "                    e,\n", "                    including_default_value_fields=True,\n", "                    preserving_proto_field_name=True,\n", "                )\n", "                for e in request_pb.request.edit_events\n", "            ],\n", "            blobs={\n", "                \"checkpoint_id\": request_pb.request.blobs.baseline_checkpoint_id,\n", "                \"added_blobs\": [e.hex() for e in request_pb.request.blobs.added],\n", "                \"deleted_blobs\": [e.hex() for e in request_pb.request.blobs.deleted],\n", "            },\n", "            warn_on_parse_error=True,\n", "        )\n", "    )\n", "    all_requests.append(responses)\n", "\n", "for r, responses in zip(data, all_requests):\n", "    print(f\"Before: {r['request_id']}; After: {responses[0].request_id}\")\n", "    comment = TARGET_REQUESTS[r[\"request_id\"]]\n", "    print(f\"Comment: {comment}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
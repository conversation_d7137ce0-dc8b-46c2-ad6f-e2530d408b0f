import dataclasses
import json
from pathlib import Path
from typing import Literal

import torch

from base.fastforward.checkpoints import save_load
from base.fastforward.layers import SplitHeadModes
from base.fastforward.starcoder.model_specs import get_starcoder2_model_spec
from research.fastbackward.checkpointing.utils import (
    merge_model_parallel_consolidated_checkpoints,
)
from research.fastbackward.model import ModelArgs
from research.tools.ckp_converter.fbw2ffw_starcoder2 import convert_weights


def convert_starcoder2_ff(
    fb_checkpoint_path: Path,
    output_path: Path,
    model_size: Literal["3b", "7b", "15b", "16b"],
):
    """Convert from FB to FF for Starcoder2."""
    params = json.loads((fb_checkpoint_path / "params.json").read_text("utf-8"))
    model_args = ModelArgs.load_from_dict(params)
    model_name = f"starcoder2-{model_size}"
    model_spec = get_starcoder2_model_spec(
        model_name=model_name, checkpoint_path=output_path
    )
    model_spec.vocab_size = model_args.vocab_size
    model_spec.max_position_embeddings = model_args.max_seq_len
    model_spec.unscaled_max_position_embeddings = model_args.max_seq_len
    model_spec.rotary_scaling_factor = 1.0

    shards: list[dict] = []
    shard_paths = sorted(fb_checkpoint_path.glob("consolidated.*.pth"))
    for shard_path in shard_paths:
        print(f"Loading {shard_path}")
        shard = torch.load(shard_path, map_location="cpu")
        shards.append(shard)

    if not shards:
        raise ValueError(f"No checkpoints found under {fb_checkpoint_path}")
    if len(shards) == 1:
        merged = shards[0]
    else:
        merged = merge_model_parallel_consolidated_checkpoints(model_args, shards)
    torch.cuda.empty_cache()

    merged = convert_weights(model_args, merged)

    manifest = save_load.save_weights(output_path, merged)
    model_spec.checkpoint_sha256 = manifest.manifest_sha256.hex()

    class CustomEncoder(json.JSONEncoder):
        def default(self, o):
            if isinstance(o, SplitHeadModes):
                return o.value
            return super().default(o)

    params_dict = json.dumps(
        dataclasses.asdict(model_spec), cls=CustomEncoder, indent=2, sort_keys=True
    )
    (output_path / "params.json").write_text(params_dict, "utf8")
    print(f"Converted checkpoint saved to {output_path}")


if __name__ == "__main__":
    checkpoints_dir = Path("/mnt/efs/augment/checkpoints/next-edit-gen/")
    convert_starcoder2_ff(
        # checkpoints_dir
        # / "S1.13.1,R1.4_ravenr,P1.14,starcoder2_15b",  # S1.3_keep_most-R1.0-P1.3-100K_repos-starcoder2_15b-mp2",
        # checkpoints_dir / "S1.13.1,R1.4_ravenr,P1.14,starcoder2_15b-ffwd",
        checkpoints_dir
        / "S23-R4_ravenR-K128-P18_star2_diff12_seq12k-pr_grouped_10k-starcoder2_15b-mp2",
        checkpoints_dir
        / "S23-R4_ravenR-K128-P18_star2_diff12_seq12k-pr_grouped_10k-starcoder2_15b-ffwd",
        model_size="15b",
    )

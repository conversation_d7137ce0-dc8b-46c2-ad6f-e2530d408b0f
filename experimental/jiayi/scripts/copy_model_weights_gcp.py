from pathlib import Path
import subprocess


def copy_to_gcp(model_path: Path):
    cw_ip = "jiayi-homepod.gcp-us1.r.augmentcode.com"
    command = f"rsync -e 'ssh -p 22022' -avhP augment@{cw_ip}:{model_path} {model_path.parent}"
    print(command)
    subprocess.run(command, shell=True)


if __name__ == "__main__":
    copy_to_gcp(
        Path(
            "/mnt/efs/augment/checkpoints/next-edit-gen/"
            "S27-R4_ethanol-P18_star2_diff12_seq12k-pr_grouped_10k-starcoder2_3b-ffw-fp8"
        )
    )

{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.core.constants import AUGMENT_ROOT\n", "from research.next_edits.smart_chunking import SmartChunker\n", "\n", "# First, let's take a look at what the chunks look like.\n", "example_file = AUGMENT_ROOT / \"research/next_edits/edit_gen_stages.py\"\n", "SmartChunker(900).print_split(example_file.read_text(), \"Python\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from typing import Sequence\n", "\n", "import tree_sitter as ts\n", "from tqdm import tqdm\n", "\n", "from base.languages.language_guesser import guess_language\n", "from base.ranges.range_types import ByteRange\n", "from base.static_analysis.parsing import TsParsedFile\n", "from research.fim.fim_sampling import _get_nodes_in_brange\n", "\n", "\n", "def compute_avg_chunks_quality(files: Sequence[TsParsedFile], chunker: SmartChunker):\n", "    \"\"\"Compute the quality of the split chunks.\"\"\"\n", "    biggest_node_sizes = list[int]()\n", "    for file in tqdm(files, desc=\"Computing chunks quality\", smoothing=0):\n", "        bmap = file.bmap\n", "        for chunk in chunker.split_chunks(file.code, guess_language(file.path)):\n", "            brange = bmap.crange_to_brange(chunk.crange())\n", "            biggest = get_biggest_node_in_brange(file.ts_tree.root_node, brange)\n", "            biggest_node_sizes.append(biggest)\n", "    return sum(biggest_node_sizes) / len(biggest_node_sizes)\n", "\n", "\n", "def get_biggest_node_in_brange(ts_node: ts.<PERSON><PERSON>, brange: ByteRange) -> int:\n", "    \"\"\"Return the size of the biggest node (in bytes) in the given byte range.\"\"\"\n", "    nodes = _get_nodes_in_brange(ts_node, brange)\n", "    if not nodes:\n", "        return 0\n", "    node_sizes = [n.end_byte - n.start_byte for n in nodes]\n", "    return max(node_sizes)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["test_files = list(AUGMENT_ROOT.glob(\"**/*.py\"))\n", "test_files += list(AUGMENT_ROOT.glob(\"**/*.go\"))\n", "test_files += list(AUGMENT_ROOT.glob(\"**/*.cpp\"))\n", "test_files += list(AUGMENT_ROOT.glob(\"**/*.ts\"))\n", "test_files += list(AUGMENT_ROOT.glob(\"**/*.rs\"))\n", "test_files += list(AUGMENT_ROOT.glob(\"**/*.java\"))\n", "\n", "test_files = [\n", "    path\n", "    for path in test_files\n", "    if \"experimental\" not in str(path)  # exclude experimental\n", "    and path.is_file()\n", "    and len(path.read_text()) > 100  # exclude files that are too short\n", "]\n", "print(f\"Total files: {len(test_files)}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.static_analysis.common import guess_lang_from_fp\n", "\n", "parsed_files = list[TsParsedFile]()\n", "for file in test_files:\n", "    lang = guess_lang_from_fp(file)\n", "    if lang:\n", "        parsed_files.append(TsParsedFile.parse(file, lang, file.read_text()))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["chunker = SmartChunker(2000)\n", "python_pfiles = [pfile for pfile in parsed_files if pfile.lang == \"python\"]\n", "non_python_pfiles = [pfile for pfile in parsed_files if pfile.lang != \"python\"]\n", "print(\"Python:\", compute_avg_chunks_quality(python_pfiles, chunker))\n", "print(\"Non-Python:\", compute_avg_chunks_quality(non_python_pfiles, chunker))"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.core.constants import AUGMENT_CHECKPOINTS_ROOT\n", "\n", "checkpoint_path = AUGMENT_CHECKPOINTS_ROOT / \"starcoderbase-3b_neox/checkpoint\"\n", "model_parallel = 1\n", "save_path = AUGMENT_CHECKPOINTS_ROOT / \"starcoderbase-3b_fb\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.fastbackward import distributed\n", "\n", "distributed.init_distributed_for_training(model_parallel)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.fastbackward.checkpointing.neox import (\n", "    get_starcoder_neox_args,\n", "    load_starcoder_neox_checkpoint,\n", ")\n", "from research.fastbackward.model import Transformer\n", "\n", "model_args = get_starcoder_neox_args(checkpoint_path)\n", "model = Transformer(model_args)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import dataclasses\n", "import json\n", "import subprocess\n", "\n", "import torch\n", "\n", "if save_path.exists():\n", "    raise FileExistsError(f\"Save path already exists: {save_path}\")\n", "save_path.mkdir(parents=True)\n", "\n", "model_args_json = dataclasses.asdict(model_args)\n", "\n", "(save_path / \"params.json\").write_text(json.dumps(model_args_json, indent=2), \"utf8\")\n", "print(f\"model args saved to {save_path / 'params.json'}\")\n", "\n", "for mp_rank in range(model_parallel):\n", "    state_dict = load_starcoder_neox_checkpoint(\n", "        checkpoint_path, mp_size=model_parallel, mp_rank=mp_rank\n", "    )\n", "    model.load_state_dict(state_dict)\n", "    save_file = save_path / f\"consolidated.{mp_rank:02d}.pth\"\n", "    print(f\"Saving model parallel rank {mp_rank} to {save_file}\")\n", "    torch.save(\n", "        model.state_dict(),\n", "        save_file,\n", "    )\n", "\n", "# setting the correct permissions for GCP syncing\n", "subprocess.run([\"chmod\", \"g+w\", str(save_path)])"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}
"""Scripts to run bazel with default args that don't break VSCode."""

import subprocess
import sys


def get_bazel_command(bazel_args: list[str]) -> list[str]:
    """Transforms a bazel command to be compatible with VSCode.

    This adds the `--symlink_prefix=/` flag to the command.
    """
    # find any `--`
    if "--" in bazel_args:
        # split the args before the `--`
        before_dashdash = bazel_args[: bazel_args.index("--")]
        # split the args after the `--`
        after_dashdash = bazel_args[bazel_args.index("--") :]
        # add the args after the `--` to the args before the `--`
        bazel_args = before_dashdash
        pass_on_args = after_dashdash
    else:
        pass_on_args = []

    # run bazel
    new_command = [
        "bazel",
        *bazel_args,
        "--experimental_convenience_symlinks=clean",
        *pass_on_args,
    ]
    return new_command


if __name__ == "__main__":
    args = sys.argv[1:]
    new_command = get_bazel_command(args)
    print("Running transformed command:", new_command)
    sys.exit(subprocess.call(new_command))

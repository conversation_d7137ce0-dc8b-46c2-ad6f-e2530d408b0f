#!/usr/bin/env python3
"""
Test script for the get_relative_time function.
"""

import datetime
from commit_retrieval_server import get_relative_time

def test_relative_time():
    """Test the get_relative_time function with various date differences."""
    # Define a fixed "now" date for consistent testing
    now = datetime.datetime(2023, 5, 15, 12, 0, 0)
    
    # Test cases with different time differences
    test_cases = [
        # (date_str, expected_output)
        ("2023-05-15 11:59:30", "just now"),  # 30 seconds ago
        ("2023-05-15 11:30:00", "30 minutes ago"),  # 30 minutes ago
        ("2023-05-15 08:00:00", "4 hours ago"),  # 4 hours ago
        ("2023-05-14 12:00:00", "yesterday"),  # 1 day ago
        ("2023-05-10 12:00:00", "5 days ago"),  # 5 days ago
        ("2023-04-15 12:00:00", "1 month ago"),  # 1 month ago
        ("2023-01-15 12:00:00", "4 months ago"),  # 4 months ago
        ("2022-05-15 12:00:00", "1 year ago"),  # 1 year ago
        ("2021-05-15 12:00:00", "2 years ago"),  # 2 years ago
        ("invalid-date", "invalid-date"),  # Invalid date format
    ]
    
    # Run tests
    for date_str, expected in test_cases:
        result = get_relative_time(date_str, now)
        print(f"Date: {date_str}, Expected: {expected}, Got: {result}")
        assert result == expected, f"Expected '{expected}' but got '{result}'"
    
    # Test with current time (should use the current time)
    current_date = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    result = get_relative_time(current_date)
    print(f"Current date test: {current_date}, Got: {result}")
    assert result == "just now", f"Expected 'just now' but got '{result}'"
    
    print("All tests passed!")

if __name__ == "__main__":
    test_relative_time()

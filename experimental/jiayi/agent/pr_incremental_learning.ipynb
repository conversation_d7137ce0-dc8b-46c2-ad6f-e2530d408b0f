{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# PR Incremental Learning Summarization\n", "\n", "This notebook experiments with using LLMs to incrementally learn from PRs in chronological order, building up knowledge about the codebase over time."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import logging\n", "\n", "# Import our PR fetcher\n", "from experimental.jiayi.agent.commit_fetcher import (\n", "    get_main_branch_commits_data,\n", ")\n", "\n", "\n", "# Set up logging\n", "logging.basicConfig(level=logging.INFO)\n", "logger = logging.getLogger(__name__)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Set Up LLM Client"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.llm_apis.llm_client import AnthropicDirectClient, TextPrompt\n", "\n", "# Initialize the client with the latest Claude model\n", "client = AnthropicDirectClient(\n", "    model_name=\"claude-3-7-sonnet-20250219\",  # Latest Claude 3.7 Sonnet model\n", "    max_retries=2,\n", "    use_caching=True,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Fetch Recent Commits in Chronological Order"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.core.constants import AUGMENT_ROOT\n", "\n", "\n", "n_commits_to_analyze = 100\n", "\n", "# Get recent commits from main branch\n", "recent_commits = get_main_branch_commits_data(\n", "    count=n_commits_to_analyze, author=\"<PERSON><PERSON><PERSON>\", project_root=AUGMENT_ROOT\n", ")\n", "recent_commits.reverse()\n", "\n", "# Display them\n", "for i, commit in enumerate(reversed(recent_commits)):\n", "    print(f\"{i+1}. {commit.hash} - {commit.subject} (by {commit.author})\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Run Incremental Learning on Recent PRs"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Process the PRs incrementally\n", "from experimental.jiayi.agent.commit_summarizer import summarize_commits_incrementally\n", "from research.core.utils import pickle_dump\n", "\n", "\n", "results, final_learnings = summarize_commits_incrementally(recent_commits, client)\n", "\n", "pickle_dump(results, \"commit_incremental_learnings/results.pkl\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. <PERSON><PERSON><PERSON> Results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load and display incremental results\n", "from experimental.jiayi.agent.commit_summarizer import load_incremental_results\n", "from collections import Counter\n", "\n", "incremental_results = load_incremental_results()\n", "learnings_counter = Counter[int]()\n", "\n", "if incremental_results:\n", "    for i, result in enumerate(reversed(incremental_results)):\n", "        print(\"~=\" * 50)\n", "        print(f\"[{i+1}] Title: {result['title']}\\n\")\n", "        for j, learning in enumerate(result[\"new_learnings\"]):\n", "            print(f\"{j+1}. {learning}\")\n", "        learnings_counter[len(result[\"new_learnings\"])] += 1\n", "else:\n", "    print(\"No results found. Run the incremental processing first.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for num_learning, freq in learnings_counter.most_common(n=10):\n", "    print(f\"{num_learning} learnings: {freq} times\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Analyze Usefulness"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "\n", "from tqdm import tqdm\n", "from experimental.jiayi.agent.commit_summarizer import (\n", "    UsefulnessResult,\n", "    load_incremental_results,\n", "    are_learnings_useful,\n", ")\n", "import shutil\n", "\n", "incremental_results = load_incremental_results()\n", "output_path = Path(\"usefulness_analysis\")\n", "if output_path.exists():\n", "    shutil.rmtree(output_path)\n", "output_path.mkdir(parents=True)\n", "usefulness_results = list[UsefulnessResult]()\n", "uSefulness_tasks = list(zip(incremental_results, recent_commits))\n", "\n", "for i, (result, commit) in enumerate(tqdm(uSefulness_tasks, smoothing=0)):\n", "    usefulness_result = are_learnings_useful(\n", "        commit, result[\"existing_learnings\"], client\n", "    )\n", "    usefulness_results.append(usefulness_result)\n", "    # Write the prompt to an individual file\n", "    commit_file = output_path / f\"{i}_commit_{commit.hash[:8]}_user_prompt.txt\"\n", "    with open(commit_file, \"w\") as f:\n", "        f.write(\n", "            usefulness_result.user_prompt\n", "            + \"\\n<response>\\n\"\n", "            + usefulness_result.response\n", "        )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["useful_ratio = sum(1 for r in usefulness_results if r.useful_learnings) / len(usefulness_results)\n", "print(f\"Useful ratio: {useful_ratio:.2f}\")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
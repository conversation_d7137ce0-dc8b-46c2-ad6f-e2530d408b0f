{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["# Hello World Program in Python\n", "\n", "```python\n", "# Simple Hello World program\n", "\n", "def main():\n", "    print(\"Hello, <PERSON>!\")\n", "\n", "if __name__ == \"__main__\":\n", "    main()\n", "```\n", "\n", "This program defines a `main()` function that prints \"Hello, World!\" to the console. The `if __name__ == \"__main__\":` line ensures that the `main()` function is only called when the script is run directly (not when imported as a module).\n", "\n", "To run this program:\n", "1. Save it to a file with a `.py` extension (e.g., `hello_world.py`)\n", "2. Execute it using Python from your terminal or command prompt:\n", "   ```\n", "   python hello_world.py\n", "   ```\n"]}], "source": ["from research.llm_apis.llm_client import AnthropicDirectClient, TextPrompt\n", "\n", "# Initialize the client\n", "client = AnthropicDirectClient(\n", "    model_name=\"claude-3-7-sonnet-latest\",\n", "    max_retries=2,\n", "    use_caching=True\n", ")\n", "\n", "# Create a message\n", "messages = [[TextPrompt(text=\"Write a hello world program in Python.\")]]\n", "\n", "# Generate a response\n", "response, metadata = client.generate(\n", "    messages=messages,\n", "    max_tokens=1024,\n", "    temperature=0.7,\n", "    system_prompt=\"You are a helpful coding assistant.\"  # Optional\n", ")\n", "\n", "# Print the response\n", "for block in response:\n", "    if hasattr(block, 'text'):\n", "        print(block.text)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
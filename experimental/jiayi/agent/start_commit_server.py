#!/usr/bin/env python3
"""
Script to start the commit retrieval server.
"""

import argparse
import logging
import socket
from experimental.jiayi.agent import commit_retrieval_server
from aiohttp import web

from research.model_server.server_logging import configure_server_logging

# Set up logging
logger = logging.getLogger(__name__)


def main():
    configure_server_logging("INFO")
    parser = argparse.ArgumentParser(description="Start the commit retrieval server")
    parser.add_argument("--host", default="0.0.0.0", help="Host to bind to")
    parser.add_argument("--port", type=int, default=5050, help="Port to bind to")
    parser.add_argument(
        "--commits-to-index",
        type=int,
        default=10_000,
        help="Number of commits to index",
    )
    args = parser.parse_args()

    # Get the internal IP address
    internal_ip = socket.gethostbyname(socket.gethostname())

    logger.info(f"Starting commit retrieval server on {args.host}:{args.port}")
    logger.info(
        f"Access the server at http://{internal_ip}:{args.port} from other machines in the same cloud"
    )

    commit_retrieval_server.COMMITS_TO_INDEX = args.commits_to_index

    app = commit_retrieval_server.create_app()
    web.run_app(app, host=args.host, port=args.port)


if __name__ == "__main__":
    main()

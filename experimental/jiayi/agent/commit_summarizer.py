from dataclasses import dataclass
import logging
import shutil
from base.ranges.string_utils import shorten_str
from research.core.utils import pickle_load
from research.llm_apis.llm_client import LL<PERSON>lient, TextPrompt
from pathlib import Path
from tqdm import tqdm
from typing import Sequence, TypedDict
from pyrsistent import <PERSON><PERSON><PERSON> as PyrVector, pvector as pyr_vector

# Import our commit fetcher
from experimental.jiayi.agent.commit_fetcher import CommitData, FileChange


logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


NO_LEARNING_PHRASE = "No learnings identified."


def format_commit_diffs(files_changed: Sequence[FileChange], max_diff_chars: int):
    # Process each file's diff individually with per-file truncation
    processed_diffs = list[str]()

    for i, file_change in enumerate(files_changed):
        # Skip showing diffs for entirely deleted files
        if file_change.filename_status.startswith("D "):
            processed_diffs.append(
                f"--- {file_change.filename_status} ---\n[Deleted file diff skipped]"
            )
            continue

        # Apply truncation per file
        file_diff = file_change.diff
        if not file_diff.strip():
            print(f"  Warning: Empty diff for file {file_change.file_path}")

        if len(file_diff) > max_diff_chars:
            truncated_file_diff = (
                file_diff[:max_diff_chars] + "\n[... File diff truncated ...]"
            )
            processed_diffs.append(
                f"--- {file_change.filename_status} ---\n{truncated_file_diff}"
            )
        else:
            processed_diffs.append(
                f"--- {file_change.filename_status} ---\n{file_diff}"
            )

    # Join all processed diffs
    all_diffs = "\n\n".join(processed_diffs)
    return all_diffs


def format_commit(
    commit: CommitData,
    max_diff_chars_per_file: int = 2_000,
    max_total_diff_chars: int = 20_000,
) -> str:
    """Format a commit into a standardized string representation.

    Args:
        commit: The CommitData object to format
        max_diff_chars_per_file: Maximum characters of diff to include per file
        max_total_diff_chars: Maximum total characters of diff to include

    Returns:
        A formatted string representation of the commit
    """
    # Get the commit description
    description = shorten_str(commit.description, max_len=1000, omit_mode="right")

    # Process diffs with per-file truncation
    all_diffs = format_commit_diffs(commit.files_changed, max_diff_chars_per_file)

    # Apply total diff truncation if needed
    if len(all_diffs) > max_total_diff_chars:
        all_diffs = (
            all_diffs[:max_total_diff_chars] + "\n[... Total diff truncated ...]"
        )

    # Format the list of files changed
    files_status = "\n".join(f.filename_status for f in commit.files_changed)

    # Format the commit into a standardized representation
    return f"""\
Message: {commit.title}
{description}

**Files Changed:**
{files_status}

{all_diffs}
"""


@dataclass
class TaskSummaryResult:
    user_prompt: str
    task_summary: str


def summarize_commit_task(
    commit: CommitData,
    client: LLMClient,
    max_tokens: int = 512,
    max_diff_chars_per_file: int = 2_000,
    max_total_diff_chars: int = 20_000,
) -> TaskSummaryResult:
    """Generate a few sentences to summarize the task performed in the commit.

    These summaries will be used to determine whether this commit is relevant to any
    future task. The summaries are optimized for RAG retrieval to match user requests
    with relevant past commits.

    Args:
        commit: The CommitData object to analyze.
        client: The LLM client to use.
        max_tokens: Maximum tokens for the response.
        max_diff_chars_per_file: Maximum characters of diff to include per file.
        max_total_diff_chars: Maximum total characters of diff to include.

    Returns:
        A TaskSummaryResult object containing the user prompt, model response, and
        the task summary.
    """
    # --- System Prompt ---
    system_prompt = """\
You are an expert AI assistant specialized in analyzing code changes (Commits) to create concise,
information-rich summaries that will be used in a Retrieval-Augmented Generation (RAG) system.

Your task is to analyze the commit data and generate a concise summary of the changes or tasks performed.
This summary will be indexed and used to retrieve relevant past commits when users ask questions about
the codebase or request help with similar tasks.

**Instructions:**
1. Create a comprehensive summary that captures:
   * The primary purpose/goal of the changes (if obvious from the provided info)
   * Key components or systems that were modified
   * Specific functionality that was added, modified, or fixed
2. Include the most important technical terms, function names, class names, and concepts that would
   make this commit retrievable when users ask about similar tasks or components.
3. *Do not* list the list of file changes in your summary.
4. Make the summary 1-5 sentences long, information-dense.
5. Focus on making the summary USEFUL FOR RETRIEVAL--it should match when users ask about:
   * Similar tasks they want to accomplish
   * Where specific functionality lives in the codebase
   * Understanding the purpose of specific components
"""

    # --- User Prompt ---
    # Format the commit using the format_commit function
    formatted_commit = format_commit(
        commit,
        max_diff_chars_per_file=max_diff_chars_per_file,
        max_total_diff_chars=max_total_diff_chars,
    )

    user_prompt = f"""\
Analyze the following commit data and generate a detailed task summary as instructed.
Directly start your response with the summary and *nothing else*.

**Input Commit Data:**

{formatted_commit}
"""

    # Create a message and generate response
    messages = [[TextPrompt(text=user_prompt)]]

    response, _ = client.generate(
        messages=messages,
        max_tokens=max_tokens,
        temperature=0.0,
        system_prompt=system_prompt,
    )

    # Extract the text from the response
    task_summary = ""
    for block in response:
        if text := getattr(block, "text", None):
            task_summary += text

    task_summary = task_summary.strip()

    return TaskSummaryResult(user_prompt, task_summary)


@dataclass
class LearningSummaryResult:
    user_prompt: str
    response: str
    new_learnings: Sequence[str]


def summarize_commit_incrementally(
    commit: CommitData,
    existing_learnings: Sequence[str],
    client: LLMClient,
    max_tokens: int = 1024,
    max_diff_chars: int = 2_000,  # Limit per file, not total
) -> LearningSummaryResult:
    """
    Generate concise, HIGH-LEVEL new/revised codebase learnings from a commit,
    focusing on navigation, structure, and core concepts, considering
    previously accumulated learnings.

    Args:
        commit: The CommitInfo object to analyze.
        existing_learnings: A list of strings containing learnings from previous commits.
        client: The LLM client to use.
        max_tokens: Maximum tokens for the response.
        max_diff_chars: Maximum characters of diff to include per file.

    Returns:
        A SummaryResult object containing the user prompt, model response, and a list of
        new or revised HIGH-LEVEL learnings identified from this commit. The new_learnings
        field will be an empty list if no learnings were found.
    """

    # --- System Prompt (REVISED FOR HIGH-LEVEL FOCUS) ---
    system_prompt = f"""\
You are an expert AI assistant specialized in analyzing code changes (Commits) to build a **high-level map** of a codebase. You will be provided with existing general learnings accumulated from previous commits (sorted chronologically).

Your goal is to analyze the *current* commit data and identify **only new or significantly revised HIGH-LEVEL, NAVIGATIONAL learnings**, if any. These learnings should help a future developer or AI agent quickly understand **WHERE** core functionalities reside, **HOW** major components are structured or interact, and **WHAT** key architectural patterns or concepts are used. Focus on information useful for orientation and finding relevant code later.

**Instructions:**
1.  **Analyze Existing Learnings:** Review the provided 'Existing Codebase Learnings'.
2.  **Analyze Current Commit:** Analyze the 'Input Commit Data' (Title, Description, Files Changed, Diff).
3.  **Identify *New High-Level* Learnings:** Extract insights from the current commit that reveal *new*:
    *   **Locations of Core Functionality:** (e.g., "Authentication logic is in `src/auth/`", "API route definitions are in `app/routes/`").
    *   **Architectural Patterns:** (e.g., "Uses a service layer pattern in `app/services/`", "Event-driven architecture for background tasks", "Dependency injection is used for database connections").
    *   **Major Component Interactions/Data Flow:** (e.g., "Requests flow from Controller -> Service -> Repository", "Caching layer sits in front of the database").
    *   **Key Concepts/Abstractions:** (e.g., "Uses `Workflow` objects to manage multi-step processes", "Employs a `Plugin` system for extensibility").
    *   **Module Responsibilities:** (e.g., "`utils/` contains common helper functions", "`core/` holds primary business logic").
4.  **Identify *High-Level Revisions/Invalidations*:** If the current commit introduces information that *significantly contradicts or updates* a previous high-level learning (e.g., a core module is moved, a major pattern changes), formulate a *new* learning point reflecting this. Indicate it's a revision if helpful (e.g., "REVISED: Authentication logic moved to `core/auth/`").
5.  **Be Concise & Navigational:** Output should be brief and focused on providing orientation. Think: "Would this help someone new find the right area of the code or understand the overall design?"
6.  **AVOID Low-Level Details:** Do **NOT** include:
    *   Specific implementation details *within* a function (e.g., how a loop works, specific algorithms unless they define a major component).
    *   Exact constant values (e.g., timeouts, thresholds, specific error codes) unless they represent a fundamental configuration principle.
    *   Minor API signature changes (e.g., adding an optional parameter) unless it reflects a broader pattern shift.
    *   Internal variable names, specific counter names, or minor helper functions.
    *   Simple bug fixes or refactors that don't change the overall structure or location of logic.
7.  **Avoid Redundancy:** Do *not* repeat high-level information already present in 'Existing Codebase Learnings' unless it's part of a significant revision. If the current commit does *not* introduce any significant *new high-level* learnings that are likely helpful for orientation, output the exact phrase:
    `{NO_LEARNING_PHRASE}`
9.  **Output Format:** Provide *only* the extracted *new or revised high-level* learnings as bullet points OR the exact phrase `{NO_LEARNING_PHRASE}`. Do not include any introductory text, concluding remarks, or conversational filler.
"""

    # --- User Prompt (Slightly adjusted final line) ---
    description = shorten_str(commit.description, max_len=1000, omit_mode="right")
    description_section = (
        f"<description>\n{description or '[No description provided]'}\n</description>"
    )

    all_diffs = format_commit_diffs(commit.files_changed, max_diff_chars)
    diff_section = f"<diff>\n```diff\n{all_diffs}\n```\n</diff>"

    # Format existing learnings as bullet points
    if existing_learnings:
        formatted_existing_learnings = "\n".join(
            [f"- {learning}" for learning in existing_learnings]
        )
    else:
        formatted_existing_learnings = "[No existing learnings provided yet.]"

    existing_learnings_section = f"""
**Existing Codebase Learnings:**
<learnings>
{formatted_existing_learnings}
</learnings>
"""

    user_prompt = f"""
{existing_learnings_section}

---

Analyze the following commit data based on the instructions provided in the system prompt. Extract **only new or revised HIGH-LEVEL, NAVIGATIONAL** learnings about the codebase. Be extremely selective in what you consider a new learning.

**Input Commit Data:**

**Title:** {commit.title}
**Author:** {commit.author}

**Files Changed:**

{description_section}

{diff_section}

**High-Level New/Revised Learnings:**
"""

    # Create a message
    messages = [[TextPrompt(text=user_prompt)]]

    response, _ = client.generate(
        messages=messages,
        max_tokens=max_tokens,
        temperature=0.0,
        system_prompt=system_prompt,
    )

    # Extract the text from the response
    new_summary = ""
    for block in response:
        if text := getattr(block, "text", None):
            new_summary += text

    new_summary = new_summary.strip()

    # Handle the specific "no new learnings" case
    if NO_LEARNING_PHRASE in new_summary:
        learnings = []
        logger.info("No new learnings identified.")
    elif not new_summary:
        # this case will be treated as no new learnings
        logger.warning(
            f"Commit {commit.hash[:8]}: LLM returned empty response, treating as no new learnings."
        )
        learnings = []
    else:
        # Process the model output into a list of learnings by bullet points
        # Split by common bullet point markers and filter out empty lines
        bullet_points = []
        for line in new_summary.split("\n"):
            line = line.strip()
            # Skip empty lines
            if not line:
                continue
            # Remove bullet point markers (-, *, •) and trim
            if line.startswith(("-", "*", "•")):
                bullet_points.append(line[1:].strip())
            else:
                # If no bullet point marker, add as is (might be a continuation)
                bullet_points.append(line)

        learnings = bullet_points

    return LearningSummaryResult(user_prompt, new_summary, learnings)


class CommitSummaryResultDict(TypedDict):
    """TypedDict representing a commit summary result for serialization."""

    commit_hash: str
    title: str
    author: str
    new_learnings: Sequence[str]
    existing_learnings: Sequence[str]


def summarize_commits_incrementally(
    commits: Sequence[CommitData], client, output_dir="commit_incremental_learnings"
) -> tuple[list[CommitSummaryResultDict], Sequence[str]]:
    """
    Process commits incrementally, building up knowledge over time.

    Args:
        commits: List of commit dictionaries to process in chronological order
        client: The LLM client to use
        output_dir: Directory to save the results

    Returns:
        Tuple of (results list of SummaryResultDict, final accumulated learnings as a list of strings)
    """
    results = []
    accumulated_learnings = (
        pyr_vector()
    )  # Now a list of strings instead of a single string

    # Create output directory if it doesn't exist
    output_path = Path(output_dir)
    if output_path.exists():
        shutil.rmtree(output_path)
    output_path.mkdir(exist_ok=True, parents=True)

    for i, commit_info in enumerate(
        tqdm(commits, smoothing=0, desc="Summarizing commits")
    ):
        try:
            summary_result = summarize_commit_incrementally(
                commit_info, accumulated_learnings, client
            )
            new_learnings = summary_result.new_learnings

            # Create result entry
            result: CommitSummaryResultDict = {
                "commit_hash": commit_info.hash,
                "title": commit_info.title,
                "author": commit_info.author,
                "new_learnings": new_learnings,  # This is a list of strings
                "existing_learnings": accumulated_learnings,
            }

            # Add to results
            results.append(result)

            # Only update accumulated learnings if we got new information
            if new_learnings:
                # Add new learnings to the accumulated list
                accumulated_learnings = accumulated_learnings.extend(new_learnings)

            # Write the prompt to an individual file
            commit_file = (
                output_path / f"{i}_commit_{commit_info.hash[:8]}_user_prompt.txt"
            )
            with open(commit_file, "w") as f:
                f.write(
                    summary_result.user_prompt
                    + "\n<response>\n"
                    + summary_result.response
                )

        except Exception as e:
            logger.error(
                f"Error processing commit {commit_info.hash}: {str(e)}", exc_info=True
            )

    return results, accumulated_learnings


def load_incremental_results(
    output_dir: str | Path = "commit_incremental_learnings",
) -> list[CommitSummaryResultDict]:
    """
    Load incremental commit learning results from an output directory.

    Args:
        output_dir: Directory containing the saved results

    Returns:
        List of CommitSummaryResultDict objects loaded from the summary file
    """
    output_path = Path(output_dir)
    summary_file = output_path / "results.pkl"

    if not summary_file.exists():
        return []

    results = pickle_load(summary_file, list[CommitSummaryResultDict])

    return results


@dataclass
class UsefulnessResult:
    user_prompt: str
    response: str
    useful_learnings: list[int]


def are_learnings_useful(
    commit: CommitData,
    existing_learnings: Sequence[str],
    client: LLMClient,
    max_tokens: int = 1024,
    max_diff_chars: int = 2_000,  # Limit per file, not total
) -> UsefulnessResult:
    """
    Determine if the given learnings are useful for writing the commit.

    Args:
        commit: CommitData object representing the commit
        existing_learnings: List of existing learnings
        client: LLMClient object for making API calls
        max_tokens: Maximum tokens for the LLM response
        max_diff_chars: Maximum characters of diff to consider per file

    Returns:
        UsefulnessResult containing the user prompt, response, and indices of essential learnings
    """
    NOT_USEFUL_RESPONSE = "None."

    # System prompt for evaluating usefulness of learnings
    system_prompt = """
You are an expert AI assistant specialized in analyzing code changes (Commits) to determine which existing codebase learnings would have been essential to a developer WHEN WRITING this commit.

Your task is to analyze the commit data and identify which of the provided existing learnings would have been essential to the developer DURING THE DEVELOPMENT of this specific change. Focus on learnings that would have directly helped the developer understand the components, architecture, or patterns they were modifying WHILE IMPLEMENTING this change.
A learning is only considered essential if (1) it's not directly clear from reading the changed files but (2) it's crucial to correctly predict the changes made in the commit.
"""

    # Process diffs similar to summarize_commit_incrementally
    description = shorten_str(commit.description, max_len=1000, omit_mode="right")
    description_section = (
        f"<description>\n{description or '[No description provided]'}\n</description>"
    )

    all_diffs = format_commit_diffs(commit.files_changed, max_diff_chars)
    diff_section = f"<diff>\n```diff\n{all_diffs}\n```\n</diff>"

    # Format existing learnings as numbered list
    if existing_learnings:
        formatted_existing_learnings = "\n".join(
            [f"{i}: {learning}" for i, learning in enumerate(existing_learnings)]
        )
    else:
        formatted_existing_learnings = "[No existing learnings provided yet.]"

    existing_learnings_section = f"""
**Existing Codebase Learnings:**
<learnings>
{formatted_existing_learnings}
</learnings>
"""

    user_prompt = f"""
{existing_learnings_section}

---

**Input Commit Data:**

**Title:** {commit.title}
**Author:** {commit.author}

**Files Changed:**

{description_section}

{diff_section}

---
**Instructions:**
1. Analyze the 'Input Commit Data' (Title, Description, Files Changed, Diff).
2. Review each of the numbered 'Existing Codebase Learnings'.
3. Identify which learnings (by index number) would have been essential to the developer when writing/implementing the commit.
4. Consider: "If the developer had known this learning BEFORE writing this code, would it have made their implementation task significantly easier or more effective?"
5. For each essential learning, provide the index number followed by a brief reason why it would have been essential. Return a bullet list and NOTHING ELSE:
   - <index1>: [explain very briefly why this learning is useful]
   - <index2>: ...
6. If none of the learnings would have been directly useful to the developer when writing this commit, return the exact phrase: '{NOT_USEFUL_RESPONSE}'
"""

    # Create a message
    messages = [[TextPrompt(text=user_prompt)]]

    response, _ = client.generate(
        messages=messages,
        max_tokens=max_tokens,
        temperature=0.0,
        system_prompt=system_prompt,
    )

    # Extract the text from the response
    result_text = ""
    for block in response:
        if text := getattr(block, "text", None):
            result_text += text

    result_text = result_text.strip()

    # Parse the response to get indices
    if not result_text:
        logger.warning(
            f"Commit {commit.hash[:8]}: LLM returned empty response, treating as no useful learnings."
        )
        useful_indices = []
    elif result_text == NOT_USEFUL_RESPONSE:
        useful_indices = []
    else:
        result_lines = result_text.split("\n")
        useful_indices = []
        try:
            for line in result_lines:
                line = line.strip()
                if not line:
                    continue
                if line.startswith("-"):
                    # Extract the index number from lines like "- 3: reason..."
                    parts = line.strip().split(":", 1)
                    if len(parts) >= 1:
                        # Extract just the number from the first part
                        index_part = parts[0].lstrip("-").strip()
                        if index_part.isdigit():
                            useful_indices.append(int(index_part))
                else:
                    logger.warning(
                        f"Commit {commit.hash[:8]}: Unexpected line format in response: {line}"
                    )
        except Exception as e:
            logger.error(
                f"Error parsing useful learning indices from '{result_text}': {str(e)}"
            )
            useful_indices = []

    return UsefulnessResult(
        user_prompt=user_prompt, response=result_text, useful_learnings=useful_indices
    )

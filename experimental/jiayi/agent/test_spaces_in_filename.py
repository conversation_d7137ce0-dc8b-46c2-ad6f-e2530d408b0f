"""
Test script to verify that the commit_fetcher can handle filenames with spaces.
"""

from commit_fetcher import parse_git_diff_output


def test_parse_git_diff_output():
    """Test that parse_git_diff_output can handle filenames with spaces."""
    # Create a sample git diff output with spaces in filenames
    diff_output = """diff --git a/file with spaces.txt b/file with spaces.txt
index 1234567..abcdef0 100644
--- a/file with spaces.txt
+++ b/file with spaces.txt
@@ -1 +1,2 @@
 This is a test file with spaces in the name.
+Added a second line.
diff --git a/another file with spaces.md b/another file with spaces.md
new file mode 100644
index 0000000..1234567
--- /dev/null
+++ b/another file with spaces.md
@@ -0,0 +1 @@
+New file content
"""

    # Parse the diff output
    file_diffs = parse_git_diff_output(diff_output)

    # Assert that both files were correctly parsed
    assert "file with spaces.txt" in file_diffs, f"'file with spaces.txt' not found in parsed diffs! Files found: {list(file_diffs.keys())}"
    assert "another file with spaces.md" in file_diffs, f"'another file with spaces.md' not found in parsed diffs! Files found: {list(file_diffs.keys())}"
    
    # Verify the content of the diffs
    assert "Added a second line" in file_diffs["file with spaces.txt"]
    assert "New file content" in file_diffs["another file with spaces.md"]

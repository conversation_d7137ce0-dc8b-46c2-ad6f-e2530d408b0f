{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from tqdm import tqdm\n", "from experimental.jiayi.agent.commit_fetcher import (\n", "    get_main_branch_commits_data\n", ")\n", "from research.core.constants import AUGMENT_ROOT\n", "\n", "\n", "n_commits_to_analyze = 500\n", "\n", "# Get recent commits from main branch\n", "commits_iter = get_main_branch_commits_data(\n", "    count=n_commits_to_analyze, author=None, project_root=AUGMENT_ROOT\n", ")\n", "recent_commits = list(tqdm(commits_iter, total=n_commits_to_analyze))\n", "hash_to_commit = {commit.hash: commit for commit in recent_commits}\n", "print(f\"A total of {len(hash_to_commit)} commits are fetched.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Display them\n", "for i, commit in enumerate(recent_commits):\n", "    print(f\"{i+1}. {commit.hash} - {commit.subject} (by {commit.author}, {commit.date_str})\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["commit = hash_to_commit[\"a14c9a91e416bb40add23502e7a0ebcbd59ff1b3\"]\n", "print(str(commit))\n", "for file_change in commit.files_changed:\n", "    print(file_change.filename_status)\n", "    print(file_change.diff)\n", "    print()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["notes = [\n", "    {\n", "        \"commit\": \"ef9621ec136db190d6e49e99b10cd38a553b89c9\", # 37\n", "        \"notes\": \"the version bumping change can benefit from past learnings.\"\n", "    },\n", "    {\n", "        \"commit\": \"f5b59b1f4e92c7afe8c577b3a1cff44a794fd428\", # 39\n", "        \"notes\": \"also benefit from knowing to bump the version in changelog. benefit from seeing 654c48705514fde631cdff20870037468c0da81e.\" # 118\n", "    },\n", "    {\n", "        \"commit\": \"9987d1a5586730f8a23ec8427078a97d86acb611\", # 40\n", "        \"notes\": \"benefit from 15729ba9d10ffa248818b3de01948640d172cf23 and its learning.\" # 54\n", "    },\n", "    {\n", "        \"commit\": \"0ef9a4ce282151b6e97d00ba231bb0100de8a60b\", # 43\n", "        \"notes\": \"benefit from 61dd5cd8e8581b7e36752ea63be86763968e2ba0 and its learning.\" # 67\n", "    },\n", "]"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
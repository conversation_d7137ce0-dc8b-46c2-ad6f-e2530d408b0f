{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from tqdm import tqdm\n", "from experimental.jiayi.agent.commit_fetcher import get_main_branch_commits_data\n", "from research.core.constants import AUGMENT_ROOT\n", "\n", "\n", "n_commits_to_index = 10_000\n", "\n", "# Get recent commits from main branch\n", "recent_commits = list(\n", "    tqdm(\n", "        get_main_branch_commits_data(\n", "            count=n_commits_to_index,\n", "            # author=\"<PERSON><PERSON><PERSON>\",\n", "            project_root=AUGMENT_ROOT,\n", "        ),\n", "        smoothing=0,\n", "        total=n_commits_to_index,\n", "        desc=\"Fetching commits\",\n", "    )\n", ")\n", "print(\"Oldest commit date:\", recent_commits[-1].date_str)\n", "print(\"Newest commit date:\", recent_commits[0].date_str)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "from experimental.jiayi.agent.commit_fetcher import CommitData\n", "from experimental.jiayi.agent.commit_summarizer import summarize_commit_task\n", "from research.llm_apis.llm_client import (\n", "    AnthropicDirectClient,\n", "    GeminiVertexClient,\n", "    OpenAIDirectClient,\n", ")\n", "import json\n", "from dataclasses import asdict\n", "\n", "summary_version = 3\n", "# v1: Uses claude-3-7-sonnet-20250219\n", "# v2: Uses gemini-2.0-flash\n", "# v3: Uses gemini-2.0-flash. Append file status to the indexed document.\n", "\n", "\n", "# client = AnthropicDirectClient(\n", "#     model_name=\"claude-3-5-haiku-20241022\",\n", "#     max_retries=2,\n", "#     use_caching=True,\n", "# )\n", "\n", "# client = OpenAIDirectClient(\n", "#     model_name=\"gpt-4.1-mini-2025-04-14\",\n", "#     max_retries=2,\n", "#     cot_model=False,\n", "# )\n", "\n", "client = GeminiVertexClient(\n", "    model_name=\"gemini-2.0-flash\",\n", ")\n", "\n", "# Create cache directory if it doesn't exist\n", "CACHE_DIR = Path(f\"~/caches/commit_summary_cache/v{summary_version}\").expanduser()\n", "CACHE_DIR.mkdir(exist_ok=True, parents=True)\n", "\n", "\n", "def summarize_commit(commit: CommitData) -> str:\n", "    # Create a cache file path based on commit hash\n", "    cache_file = CACHE_DIR / f\"{commit.hash}.json\"\n", "\n", "    # Check if cache file exists\n", "    if cache_file.exists():\n", "        try:\n", "            with open(cache_file, \"r\") as f:\n", "                cached_data = json.load(f)\n", "                return cached_data[\"task_summary\"]\n", "        except (j<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>) as e:\n", "            print(f\"Error reading cache file: {e}\")\n", "\n", "    # If not cached or error reading cache, generate summary\n", "    result = summarize_commit_task(commit, client)\n", "\n", "    # Cache the result\n", "    with open(cache_file, \"w\") as f:\n", "        json.dump(asdict(result), f, indent=2)\n", "\n", "    return result.task_summary"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from experimental.jiayi.agent.commit_retriever import (\n", "    CommitRetriever,\n", "    get_lastest_chatanol_config,\n", ")\n", "\n", "# 1. Create a Chatanol retriever configuration\n", "config = get_lastest_chatanol_config()\n", "\n", "# 2. Create the CommitRetriever with the configuration\n", "print(\"Initializing CommitRetriever...\")\n", "commit_retriever = CommitRetriever(config, summarize_commit, num_threads=10)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Index all recent commits\n", "\n", "commit_retriever.add_commits(recent_commits)\n", "\n", "# compute some stats about the indexed document\n", "all_docs = commit_retriever.database.get_docs(commit_retriever.database.get_doc_ids())\n", "print(f\"Indexed {len(all_docs)} documents\")\n", "average_doc_length = sum(len(doc.text) for doc in all_docs) / len(all_docs)\n", "print(f\"Average document length: {average_doc_length:.2f} characters\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 4. Query for commits relevant to a task\n", "import textwrap\n", "from experimental.jiayi.agent.commit_retriever import format_commit_contents\n", "\n", "\n", "task_description = \"How to deploy a new next edit location model?\"\n", "# task_description = \"Add a new tool use feature flag to the agent.\"\n", "# task_description = \"How to add a feature flag to control whether a button shows up in VSCode?\"\n", "# task_description = \"How to add a chat history node?\"\n", "\n", "\n", "print(f\"Querying for commits relevant to: '{task_description}'\")\n", "\n", "relevant_commits = commit_retriever.retrieve_commits(task_description, top_k=10)\n", "\n", "for i, (commit, summary, score) in enumerate(relevant_commits):\n", "    print(\"~=\" * 10 + f\" Commit {i+1}, score={score:.4f} \" + \"~=\" * 10)\n", "    print(str(commit))\n", "    summary = summary.split(\"Files changed:\")[0]  # don't repeat\n", "    print(\"Summary:\")\n", "    print(\"\\n\".join(textwrap.wrap(summary, width=90)))\n", "    # print(summary)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
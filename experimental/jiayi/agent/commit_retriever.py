#!/usr/bin/env python3
"""
Example of using the Chatanol dense retriever to build an indexer and perform retrieval.
This example shows how to use the YAML configuration approach commonly used in the codebase.
"""

import pathlib
from tqdm import tqdm
from typing import Callable, List, Sequence
import concurrent.futures

# Import necessary components
from experimental.jiayi.agent.commit_fetcher import CommitData
from experimental.jiayi.agent.commit_summarizer import format_commit_diffs
from research.core.types import Document, DocumentId
from research.retrieval.retrieval_database import RetrievalDatabase
from experimental.igor.systems.chatanol import ChatanolRetrieverPromptInput


class CommitRetriever:
    def __init__(
        self,
        chatanol_config: dict,
        summarizer: Callable[[CommitData], str],
        num_threads: int = 8,
    ):
        """Initialize the CommitRetriever with a Chatanol configuration.

        Args:
            chatanol_config: Configuration dictionary for the Chatanol retriever
        """
        # Build a retrieval database from the config
        self.database = RetrievalDatabase.from_yaml_config(chatanol_config)
        # Load the model
        self.database.scorer.load()
        # Store commits by their document ID for later retrieval
        self.commits = dict[DocumentId, CommitData]()
        self.commit_hash_to_doc_id = dict[str, DocumentId]()
        self.summarizer = summarizer
        self.num_threads = num_threads

    def add_commits(self, commits: Sequence[CommitData]):
        """Add commits to the retrieval database.

        This method converts each CommitData object into a Document using the
        format_commit function, then adds it to the retrieval database.

        Args:
            commits: Sequence of CommitData objects to add
        """
        documents = []

        # Define a helper function for the pool
        def summarize_commit(commit):
            summarized = self.summarizer(commit)
            return commit, summarized

        # Process commits in parallel using a thread pool
        with concurrent.futures.ThreadPoolExecutor(
            max_workers=self.num_threads
        ) as executor:
            futures = [executor.submit(summarize_commit, commit) for commit in commits]
            results = []
            for future in tqdm(
                concurrent.futures.as_completed(futures),
                total=len(commits),
                desc="Summarizing commits",
            ):
                results.append(future.result())

        # Process the results
        for commit, summarized_commit in results:
            # Create a Document object with the formatted commit
            commit: CommitData
            change_list = "\n".join(
                f"{f.filename_status}" for f in commit.files_changed
            )
            doc_text = "Summary:\n" + summarized_commit + "\n\nFiles changed:\n" + change_list
            doc = Document.new(
                text=doc_text,
                path="",  # no file path for commits
            )

            # Add to documents list
            documents.append(doc)

            # Store the commit for later retrieval
            self.commits[doc.id] = commit
            self.commit_hash_to_doc_id[commit.hash] = doc.id

        # Add documents to the retrieval database
        self.database.add_docs(documents)

    def retrieve_commits(
        self, task_description: str, top_k: int
    ) -> Sequence[tuple[CommitData, str, float]]:
        """Retrieve commits relevant to the given task description.

        Args:
            task_description: Natural language description of the task or query

        Returns:
            A list of (commit, summary, score)
        """
        # Create a query input for the retriever
        query_input = ChatanolRetrieverPromptInput(
            prefix="",  # The query goes in the prefix field
            suffix="",
            path="",
            selected_code="",
            message=task_description,
        )

        # Retrieve chunks and scores
        chunks, scores = self.database.query(query_input, top_k=top_k)
        doc_ids = dict[DocumentId, None]()
        commits = list[tuple[CommitData, str, float]]()
        for chunk, score in zip(chunks, scores):
            # only add the commit if we haven't seen the doc id before
            if chunk.parent_doc.id not in doc_ids:
                commit = self.commits[chunk.parent_doc.id]
                summary = chunk.text
                commits.append((commit, summary, score))
                doc_ids[chunk.parent_doc.id] = None

        return commits


def get_lastest_chatanol_config():
    """
    Create a configuration for the Chatanol retriever.

    This uses the latest production configuration based on chatanol1-18-neox.
    """
    return {
        "scorer": {
            "name": "dense_scorer_v2_ffwd",
            # In production, this would be:
            "checkpoint_path": "/mnt/efs/augment/checkpoints/chatanol/chatanol1-18-neox/global_step1468",
            # "cache_dir": "/tmp/augment/chatanol_cache/commit_retrieval_v1",
        },
        "chunker": {
            "name": "smart_line_level",
            "max_chunk_chars": 10_000_000,  # use a super large number to disable chunking
            "max_headers": 0,
        },
        "query_formatter": {
            "name": "base:chatanol6-singleturnisspecial",
            "tokenizer_name": "rogue",
            "max_tokens": 1024,
        },
        "document_formatter": {
            "name": "base:chatanol6-embedding-with-path-key",
            "tokenizer_name": "rogue",
        },
    }


def format_commit_contents(commit: CommitData, max_diff_chars: int = 2000) -> str:
    """Format a given commit as a document string.

    This function formats a CommitData object into a structured document string
    that can be used for retrieval or summarization purposes.

    Args:
        commit: The CommitData object to format

    Returns:
        A formatted string representation of the commit
    """
    # Format the list of files changed
    files_list = "\n".join(f"  {f.filename_status}" for f in commit.files_changed)

    all_diffs = format_commit_diffs(commit.files_changed, max_diff_chars)

    # Format the commit as a document string
    formatted_commit = f"""\
Author: {commit.author_name} <{commit.author_email}>
Date: {commit.date_str}
Subject: {commit.subject}
Body:
{commit.body.strip() or '[Empty]'}

Files changed ({len(commit.files_changed)}):
{files_list}

Diffs:
```diff
{all_diffs}
```
"""

    return formatted_commit


def get_documents(
    directory_path: str, extensions: List[str] = [".py"]
) -> List[Document]:
    """
    Get documents from files in a directory with specified extensions.

    Args:
        directory_path: Path to the directory to scan for files
        extensions: List of file extensions to include

    Returns:
        List of Document objects
    """
    docs = []
    path = pathlib.Path(directory_path)

    for file_path in path.glob("**/*"):
        if file_path.is_file() and file_path.suffix in extensions:
            try:
                # Create a Document object for each file
                doc = Document(
                    text=file_path.read_text(),
                    id=str(file_path),
                    path=str(file_path.relative_to(path)),
                    meta={},
                )
                docs.append(doc)
                print(f"Added document: {doc.path}")
            except Exception as e:
                print(f"Error reading {file_path}: {e}")

    return docs

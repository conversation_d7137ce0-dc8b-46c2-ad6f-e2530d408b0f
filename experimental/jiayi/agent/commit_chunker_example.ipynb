{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from tqdm import tqdm\n", "from experimental.jiayi.agent.commit_fetcher import get_main_branch_commits_data\n", "from research.core.constants import AUGMENT_ROOT\n", "\n", "\n", "n_commits_to_index = 20\n", "\n", "# Get recent commits from main branch\n", "recent_commits = list(\n", "    tqdm(\n", "        get_main_branch_commits_data(\n", "            count=n_commits_to_index,\n", "            # author=\"<PERSON><PERSON><PERSON>\",\n", "            project_root=AUGMENT_ROOT,\n", "        ),\n", "        smoothing=0,\n", "        total=n_commits_to_index,\n", "        desc=\"Fetching commits\",\n", "    )\n", ")\n", "print(\"Oldest commit date:\", recent_commits[-1].date_str)\n", "print(\"Newest commit date:\", recent_commits[0].date_str)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "from base.blob_names.python.blob_names import get_blob_name\n", "from experimental.jiayi.agent.commit_fetcher import CommitData\n", "from models.retrieval.chunking import chunking\n", "\n", "\n", "def commit_to_document(commit: CommitData) -> chunking.Document:\n", "    json_text = json.dumps(commit.to_json(), indent=2)\n", "    path = f\"{commit.hash}.gitcommit\"\n", "    blob_name = get_blob_name(path, json_text)\n", "    return chunking.Document(blob_name=blob_name, text=json_text, path=path)\n", "\n", "\n", "commit_doc = commit_to_document(recent_commits[0])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from models.retrieval.chunking.commit_summary_chunker import CommitSummaryChunker\n", "from google.genai import Client as genai_Client\n", "\n", "# build a google genai client\n", "region=\"us-central1\"\n", "project_id=\"augment-research-gsc\"\n", "client = genai_Client(vertexai=True, project=project_id, location=region)\n", "chunker = CommitSummaryChunker(client=client)\n", "chunks = list(chunker.split_into_chunks(commit_doc))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["chunk = chunks[0]\n", "print(chunk.text)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(chunk.parent_doc.text)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
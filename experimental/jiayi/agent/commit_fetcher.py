"""
Commit Fetcher - Utility to fetch commit information from a git repository.

This module provides functions to:
1. Get detailed information about a commit
2. Get the diff for a commit
3. Get recent commits from the main branch
"""

from pathlib import Path
import subprocess
import re
from typing import Any, Iterable, Literal
from dataclasses import dataclass
import datetime
import warnings

from base.ranges.string_utils import shorten_str


@dataclass
class FileChange:
    """Data class representing a file change in a commit."""

    change_type: Literal["A", "C", "D", "M", "R", "T"]
    """
    git status	Short	What happened
    modified	M	    The contents of a tracked file changed.
    new file	A	    A brand-new file was added and staged.
    deleted	    D	    A previously-tracked file was removed.
    renamed	    R	    The file’s path changed but Git detected it’s the same content.
    copied	    C	    A new path has content identical (or very close) to an existing file, so Git flagged it as a copy.
    typechange 	T	    The “type” flipped—e.g. regular file → symlink, symlink → file, or file → submodule.
    """

    file_path: str
    """The new file path."""

    old_file_path: str | None
    """The old file path."""

    diff: str
    """The diff for this specific file change."""

    @property
    def filename_status(self) -> str:
        """Get the filename status."""
        if self.old_file_path:
            return f"{self.change_type} {self.old_file_path} -> {self.file_path}"
        return f"{self.change_type} {self.file_path}"

    def to_json(self) -> dict[str, Any]:
        """Convert to dictionary."""
        return {
            "changeType": self.change_type,
            "filePath": self.file_path,
            "oldFilePath": self.old_file_path,
            "diff": self.diff,
        }


@dataclass
class CommitData:
    """Data class representing a Git commit."""

    hash: str
    author_name: str
    author_email: str
    timestamp: int
    subject: str
    body: str
    files_changed: list[FileChange]

    def __str__(self) -> str:
        """String representation of the commit."""
        # Format the list of files changed
        files_list_parts = list[str]()
        for f in self.files_changed:
            if f.old_file_path:
                files_list_parts.append(
                    f"  {f.change_type} {f.old_file_path} -> {f.file_path}"
                )
            else:
                files_list_parts.append(f"  {f.change_type} {f.file_path}")
        files_list = "\n".join(files_list_parts)
        body_text = shorten_str(self.body, max_len=1000, omit_mode="right") or "[Empty]"

        return (
            f"Commit: {self.hash}\n"
            f"Author: {self.author_name} <{self.author_email}>\n"
            f"Date: {self.date_str}\n"
            f"Subject: {self.subject}\n"
            f"Body:\n{body_text}\n"
            f"Files changed ({len(self.files_changed)}):\n{files_list}\n"
        )

    @property
    def date_str(self) -> str:
        """Get the date of the commit as a string."""
        return datetime.datetime.fromtimestamp(self.timestamp).strftime(
            "%Y-%m-%d %H:%M:%S"
        )

    def to_json(self) -> dict[str, Any]:
        """Convert to dictionary."""
        return {
            "hash": self.hash,
            "authorName": self.author_name,
            "authorEmail": self.author_email,
            "timestamp": self.timestamp,
            "subject": self.subject,
            "body": self.body,
            "filesChanged": [f.to_json() for f in self.files_changed],
        }

    @property
    def title(self) -> str:
        """Get the title of the commit (same as subject)."""
        return self.subject

    @property
    def description(self) -> str:
        """Get the description of the commit (same as body)."""
        return self.body

    @property
    def author(self) -> str:
        """Get the author of the commit."""
        return self.author_name


def parse_git_diff_output(diff_output: str) -> dict[str, str]:
    """Parse git diff output into a dictionary mapping filenames to their diffs.

    This function handles filenames with spaces correctly by using regex to parse
    the diff headers rather than simple string splitting.

    Args:
        diff_output: The output from a git diff or git show command with patch information

    Returns:
        A dictionary mapping filenames to their diff content
    """
    file_diffs = dict[str, str]()
    current_file = None
    current_diff_lines = list[str]()
    # Regex pattern to extract filenames from git diff headers
    # Format is typically: diff --git a/path/to/file b/path/to/file
    diff_header_pattern = re.compile(r"^diff --git a/(.*) b/(.*)$")

    for line in diff_output.splitlines(keepends=True):
        # Check for diff header lines that indicate a new file
        if line.startswith("diff --git "):
            # Save the previous file's diff if there is one
            if current_file is not None and current_diff_lines:
                file_diffs[current_file] = "".join(current_diff_lines)

            # Extract the new filename from the diff header using regex
            # This handles filenames with spaces correctly
            match = diff_header_pattern.match(line.rstrip())
            if match:
                # Use the b/path/to/file part (second group in the regex)
                current_file = match.group(2)
                current_diff_lines = []
            continue

        # Skip other header lines we don't want in the final diff
        if (
            line.startswith("---")
            or line.startswith("+++")
            or line.startswith("index ")
        ):
            continue

        # Add content lines to the current diff
        if current_file is not None:
            current_diff_lines.append(line)

    # Add the last file's diff
    if current_file is not None and current_diff_lines:
        file_diffs[current_file] = "".join(current_diff_lines)

    return file_diffs


def get_commit_info(project_root: Path, commit_hash: str) -> CommitData:
    """Get detailed information about a commit.

    Args:
        commit_hash: The hash of the commit to get information for.

    Returns:
        A CommitInfo object containing the commit details.

    Raises:
        ValueError: If the commit information cannot be retrieved.
    """
    try:
        # Step 1: Get commit metadata (hash, author, date, subject, body)
        # Using a separate command to get just the commit metadata
        commit_metadata = subprocess.check_output(
            [
                "git",
                "show",
                "--no-patch",  # Don't include patch/diff information
                "--pretty=format:%H%n%an%n%ae%n%at%n%s%n%b",
                commit_hash,
            ],
            universal_newlines=True,
            cwd=project_root,
        ).strip()

        # Parse the metadata
        metadata_lines = commit_metadata.split("\n")
        hash_val = metadata_lines[0]
        author_name = metadata_lines[1]
        author_email = metadata_lines[2]
        author_date = metadata_lines[3]
        subject = metadata_lines[4]

        # The body starts at line 6
        body = "\n".join(metadata_lines[5:]).strip()

        # Step 2: Get file changes using git show with --name-status and empty format
        # This works for all commits including the initial commit
        file_changes_output = subprocess.check_output(
            [
                "git",
                "show",
                "--name-status",
                "--format=",  # Empty format to exclude commit info
                commit_hash,
            ],
            universal_newlines=True,
            cwd=project_root,
        ).strip()

        # Step 3: Get all diffs in a single command
        # This avoids making individual git calls for each file
        all_diffs_output = subprocess.check_output(
            [
                "git",
                "show",
                "--format=",  # Empty format to exclude commit info
                "--patch",  # Include the patch/diff information
                commit_hash,
            ],
            universal_newlines=True,
            cwd=project_root,
        ).strip()

        # Parse the diffs into a dictionary mapping filenames to their diffs
        file_diffs = parse_git_diff_output(all_diffs_output)

        # Parse the file changes and associate with diffs
        files_changed = list[FileChange]()
        for line in file_changes_output.splitlines(keepends=False):
            if line.strip():
                # parts[0] is the status (e.g., "M", "A", "R100")
                # parts[1] is the filename, or old_filename in case of rename/copy
                # parts[2] is new_filename in case of rename/copy
                parts = line.split("\t")
                if not parts:
                    continue

                raw_status = parts[0]
                change_type: Literal["A", "C", "D", "M", "R", "T"] | None = None
                file_path: str | None = None
                old_file_path: str | None = None

                if raw_status.startswith("R"):
                    change_type = "R"
                    if len(parts) >= 3:
                        old_file_path = parts[1]
                        file_path = parts[2]
                    else:  # Should not happen with git diff --name-status
                        warnings.warn(f"Could not parse renamed file line: {line}")
                        continue
                elif raw_status.startswith("C"):
                    change_type = "C"
                    if len(parts) >= 3:
                        old_file_path = parts[1]
                        file_path = parts[2]
                    else:  # Should not happen with git diff --name-status
                        warnings.warn(f"Could not parse copied file line: {line}")
                        continue
                elif raw_status == "A":
                    change_type = "A"
                    if len(parts) >= 2:
                        file_path = parts[1]
                    else:
                        warnings.warn(f"Could not parse added file line: {line}")
                        continue
                elif raw_status == "D":
                    change_type = "D"
                    if len(parts) >= 2:
                        # For deleted files, file_path is the path of the deleted file
                        file_path = parts[1]
                        old_file_path = parts[
                            1
                        ]  # Store it in old_file_path as well for consistency
                    else:
                        warnings.warn(f"Could not parse deleted file line: {line}")
                        continue
                elif raw_status == "M":
                    change_type = "M"
                    if len(parts) >= 2:
                        file_path = parts[1]
                    else:
                        warnings.warn(f"Could not parse modified file line: {line}")
                        continue
                elif raw_status == "T":
                    change_type = "T"
                    if len(parts) >= 2:
                        file_path = parts[1]
                    else:
                        warnings.warn(f"Could not parse typechange file line: {line}")
                        continue
                else:
                    warnings.warn(f"Unknown git status: {raw_status} in line: {line}")
                    continue

                if not file_path:  # Should be caught by earlier checks
                    warnings.warn(f"File path could not be determined for line: {line}")
                    continue

                # For deleted files, the diff might be associated with the old path
                # For other types, it's associated with the new path.
                diff_key = old_file_path if change_type == "D" else file_path

                if diff_key not in file_diffs:
                    # Git show --name-status sometimes shows files that are not in the diff output
                    # e.g. for binary files or submodule changes where no textual diff is generated.
                    # We'll create a FileChange object with an empty diff in such cases.
                    current_diff = ""
                    # warnings.warn(f"Warning: No diff found for file {diff_key} (status: {change_type}). Line: {line}")
                else:
                    current_diff = file_diffs[diff_key]

                files_changed.append(
                    FileChange(
                        change_type=change_type,
                        file_path=file_path,
                        old_file_path=old_file_path,
                        diff=current_diff,
                    )
                )

        return CommitData(
            hash=hash_val,
            author_name=author_name,
            author_email=author_email,
            timestamp=int(author_date),
            subject=subject,
            body=body,
            files_changed=files_changed,
        )

    except subprocess.CalledProcessError as e:
        raise ValueError(
            f"Failed to get information for commit {commit_hash}: {str(e)}"
        )


def get_main_branch_commits_dict(
    project_root: Path, count: int = 10, author: str | None = None
) -> list[dict[str, Any]]:
    """Get recent commits from the main branch.

    Args:
        count: Maximum number of commits to return
        author: If provided, only return commits by this author
    """
    try:
        # Determine the main branch name (main or master)
        branches = (
            subprocess.check_output(
                ["git", "branch"], universal_newlines=True, cwd=project_root
            )
            .strip()
            .split("\n")
        )

        main_branch = None
        for branch in branches:
            branch = branch.strip("* ")
            if branch in ["main", "master"]:
                main_branch = branch
                break

        if not main_branch:
            main_branch = "main"  # Default to main if not found

        # Build git log command
        git_cmd = [
            "git",
            "log",
            f"{main_branch}",
            f"-{count}",
            "--pretty=format:%H|%an|%s",
        ]

        # Add author filter if specified
        if author:
            git_cmd.append(f"--author={author}")

        # Get recent commits
        commits_output = subprocess.check_output(
            git_cmd, universal_newlines=True, cwd=project_root
        ).strip()

        commits = []
        for line in commits_output.split("\n"):
            if line:
                parts = line.split("|", 2)
                if len(parts) == 3:
                    commit_hash, author, subject = parts
                    commits.append(
                        {"hash": commit_hash, "author": author, "subject": subject}
                    )

        return commits

    except subprocess.CalledProcessError as e:
        raise ValueError(f"Failed to get main branch commits: {str(e)}")


def get_main_branch_commits_data(
    project_root: Path, count: int = 10, author: str | None = None
) -> Iterable[CommitData]:
    return (
        get_commit_info(project_root, commit["hash"])
        for commit in get_main_branch_commits_dict(project_root, count, author)
    )

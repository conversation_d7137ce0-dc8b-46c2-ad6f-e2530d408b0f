#!/usr/bin/env python3
"""
Simple async HTTP server for commit retrieval.
Provides an API to search for commits relevant to a query.
"""

import asyncio
from dataclasses import asdict
import datetime
import json
import logging
from pathlib import Path
from typing import Optional

from aiohttp import web
import aiohttp_cors
from tqdm import tqdm

from experimental.jiayi.agent.commit_fetcher import (
    CommitData,
    get_main_branch_commits_data,
    get_commit_info,
)
from experimental.jiayi.agent.commit_retriever import (
    CommitRetriever,
    get_lastest_chatanol_config,
)
from experimental.jiayi.agent.commit_summarizer import (
    summarize_commit_task,
    format_commit,
)
from research.core.constants import AUGMENT_ROOT
from research.llm_apis.llm_client import GeminiVertexClient

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Global retriever instance
global_retriever: Optional[CommitRetriever] = None

summary_version = 3
repo_root = AUGMENT_ROOT
CACHE_DIR = Path(f"~/caches/commit_summary_cache/v{summary_version}").expanduser()
COMMITS_TO_INDEX = 10_000


async def initialize_retriever(n_commits: int) -> None:
    """Initialize the commit retriever with recent commits."""
    global global_retriever

    # Create LLM client for summarization
    client = GeminiVertexClient(model_name="gemini-2.0-flash")

    # Create cache directory
    CACHE_DIR.mkdir(exist_ok=True, parents=True)

    def summarize_commit_cached(commit: CommitData) -> str:
        # Create a cache file path based on commit hash
        cache_file = CACHE_DIR / f"{commit.hash}.json"

        # Check if cache file exists
        if cache_file.exists():
            try:
                with open(cache_file, "r") as f:
                    cached_data = json.load(f)
                    return cached_data["task_summary"]
            except (json.JSONDecodeError, KeyError) as e:
                print(f"Error reading cache file: {e}")

        # If not cached or error reading cache, generate summary
        result = summarize_commit_task(commit, client)

        # Cache the result
        with open(cache_file, "w") as f:
            json.dump(asdict(result), f, indent=2)

        return result.task_summary

    # Create retriever
    config = get_lastest_chatanol_config()
    retriever = CommitRetriever(config, summarize_commit_cached, num_threads=10)

    # Get recent commits
    logger.info(f"Fetching {n_commits} recent commits...")
    commits_iter = get_main_branch_commits_data(count=n_commits, project_root=repo_root)
    recent_commits = list(tqdm(commits_iter, total=n_commits, desc="Fetching commits"))

    # Index commits
    logger.info("Indexing commits...")
    retriever.add_commits(recent_commits)

    # Log stats
    all_docs = retriever.database.get_docs(retriever.database.get_doc_ids())
    logger.info(f"Indexed {len(all_docs)} documents")
    average_doc_length = sum(len(doc.text) for doc in all_docs) / len(all_docs)
    logger.info(f"Average document length: {average_doc_length:.2f} characters")

    # set the global retriever so it's ready to be used
    global_retriever = retriever


async def search_commits(request: web.Request) -> web.Response:
    """Handle commit search requests."""
    global global_retriever

    # Check if retriever is initialized
    if global_retriever is None:
        logger.error("Retriever not initialized")
        return web.json_response({"error": "Retriever not initialized"}, status=500)

    try:
        # Parse request
        data: dict = await request.json()
        query = data.get("query")
        top_k = data.get("top_k", 5)

        if not query:
            logger.error(f"Missing 'query' parameter in request: {data}")
            return web.json_response({"error": "Missing 'query' parameter"}, status=400)

        logger.info("[search_commits] Received query: %s", query)

        # Retrieve relevant commits
        relevant_commits = global_retriever.retrieve_commits(query, top_k=top_k)

        # Format results
        results = list[dict]()
        for commit, summary, score in relevant_commits:
            results.append(
                {
                    "hash": commit.hash,
                    "author": commit.author_name,
                    "date": get_relative_time(commit.date_str),
                    "subject": commit.subject,
                    "body": commit.body,
                    "summary": summary,
                    "score": score,
                }
            )

        return web.json_response({"results": results})

    except Exception as e:
        logger.exception(f"Error processing search_commits request: {e}")
        return web.json_response({"error": str(e)}, status=500)


async def read_commit_detail(request: web.Request) -> web.Response:
    """Read and format a commit.

    Expects a commit SHA in the request parameters.
    Returns a formatted representation of the commit.
    """
    try:
        # Parse request
        data: dict = await request.json()
        commit_sha = data.get("commit_sha")

        if not commit_sha:
            logger.error(f"Missing 'commit_sha' parameter in request: {data}")
            return web.json_response(
                {"error": "Missing 'commit_sha' parameter"}, status=400
            )

        logger.info("[read_commit_detail] Received commit_sha: %s", commit_sha)

        # Get commit info using the function from commit_fetcher
        commit_data = get_commit_info(AUGMENT_ROOT, commit_sha)

        # Prepare the response with only the formatted commit
        response = {"formatted_commit": format_commit(commit_data)}

        return web.json_response(response)

    except Exception as e:
        logger.exception("Error processing request")
        return web.json_response({"error": str(e)}, status=500)


async def health_check(_request: web.Request) -> web.Response:
    """Simple health check endpoint."""
    global global_retriever
    status = "ready" if global_retriever is not None else "initializing"
    return web.json_response({"status": status})


async def start_background_tasks(app: web.Application) -> None:
    """Start background tasks when the app starts."""
    app["retriever_task"] = asyncio.create_task(initialize_retriever(COMMITS_TO_INDEX))


async def cleanup_background_tasks(app: web.Application) -> None:
    """Clean up background tasks when the app shuts down."""
    app["retriever_task"].cancel()
    try:
        await app["retriever_task"]
    except asyncio.CancelledError:
        pass


def create_app() -> web.Application:
    """Create and configure the web application."""
    app = web.Application()

    # Set up routes
    app.add_routes(
        [
            web.get("/health", health_check),
            web.post("/search", search_commits),
            web.post("/read_commit", read_commit_detail),
        ]
    )

    # Set up CORS
    cors = aiohttp_cors.setup(
        app,
        defaults={
            "*": aiohttp_cors.ResourceOptions(
                allow_credentials=True,
                expose_headers="*",
                allow_headers="*",
            )
        },
    )

    # Configure CORS for all routes
    for route in list(app.router.routes()):
        cors.add(route)

    # Set up background tasks
    app.on_startup.append(start_background_tasks)
    app.on_cleanup.append(cleanup_background_tasks)

    return app


def get_relative_time(date_str: str, now: Optional[datetime.datetime] = None) -> str:
    """Convert a date string to a relative time format (e.g., '2 days ago', 'today').

    Args:
        date_str: Date string in format '%Y-%m-%d %H:%M:%S'
        now: Optional datetime object representing the current time.
             If not provided, the current time will be used.

    Returns:
        A string representing the relative time
    """
    try:
        # Parse the date string
        date = datetime.datetime.strptime(date_str, "%Y-%m-%d %H:%M:%S")

        # Use provided time or get current time
        if now is None:
            now = datetime.datetime.now()

        # Calculate the difference
        diff = now - date
        diff_days = diff.days

        # Format based on the difference
        if diff_days == 0:
            # Check if it's within the last hour
            diff_seconds = diff.total_seconds()
            if diff_seconds < 60:
                return "just now"
            elif diff_seconds < 3600:
                return f"{int(diff_seconds // 60)} minutes ago"
            else:
                return f"{diff_seconds / 3600:.1f} hours ago"
        else:
            # Return fractional days for more accuracy
            total_days = diff.total_seconds() / (24 * 3600)
            return f"{total_days:.1f} days ago"
    except Exception as e:
        # Log the error and return the original date string if parsing fails
        logger.exception(f"Error parsing date string '{date_str}': {e}")
        return date_str

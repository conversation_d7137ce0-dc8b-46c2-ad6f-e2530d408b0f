{"include": ["research/"], "exclude": ["research/data/", "research/gpt-neox/", "research/eval/code_exec_eval/", "research/eval/dataset_generation_lib/", "research/eval/edit/experimental_edit_systems.py", "research/eval/edit/visualize_result.py", "research/eval/generation/execution.py", "research/eval/hydra/", "research/eval/vulcan/testdata/", "research/retrieval/legacy_retrieval_implementations/bm25/", "research/fastbackward/checkpointing/", "research/static_analysis/tests/testdata/", "research/fim/tests/testdata/"], "strict": [], "ignore": [], "executionEnvironments": [{"root": ".", "extraPaths": [".", "research/gpt-neox"]}], "typeCheckingMode": "standard", "reportIncompatibleVariableOverride": "none", "reportIncompatibleMethodOverride": "none", "reportFunctionMemberAccess": "none", "reportPrivateImportUsage": "none", "pythonVersion": "3.11"}
/**
 * NOTE: If this file is modified, you MUST run `pnpm install -r` in order to
 * update the checksum in `pnpm-lock.yaml` or else tests will fail.
 *
 * pnpm and npm handle dependencies differently. Due to the way node_modules is
 * structured in npm, some dependencies may not properly list all of their
 * dependencies in their package.json. This causes us to not install all of the
 * nested dependencies using pnpm.
 *
 * To remedy this, this file contains a hook that is run after our package.json
 * is parsed. The hook injects missing dependencies into the package.json of our
 * dependencies so that we are able to install them.
 *
 * If a package you need to install has trouble resolving dependencies, add them
 * to this hook. It is recommended that you dig through their dependency tree to
 * find the exact version that their dependency uses.
 *
 * To read more about this hook:
 * https://pnpm.io/pnpmfile#hooksreadpackagepkg-context-pkg--promisepkg
 *
 * To read more about the context:
 * https://github.com/augmentcode/augment/pull/14716
 */

function readPackage(pkg, context) {
  if (pkg.name === "@vscode/webview-ui-toolkit") {
    pkg.dependencies = {
      ...pkg.dependencies,
      "@microsoft/fast-web-utilities": "^5.4.1",
    };
    context.log("@vscode/webview-ui-toolkit: add missing dependencies");
  }

  if (pkg.name === "langium") {
    pkg.dependencies = {
      ...pkg.dependencies,
      "vscode-languageserver-types": "3.17.5",
      "vscode-jsonrpc": "8.2.1",
      "@chevrotain/regexp-to-ast": "11.0.3",
    };
    context.log("Langium: add missing dependencies");
  }

  if (pkg.name === "simple-git") {
    pkg.dependencies = {
      ...pkg.dependencies,
      "supports-color": "^9.4.0",
    };
    context.log("simple-git: add missing dependencies");
  }

  return pkg;
}

module.exports = {
  hooks: {
    readPackage,
  },
};
